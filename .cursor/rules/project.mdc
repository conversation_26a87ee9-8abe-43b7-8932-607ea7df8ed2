---
alwaysApply: true
---

# Workspace Context for MaidProfit/JobON Projects

## Project Overview

This workspace contains three interconnected projects that form a comprehensive platform for connecting customers with service providers:

1. **job-on** - A Laravel-based backend system handling core business logic, user management, and service provider operations
2. **JobON.app-Web** - A React-based frontend application providing user interfaces for customers, providers, and administrators
3. **maidprofit-be** - A NestJS-based backend system handling advanced features, real-time communications, and specialized business operations

## Technology Stack Analysis

### job-on (Laravel PHP Backend)

- **Framework**: Laravel 11.x
- **Language**: PHP 8.1+
- **Database**: MySQL/MariaDB with MongoDB integration
- **Key Libraries**:
  - Laravel Sanctum for authentication
  - <PERSON><PERSON> Media Library for file handling
  - <PERSON><PERSON> Permissions for role-based access control
  - Guzzle HTTP client for API communications
  - Stripe PHP SDK for payment processing
  - Google API Client for integration with Google services
  - Yajra DataTables for server-side data handling
- **Architecture**: Traditional MVC with repository pattern, API resources, and modular structure using Laravel Modules

### JobON.app-Web (React TypeScript Frontend)

- **Framework**: React 18 with TypeScript
- **State Management**: React hooks and context API
- **Routing**: React Router DOM v6
- **UI Components**: Shadcn/ui, Radix UI primitives
- **Styling**: Tailwind CSS with custom themes
- **Key Libraries**:
  - React Hook Form for form handling
  - Zod for validation
  - React Query for server state management
  - Socket.io-client for real-time communications
  - Mapbox GL for mapping functionality
  - Stripe.js for payment processing
  - React PDF for document generation
- **Architecture**: Component-based with role-specific routing (Customer, Provider, Admin)

### maidprofit-be (NestJS TypeScript Backend)

- **Framework**: NestJS 10.x
- **Language**: TypeScript
- **Database**: MongoDB with Mongoose ODM
- **Real-time**: Socket.io for WebSocket communications
- **Key Libraries**:
  - Mongoose for MongoDB object modeling
  - Stripe SDK for payment processing
  - Google APIs for integration
  - Twilio for SMS communications
  - SendGrid for email services
  - Redis for caching and session management
  - AWS SDK for S3 storage
- **Architecture**: Modular with dependency injection, microservices-ready design

## Project Architecture & Flow

### Data Flow Overview

1. **Frontend (React)**: Users interact with the web application through role-specific interfaces
2. **Primary API (Laravel)**: Handles core business operations, user authentication, and service management
3. **Secondary API (NestJS)**: Manages specialized features, real-time communications, and advanced business logic
4. **Database Layer**: MySQL for structured data, MongoDB for flexible document storage, Redis for caching
5. **External Services**: Stripe for payments, Twilio for communications, Google APIs for maps and authentication

### Core Domain Entities

- **Users**: Customers, Service Providers, and Administrators with role-based permissions
- **Services**: Service categories, packages, and availability management
- **Jobs/Bookings**: Job creation, bidding, scheduling, and completion tracking
- **Payments**: Invoicing, transactions, subscriptions, and billing management
- **Communications**: Messaging, notifications, and real-time chat functionality
- **Businesses**: Provider business profiles, certifications, and performance metrics

## Database Design

### Laravel MySQL Schema

- **Users Table**: Core user information with polymorphic relationships for different user types
- **Services/Service Requests**: Service categories, packages, and customer requests
- **Jobs/Job Bookings**: Job posting, provider assignment, and status tracking
- **Bookings**: Customer bookings with scheduling and payment information
- **Providers**: Service provider profiles, availability, and performance metrics
- **Transactions**: Payment records, invoices, and financial tracking
- **Reviews/Ratings**: Customer feedback and provider ratings system

### NestJS MongoDB Schema

- **Workspaces**: Multi-tenancy support for different business entities
- **Clients**: Customer profiles and preferences
- **Jobs**: Advanced job tracking with complex scheduling
- **Quotes/Invoices**: Document generation and management
- **Booking Requests**: Detailed booking workflows with addons and extras
- **Notifications**: Real-time messaging and alert system
- **Media**: File storage and management with AWS S3 integration

## API Structure

### Laravel API Endpoints

- **Authentication**: User registration, login, social authentication, password reset
- **User Management**: Profile updates, address management, document uploads
- **Service Operations**: Service browsing, category management, package details
- **Booking System**: Job creation, scheduling, payment processing
- **Provider Features**: Service management, availability settings, bid handling
- **Admin Functions**: System configuration, user management, reporting

### NestJS API Endpoints

- **Workspace Management**: Multi-tenancy operations and business configuration
- **Client Operations**: Advanced customer management and preferences
- **Job Handling**: Complex job workflows with real-time status updates
- **Booking Features**: Detailed booking processes with addons and frequency options
- **Document Management**: Quote and invoice generation with template systems
- **Communication**: Real-time messaging and notification services

## Security Implementation

### Authentication & Authorization

- **Laravel**: Sanctum token-based authentication with role-based permissions
- **NestJS**: JWT authentication with custom guards and role-based access control
- **React**: Secure token storage and role-based route protection

### Data Protection

- **Encryption**: Password hashing with bcrypt, sensitive data encryption
- **Input Validation**: Comprehensive validation on both frontend and backend
- **API Security**: Rate limiting, CORS policies, and secure headers
- **File Security**: Secure file uploads with validation and access controls

## Development Workflow & Standards

### Code Quality

- **Laravel**: PSR-12 coding standards, PHPStan for static analysis
- **NestJS**: TypeScript with strict typing, ESLint for code quality
- **React**: TypeScript with strict mode, ESLint and Prettier for formatting

### Testing

- **Laravel**: PHPUnit for unit and feature testing
- **NestJS**: Jest for unit and integration testing
- **React**: Jest and React Testing Library for component testing

### CI/CD

- **Deployment**: Docker-based containerization with Docker Compose
- **Environment Management**: Dotenv configuration with environment-specific settings
- **Monitoring**: Logging and error tracking with integrated solutions

## Integration Points

### Payment Processing

- Stripe integration across both backend systems for consistent payment handling
- Subscription management and recurring billing features

### Communication Services

- Twilio for SMS notifications and communications
- Email services through SendGrid and custom SMTP configurations
- Real-time messaging with Socket.io integration

### External APIs

- Google Maps for location services and geocoding
- Social authentication through Google, Facebook, and other providers
- Business data enhancement through external data sources

## Performance Optimization

### Caching Strategy

- Redis for session storage and application caching
- Database query optimization with indexing and eager loading
- Frontend caching with React Query and service workers

### Scalability Features

- Database connection pooling and query optimization
- Horizontal scaling support through microservices architecture
- Load balancing considerations for high-traffic scenarios

## Deployment Architecture

### Infrastructure

- Docker containers for consistent deployment environments
- Environment-specific configuration management
- Database migration and seeding processes

### Monitoring & Logging

- Application performance monitoring
- Error tracking and alerting systems
- Database performance optimization

This workspace context provides a comprehensive understanding of the interconnected systems that power the MaidProfit/JobON platform, enabling efficient development, maintenance, and enhancement of the service ecosystem.
