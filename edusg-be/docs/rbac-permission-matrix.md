# Role-Based Access Control (RBAC) Permission Matrix

## Overview

This document defines the comprehensive permission matrix for the EduSG backend application, specifying which user roles can access which endpoints and under what conditions.

## Role Hierarchy

The system implements a role hierarchy where higher-level roles inherit permissions from lower-level roles:

```
ADMIN > SCHOOL_MANAGER > TEACHER > STUDENT
```

- **ADMIN**: Full system access, can manage all resources across all schools
- **SCHOOL_MANAGER**: Can manage users and resources within their assigned school only
- **TEACHER**: Can access educational content and manage their classes within their school
- **STUDENT**: Can access their own profile and educational content assigned to them

## Permission Matrix

### Authentication Controller (`/auth`)

| Endpoint | Method | ADMIN | SCHOOL_MANAGER | TEACHER | STUDENT | Notes |
|----------|--------|-------|----------------|---------|---------|-------|
| `/auth/login` | POST | ✅ | ✅ | ✅ | ✅ | Public endpoint |
| `/auth/register` | POST | ✅ | ✅ | ✅ | ✅ | Public endpoint |

### User Controller (`/user`)

| Endpoint | Method | ADMIN | SCHOOL_MANAGER | TEACHER | STUDENT | Notes |
|----------|--------|-------|----------------|---------|---------|-------|
| `/user` | POST | ✅ | ✅* | ❌ | ❌ | *School managers can only create users for their school |
| `/user` | GET | ✅ | ✅* | ❌ | ❌ | *School managers can only see users from their school |
| `/user/me` | GET | ✅ | ✅ | ✅ | ✅ | All authenticated users can access their own profile |
| `/user/:id` | GET | ✅ | ✅* | ❌ | ❌ | *School managers can only access users from their school |
| `/user/:id` | PATCH | ✅ | ✅* | ❌ | ❌ | *School managers can only update users from their school |

### School Controller (`/schools`)

| Endpoint | Method | ADMIN | SCHOOL_MANAGER | TEACHER | STUDENT | Notes |
|----------|--------|-------|----------------|---------|---------|-------|
| `/schools` | POST | ✅ | ❌ | ❌ | ❌ | Public endpoint for school creation |
| `/schools` | GET | ✅ | ❌ | ❌ | ❌ | Public endpoint |
| `/schools/:id` | GET | ✅ | ✅* | ✅* | ❌ | *Non-admin users can only access their own school |
| `/schools/:id` | PATCH | ✅ | ✅* | ❌ | ❌ | *School managers can only update their own school |
| `/schools/:id` | DELETE | ✅ | ❌ | ❌ | ❌ | Admin only |
| `/schools/examination-format` | POST | ✅ | ✅* | ❌ | ❌ | *School managers can only upload for their school |
| `/schools/:schoolId/examination-format` | GET | ✅ | ❌ | ❌ | ❌ | Admin only |
| `/schools/:schoolId/examination-format` | DELETE | ✅ | ❌ | ❌ | ❌ | Admin only |
| `/schools/:schoolId/narrative-structure/extract` | POST | ✅ | ❌ | ❌ | ❌ | Admin only |
| `/schools/narrative-structure/extract-all` | POST | ✅ | ❌ | ❌ | ❌ | Admin only |
| `/schools/:schoolId/narrative-structure` | GET | ✅ | ❌ | ❌ | ❌ | Admin only |
| `/schools/:schoolId/narrative-structure` | DELETE | ✅ | ❌ | ❌ | ❌ | Admin only |

### Monitoring Controller (`/admin/monitoring`)

| Endpoint | Method | ADMIN | SCHOOL_MANAGER | TEACHER | STUDENT | Notes |
|----------|--------|-------|----------------|---------|---------|-------|
| `/admin/monitoring/dashboard` | GET | ✅ | ❌ | ❌ | ❌ | Admin only |
| `/admin/monitoring/pool-utilization` | GET | ✅ | ❌ | ❌ | ❌ | Admin only |
| `/admin/monitoring/question-reuse` | GET | ✅ | ❌ | ❌ | ❌ | Admin only |
| `/admin/monitoring/generation-times` | GET | ✅ | ❌ | ❌ | ❌ | Admin only |
| `/admin/monitoring/validation-metrics` | GET | ✅ | ❌ | ❌ | ❌ | Admin only |
| `/admin/monitoring/cache-metrics` | GET | ✅ | ❌ | ❌ | ❌ | Admin only |
| `/admin/monitoring/events` | GET | ✅ | ❌ | ❌ | ❌ | Admin only |

## Business Logic Rules

### School Manager Restrictions

1. **School Scope**: School managers can only access and manage resources within their assigned school
2. **User Management**: 
   - Can create TEACHER and STUDENT users only
   - Cannot create ADMIN or SCHOOL_MANAGER users
   - Cannot transfer users between schools
3. **School Management**: Can only update their own school's information
4. **Examination Formats**: Can only upload examination formats for their own school

### Teacher Restrictions

1. **School Access**: Can only access their own school's information
2. **Profile Management**: Can only access and update their own profile via `/user/me`

### Student Restrictions

1. **Profile Management**: Can only access and update their own profile via `/user/me`
2. **Limited Access**: Cannot access other users or school management features

## Error Handling

### HTTP Status Codes

- **401 Unauthorized**: Missing or invalid authentication token
- **403 Forbidden**: Valid authentication but insufficient permissions
- **404 Not Found**: Resource not found or user doesn't have access to it

### Standardized Error Messages

- `"Forbidden"`: Generic message for role-based access denial
- `"You do not have access to this school"`: School ownership violation
- `"You can only access users from your own school"`: School manager scope violation
- `"School manager must be assigned to a school"`: Missing school assignment

## Implementation Notes

1. **Role Hierarchy**: ADMIN role has access to all resources regardless of other restrictions
2. **Public Endpoints**: Some endpoints are marked as `@Public()` and bypass authentication entirely
3. **Business Logic**: Role-based restrictions are enforced at the controller level, with additional business logic for ownership validation
4. **Consistent Guards**: All protected endpoints use `@UseGuards(AuthGuard, RoleGuard)` pattern
5. **Decorator Usage**: `@Roles()` decorator specifies allowed roles for each endpoint

## Future Considerations

1. **Resource-Level Permissions**: Consider implementing more granular permissions for specific resources
2. **Dynamic Permissions**: Potential for database-driven permission configuration
3. **Audit Logging**: Track permission-based access attempts for security monitoring
4. **Role Extensions**: Possibility of adding more specialized roles (e.g., CONTENT_MANAGER, SYSTEM_SUPPORT)
