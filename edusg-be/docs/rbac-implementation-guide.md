# RBAC Implementation Guide

## Quick Reference

### Role Capabilities Summary

| Capability | ADMIN | SCHOOL_MANAGER | TEACHER | STUDENT |
|------------|-------|----------------|---------|---------|
| Manage all schools | ✅ | ❌ | ❌ | ❌ |
| Manage own school | ✅ | ✅ | ❌ | ❌ |
| View own school info | ✅ | ✅ | ✅ | ❌ |
| Create users (any school) | ✅ | ❌ | ❌ | ❌ |
| Create users (own school) | ✅ | ✅* | ❌ | ❌ |
| View all users | ✅ | ❌ | ❌ | ❌ |
| View school users | ✅ | ✅ | ❌ | ❌ |
| Update any user | ✅ | ❌ | ❌ | ❌ |
| Update school users | ✅ | ✅ | ❌ | ❌ |
| View own profile | ✅ | ✅ | ✅ | ✅ |
| System monitoring | ✅ | ❌ | ❌ | ❌ |
| Examination formats | ✅ | ✅* | ❌ | ❌ |
| Narrative structures | ✅ | ❌ | ❌ | ❌ |

*Limited to own school only

## Implementation Patterns

### Controller Setup Pattern

```typescript
@Controller('resource')
@UseGuards(AuthGuard, RoleGuard)
@ApiBearerAuth()
export class ResourceController {
  // Controller implementation
}
```

### Endpoint Protection Patterns

#### Admin Only
```typescript
@Get('admin-resource')
@Roles(EUserRole.ADMIN)
@ApiOperation({ summary: 'Admin only endpoint' })
async adminOnlyEndpoint() {
  // Implementation
}
```

#### Admin + School Manager
```typescript
@Get('school-resource')
@Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
@ApiOperation({ summary: 'Admin or School Manager endpoint' })
async schoolResourceEndpoint(@Req() req) {
  // Business logic for school manager restrictions
  if (req.user.role === EUserRole.SCHOOL_MANAGER) {
    // Validate school ownership
  }
  // Implementation
}
```

#### All Authenticated Users
```typescript
@Get('profile')
@ApiOperation({ summary: 'All authenticated users' })
async profileEndpoint() {
  // No @Roles() decorator = all authenticated users
}
```

### Business Logic Patterns

#### School Ownership Validation
```typescript
// For School Managers - validate they can only access their school
if (req.user.role === EUserRole.SCHOOL_MANAGER) {
  if (!req.user.schoolId) {
    throw new ForbiddenException('School manager must be assigned to a school');
  }
  if (resourceSchoolId !== req.user.schoolId) {
    throw new ForbiddenException('You can only access resources from your own school');
  }
}
```

#### User Creation Restrictions
```typescript
// School managers cannot create admin or school manager users
if (req.user.role === EUserRole.SCHOOL_MANAGER) {
  if (createUserDto.role === EUserRole.ADMIN || createUserDto.role === EUserRole.SCHOOL_MANAGER) {
    throw new ForbiddenException('You cannot create admin or school manager users');
  }
}
```

## Error Handling Standards

### Standard Exception Types
- `ForbiddenException`: For role-based access violations
- `UnauthorizedException`: For authentication failures
- `NotFoundException`: For resources that don't exist or user can't access

### Error Message Guidelines
- Keep messages user-friendly but not revealing internal system details
- Use consistent language across similar violations
- Provide actionable feedback when possible

## Testing Checklist

### For Each Protected Endpoint

- [ ] Test with ADMIN role (should have access)
- [ ] Test with appropriate roles (should have access)
- [ ] Test with inappropriate roles (should get 403)
- [ ] Test without authentication (should get 401)
- [ ] Test business logic restrictions (school ownership, etc.)

### Integration Test Examples

```typescript
describe('UserController RBAC', () => {
  it('should allow admin to access all users', async () => {
    // Test implementation
  });
  
  it('should allow school manager to access only their school users', async () => {
    // Test implementation
  });
  
  it('should deny teacher access to user management', async () => {
    // Test implementation
  });
});
```

## Maintenance Guidelines

### Adding New Endpoints
1. Add appropriate `@Roles()` decorator
2. Implement business logic for school managers if applicable
3. Update permission matrix documentation
4. Add integration tests
5. Update API documentation

### Modifying Existing Permissions
1. Review impact on existing functionality
2. Update permission matrix
3. Update tests
4. Consider backward compatibility
5. Document changes

### Role Changes
1. Update `EUserRole` enum if needed
2. Update all affected controllers
3. Update permission matrix
4. Update database migrations if needed
5. Update tests and documentation

## Security Considerations

1. **Principle of Least Privilege**: Users should have minimum permissions needed
2. **Defense in Depth**: Multiple layers of validation (RBAC + business logic)
3. **Audit Trail**: Consider logging access attempts for security monitoring
4. **Regular Review**: Periodically review and update permissions
5. **Testing**: Comprehensive testing of all permission scenarios
