# AddQuestionToWorksheetDto Database Option Fields

## Overview

The `AddQuestionToWorksheetDto` has been enhanced to include database option type and option value fields, allowing questions to be associated with specific option configurations from the database.

## Interface Structure

```typescript
interface AddQuestionToWorksheetDto {
  // Question Content (Required)
  type: 'multiple_choice' | 'true_false' | 'fill_in_the_blank' | 'short_answer' | 'essay' | 'matching' | 'ordering' | 'calculation' | 'diagram' | 'long_answer';
  content: string;
  options: string[];
  answer: string[];
  explain: string;
  
  // Database Option References (Optional)
  optionTypeId?: string;  // UUID reference to OptionType entity
  optionValueId?: string; // UUID reference to OptionValue entity
  
  // Worksheet-specific fields (Optional)
  position?: number;
  points?: number;
  
  // Additional fields inherited from CreateExerciseQuestionDto...
}
```

## New Fields

### optionTypeId (Optional)
- **Type**: `string` (UUID)
- **Description**: Database option type ID for categorizing the question context
- **Example**: `'123e4567-e89b-12d3-a456-************'`
- **Validation**: Must be a valid UUID format
- **References**: `OptionType` entity in the database

### optionValueId (Optional)
- **Type**: `string` (UUID)
- **Description**: Database option value ID for specific option selection within the type
- **Example**: `'987fcdeb-51a2-43d1-b789-123456789abc'`
- **Validation**: Must be a valid UUID format
- **References**: `OptionValue` entity in the database

## Database Schema

The option system uses a three-tier structure:

1. **OptionType** - Categories like "grade", "topic", "question_type"
2. **OptionValue** - Specific values within each type like "P1", "P2" for grade
3. **WorksheetOption** - Junction table linking worksheets to option type/value pairs

## Usage Examples

### Adding a Question with Option References

```typescript
const questionDto: AddQuestionToWorksheetDto = {
  type: 'multiple_choice',
  content: 'What is the capital of France?',
  options: ['Paris', 'London', 'Berlin', 'Madrid'],
  answer: ['Paris'],
  explain: 'Paris is the capital and largest city of France.',
  optionTypeId: '123e4567-e89b-12d3-a456-************', // References "question_type" OptionType
  optionValueId: '987fcdeb-51a2-43d1-b789-123456789abc', // References "Multiple Choice" OptionValue
  position: 1,
  points: 5
};
```

### Adding a Question without Option References

```typescript
const questionDto: AddQuestionToWorksheetDto = {
  type: 'true_false',
  content: 'The sky is blue.',
  options: ['True', 'False'],
  answer: ['True'],
  explain: 'The sky appears blue due to light scattering.'
  // optionTypeId and optionValueId are optional
};
```

## API Documentation

The fields are automatically documented in the OpenAPI/Swagger specification:

- **optionTypeId**: Optional UUID field with example value
- **optionValueId**: Optional UUID field with example value
- Both fields include proper validation decorators (`@IsOptional`, `@IsString`, `@IsUUID`)

## Inheritance

The new fields are automatically inherited by related DTOs:

- **UpdateWorksheetQuestionDto**: Inherits via `PartialType(AddQuestionToWorksheetDto)`
- **ReplaceWorksheetQuestionDto**: Inherits via `extends AddQuestionToWorksheetDto`

## Service Layer Integration

The worksheet question service has been updated to handle the new fields:

- **createQuestion()**: Stores `optionTypeId` and `optionValueId` on the question object
- **applyQuestionUpdate()**: Automatically includes the fields via spread operator
- **createReplacementQuestion()**: Automatically includes the fields via spread operator

## Storage

The option references are stored as additional fields on the `IExerciseQuestion` object, leveraging the flexible `[key: string]: any` property of the interface.

## Validation

- Both fields are optional (`@IsOptional`)
- Both fields must be valid UUIDs when provided (`@IsUUID`)
- Both fields must be strings (`@IsString`)
- Invalid UUID formats will result in validation errors

## Testing

Comprehensive tests have been added to verify:
- Successful validation with valid UUIDs
- Successful validation without the fields (optional)
- Validation failures with invalid UUID formats
- Proper inheritance in related DTOs
- All valid question type enum values

## Backward Compatibility

The changes are fully backward compatible:
- Existing code will continue to work without modification
- The new fields are optional and don't affect existing functionality
- All existing tests continue to pass

## Future Enhancements

Potential future improvements could include:
- Validation that `optionValueId` belongs to the specified `optionTypeId`
- Service methods to retrieve questions by option type/value
- Analytics and reporting based on option associations
- Automatic option assignment based on question content analysis
