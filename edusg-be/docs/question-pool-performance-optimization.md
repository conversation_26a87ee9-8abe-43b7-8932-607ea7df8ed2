# Question Pool Performance Optimization

## Overview

This document describes the performance optimizations implemented for the random question pool selection feature in Task 19. The optimizations include database indexing, caching strategies, performance monitoring, and MongoDB connection pooling.

## Database Index Optimization

### Compound Indexes Added

The following compound indexes have been added to the `questions` collection to optimize query performance:

#### Primary Compound Indexes
```javascript
// For subject hierarchy + difficulty + type queries
{ status: 1, subject: 1, grade: 1, difficultyLevel: 1, type: 1 }
{ status: 1, parentSubject: 1, grade: 1, difficultyLevel: 1, type: 1 }
{ status: 1, childSubject: 1, grade: 1, difficultyLevel: 1, type: 1 }
```

#### Secondary Compound Indexes
```javascript
// For distribution-based queries
{ status: 1, difficultyLevel: 1, type: 1, grade: 1 }
{ status: 1, subject: 1, difficultyLevel: 1, type: 1 }
{ status: 1, parentSubject: 1, difficultyLevel: 1, type: 1 }
```

#### Language-Specific Indexes
```javascript
// For multilingual support
{ status: 1, language: 1, grade: 1, difficultyLevel: 1 }
{ status: 1, language: 1, subject: 1, type: 1 }
```

#### Diversity Tracking Indexes
```javascript
// For selection frequency optimization
{ status: 1, lastSelectedTimestamp: 1, selectionFrequency: 1 }
{ status: 1, difficultyLevel: 1, lastSelectedTimestamp: 1 }
```

#### Performance Optimization Indexes
```javascript
// For aggregation pipelines
{ status: 1, type: 1, _id: 1 } // For $nin exclusion queries
{ status: 1, grade: 1, subject: 1, _id: 1 } // For replacement queries
```

### Index Verification

Use MongoDB's `explain('executionStats')` command to verify index usage:

```javascript
db.questionpools.find({
  status: 'active',
  subject: 'Mathematics',
  grade: 'Primary 5',
  difficultyLevel: 'Medium'
}).explain('executionStats')
```

Look for:
- `IXSCAN` stages (indicates index usage)
- Low `docsExamined` relative to `docsReturned`
- Reasonable `executionTimeMillis`

## Caching Strategy

### QuestionPoolCacheService

A dedicated caching service that integrates with the existing `WorksheetDocumentCacheService`:

#### Cache Key Generation
- Includes all query parameters: subject, parentSubject, childSubject, type, grade, language, count
- Includes distribution configuration parameters
- Uses MD5 hash for consistent, unique keys
- Handles array parameters by sorting for consistency

#### Cache TTL
- Default TTL: 2 hours
- Configurable via service constructor
- Balances data freshness with performance gains

#### Cache Invalidation
- TTL-based expiration (automatic)
- Manual invalidation for specific parameters
- Event-driven invalidation (when questions are modified)

#### Caching Strategy
- Only caches successful, non-empty results
- Skips caching for diversity-enabled queries (to avoid stale selection tracking)
- Validates cached result structure before returning

### Usage Example

```typescript
// Check cache first
const cachedResult = await this.cacheService.getFromCache(params);
if (cachedResult) {
  return cachedResult; // Cache hit
}

// Execute query
const result = await this.performQuery(params);

// Cache the result
await this.cacheService.saveToCache(params, result);
```

## Performance Monitoring

### Metrics Collected

The `QuestionPoolMetricsService` collects the following metrics using Prometheus:

#### Query Performance
- `question_pool_query_duration_seconds`: Histogram of query execution times
- `question_pool_db_query_duration_seconds`: Histogram of database query times
- `question_pool_queries_total`: Counter of total queries by method and status

#### Cache Performance
- `question_pool_cache_hits_total`: Counter of cache hits
- `question_pool_cache_misses_total`: Counter of cache misses

#### Error Tracking
- `question_pool_errors_total`: Counter of errors by operation and type

#### Resource Utilization
- `question_pool_questions_requested`: Gauge of questions requested
- `question_pool_questions_returned`: Gauge of questions returned
- `question_pool_db_connections_active`: Gauge of active database connections

### Metrics Endpoints

#### Prometheus Metrics
```
GET /question-pool/metrics
Content-Type: text/plain
```

Returns metrics in Prometheus format for scraping.

#### Performance Summary
```
GET /question-pool/metrics/summary
Content-Type: application/json
```

Returns a JSON summary with key performance indicators:
```json
{
  "totalQueries": 1250,
  "cacheHitRate": 0.75,
  "averageQueryTime": 0.045,
  "errorRate": 0.002
}
```

#### Cache Statistics
```
GET /question-pool/metrics/cache-stats
Content-Type: application/json
```

Returns cache-specific statistics:
```json
{
  "hitRate": 0.75,
  "totalHits": 938,
  "totalMisses": 312
}
```

## MongoDB Connection Pooling

### Configuration

Updated MongoDB connection configuration with optimized pool settings:

```typescript
{
  uri: configService.get<string>('MONGODB_URI'),
  useNewUrlParser: true,
  useUnifiedTopology: true,
  // Connection pool configuration
  maxPoolSize: 20,           // Maximum connections
  minPoolSize: 5,            // Minimum connections
  maxIdleTimeMS: 30000,      // Close idle connections after 30s
  waitQueueTimeoutMS: 5000,  // Wait 5s for available connection
  serverSelectionTimeoutMS: 5000, // Server selection timeout
  socketTimeoutMS: 45000,    // Socket timeout
  bufferCommands: false,     // Disable command buffering
}
```

### Pool Monitoring

Monitor connection pool usage:
- Track active connections via metrics
- Monitor for connection timeouts
- Watch for pool exhaustion under load

## Performance Testing

### Baseline Measurement

Before optimization:
1. Record query execution times using `explain('executionStats')`
2. Measure `docsExamined`, `executionTimeMillis`, and stages used
3. Document baseline performance metrics

### Post-Optimization Validation

After implementing optimizations:
1. Re-run the same queries with `explain('executionStats')`
2. Verify `IXSCAN` usage and reduced `docsExamined`
3. Measure performance improvements

### Load Testing

Conduct load tests to validate:
- Query response times under concurrent load
- Cache hit rates during sustained traffic
- Connection pool behavior under stress
- Error rates and system stability

### Example Test Scenarios

```typescript
// Test cache hit scenario
const params = { subject: 'Mathematics', grade: 'Primary 5', count: 10 };
const result1 = await questionPoolService.getRandomQuestions(params);
const result2 = await questionPoolService.getRandomQuestions(params); // Should be cached

// Test index usage
const complexQuery = {
  subject: 'Science',
  grade: 'Secondary 2',
  difficultyLevel: 'Medium',
  type: ['multiple_choice', 'fill_blank'],
  count: 20
};
const result = await questionPoolService.getRandomQuestionsWithDistribution(complexQuery);
```

## Monitoring and Alerting

### Key Performance Indicators (KPIs)

Monitor these metrics for system health:

1. **Query Performance**
   - P95 query response time < 100ms
   - P99 query response time < 500ms
   - Average query time < 50ms

2. **Cache Performance**
   - Cache hit rate > 70%
   - Cache miss rate < 30%

3. **Error Rates**
   - Query error rate < 1%
   - Database connection errors < 0.1%

4. **Resource Utilization**
   - Active database connections < 80% of pool size
   - Memory usage stable
   - CPU usage reasonable

### Alerting Thresholds

Set up alerts for:
- Query response time P95 > 200ms
- Cache hit rate < 50%
- Error rate > 2%
- Database connection pool > 90% utilized

## Troubleshooting

### Common Issues

1. **High Query Times**
   - Check index usage with `explain()`
   - Verify compound indexes cover query patterns
   - Monitor database load

2. **Low Cache Hit Rate**
   - Review cache key generation logic
   - Check TTL settings
   - Verify cache invalidation strategy

3. **Connection Pool Exhaustion**
   - Monitor active connections
   - Check for connection leaks
   - Adjust pool size if needed

4. **Memory Issues**
   - Monitor cache size
   - Check for memory leaks in metrics collection
   - Review query result sizes

### Performance Debugging

Use these tools for debugging:
- MongoDB Profiler for slow queries
- Prometheus metrics for performance trends
- Application logs for error patterns
- Connection pool statistics

## Future Enhancements

### Potential Improvements

1. **Advanced Caching**
   - Redis-based distributed caching
   - Cache warming strategies
   - Intelligent cache invalidation

2. **Query Optimization**
   - Query result pagination
   - Streaming large result sets
   - Parallel query execution

3. **Monitoring Enhancements**
   - Real-time dashboards
   - Predictive alerting
   - Performance trend analysis

4. **Database Optimization**
   - Read replicas for query distribution
   - Sharding for large datasets
   - Archive old questions to separate collections
