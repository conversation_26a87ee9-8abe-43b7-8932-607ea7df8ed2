# RBAC Technical Specification

## Architecture Overview

The Role-Based Access Control (RBAC) system is implemented using NestJS guards and decorators, providing a declarative approach to authorization.

## Core Components

### 1. RoleGuard (`src/modules/auth/guards/role.guard.ts`)

```typescript
@Injectable()
export class RoleGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.get<EUserRole[]>('roles', context.getHandler());
    
    if (!requiredRoles || requiredRoles.length === 0) {
      return true; // No role restriction
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      return false; // No user in request
    }

    // Admin has access to all resources
    if (user.role === EUserRole.ADMIN) {
      return true;
    }

    // Check if user's role is in the required roles
    return requiredRoles.includes(user.role);
  }
}
```

**Key Features:**
- Extracts required roles from method metadata
- Implements admin privilege escalation
- Returns boolean for access decision

### 2. Roles Decorator (`src/modules/auth/decorators/role.decorator.ts`)

```typescript
export const Roles = (...roles: EUserRole[]) => SetMetadata('roles', roles);
```

**Usage:**
- `@Roles(EUserRole.ADMIN)` - Admin only
- `@Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)` - Admin or School Manager
- No decorator - All authenticated users

### 3. User Role Enum (`src/modules/user/dto/create-user.dto.ts`)

```typescript
export enum EUserRole {
  TEACHER = 'teacher',
  ADMIN = 'admin',
  STUDENT = 'student',
  SCHOOL_MANAGER = 'school_manager',
}
```

### 4. AuthGuard Integration

The RBAC system works in conjunction with `AuthGuard`:

```typescript
@UseGuards(AuthGuard, RoleGuard)
```

**Execution Order:**
1. `AuthGuard` validates JWT and populates `request.user`
2. `RoleGuard` checks user roles against required roles

## Implementation Patterns

### Controller-Level Protection

```typescript
@Controller('resource')
@UseGuards(AuthGuard, RoleGuard)
@ApiBearerAuth()
export class ResourceController {
  // All methods require authentication
  // Individual methods can add role restrictions
}
```

### Method-Level Protection

```typescript
@Get('admin-only')
@Roles(EUserRole.ADMIN)
async adminOnlyMethod() {
  // Only admins can access
}

@Get('public-authenticated')
async publicAuthenticatedMethod() {
  // All authenticated users can access
}
```

## User Context Structure

The `request.user` object contains:

```typescript
interface UserContext {
  sub: string;        // User ID
  email: string;      // User email
  role: EUserRole;    // User role
  schoolId?: string;  // School ID (if applicable)
}
```

## Business Logic Integration

### School Ownership Validation

```typescript
// Pattern for school managers
if (req.user.role === EUserRole.SCHOOL_MANAGER) {
  if (!req.user.schoolId) {
    throw new ForbiddenException('School manager must be assigned to a school');
  }
  
  if (resourceSchoolId !== req.user.schoolId) {
    throw new ForbiddenException('Access denied: different school');
  }
}
```

### Role Hierarchy Implementation

The system implements implicit role hierarchy through the `RoleGuard`:

1. **ADMIN**: Bypasses all role checks (line 28-30 in RoleGuard)
2. **Other Roles**: Must be explicitly listed in `@Roles()` decorator

## Error Handling

### HTTP Status Codes

| Scenario | Status Code | Exception Type |
|----------|-------------|----------------|
| No authentication token | 401 | `UnauthorizedException` |
| Invalid token | 401 | `UnauthorizedException` |
| Valid token, insufficient role | 403 | `ForbiddenException` |
| Business logic violation | 403 | `ForbiddenException` |

### Exception Flow

1. `AuthGuard` throws `UnauthorizedException` for auth failures
2. `RoleGuard` returns `false` for role failures (NestJS converts to 403)
3. Business logic throws `ForbiddenException` for ownership violations

## Performance Considerations

### Guard Execution

- Guards execute on every request to protected endpoints
- Role checking is O(1) for admin users
- Role checking is O(n) for other users (where n = number of allowed roles)
- Metadata reflection happens once per method (cached by NestJS)

### Optimization Strategies

1. **Early Returns**: Admin check happens first for performance
2. **Minimal Logic**: Guards contain only essential authorization logic
3. **Caching**: NestJS caches decorator metadata automatically

## Security Features

### Protection Against Common Attacks

1. **Privilege Escalation**: Admin role is hardcoded in guard logic
2. **Role Injection**: Roles come from JWT payload, not request parameters
3. **Bypass Attempts**: All endpoints require explicit guard declaration

### Security Best Practices

1. **Fail Secure**: Default behavior denies access
2. **Explicit Permissions**: Each endpoint must declare its requirements
3. **Separation of Concerns**: Authentication and authorization are separate
4. **Audit Trail**: All access decisions are logged through NestJS logging

## Testing Strategy

### Unit Testing

```typescript
describe('RoleGuard', () => {
  it('should allow admin access to any endpoint', () => {
    // Test admin privilege escalation
  });
  
  it('should deny access when user lacks required role', () => {
    // Test role enforcement
  });
});
```

### Integration Testing

```typescript
describe('Controller RBAC', () => {
  it('should return 403 for insufficient permissions', async () => {
    // Test end-to-end authorization
  });
});
```

## Migration and Deployment

### Backward Compatibility

- New RBAC system is additive (doesn't break existing functionality)
- Endpoints without `@Roles()` decorator allow all authenticated users
- Gradual migration possible by adding decorators incrementally

### Deployment Considerations

1. **Database Changes**: No schema changes required for basic RBAC
2. **Environment Variables**: No new configuration needed
3. **Monitoring**: Consider adding metrics for authorization failures

## Future Enhancements

### Potential Improvements

1. **Resource-Level Permissions**: More granular access control
2. **Dynamic Roles**: Database-driven role configuration
3. **Permission Caching**: Cache user permissions for performance
4. **Audit Logging**: Detailed access attempt logging
5. **Role Inheritance**: More complex role hierarchies

### Extension Points

1. **Custom Guards**: Additional guards for specific business logic
2. **Permission Decorators**: More specific permission decorators
3. **Context Providers**: Additional user context information
4. **Integration**: Integration with external authorization systems
