{"dashboard": {"id": "worksheet-question-performance", "title": "Worksheet Question Management Performance", "description": "Comprehensive performance monitoring dashboard for worksheet question operations", "version": "1.0.0", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "API Response Time", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(worksheet_question_api_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.50, rate(worksheet_question_api_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile", "refId": "B"}], "yAxes": [{"label": "Response Time (seconds)", "min": 0}], "thresholds": [{"value": 0.5, "colorMode": "critical", "op": "gt"}, {"value": 0.1, "colorMode": "warning", "op": "gt"}]}, {"id": 2, "title": "Request Rate", "type": "graph", "targets": [{"expr": "rate(worksheet_question_api_requests_total[5m])", "legendFormat": "{{method}} {{endpoint}}", "refId": "A"}], "yAxes": [{"label": "Requests per second", "min": 0}]}, {"id": 3, "title": "Error Rate", "type": "graph", "targets": [{"expr": "rate(worksheet_question_api_errors_total[5m]) / rate(worksheet_question_api_requests_total[5m]) * 100", "legendFormat": "Error Rate %", "refId": "A"}], "yAxes": [{"label": "Error Rate (%)", "min": 0, "max": 100}], "thresholds": [{"value": 5, "colorMode": "critical", "op": "gt"}, {"value": 1, "colorMode": "warning", "op": "gt"}]}, {"id": 4, "title": "Database Query Performance", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(worksheet_question_db_query_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.50, rate(worksheet_question_db_query_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile", "refId": "B"}], "yAxes": [{"label": "Query Time (seconds)", "min": 0}]}, {"id": 5, "title": "<PERSON><PERSON>", "type": "stat", "targets": [{"expr": "rate(worksheet_question_cache_hits_total[5m]) / (rate(worksheet_question_cache_hits_total[5m]) + rate(worksheet_question_cache_misses_total[5m])) * 100", "legendFormat": "<PERSON><PERSON> Hit Rate", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 70}, {"color": "green", "value": 85}]}}}}, {"id": 6, "title": "Memory Usage", "type": "graph", "targets": [{"expr": "worksheet_question_memory_usage_bytes", "legendFormat": "Memory Usage", "refId": "A"}], "yAxes": [{"label": "Memory (bytes)", "min": 0}]}, {"id": 7, "title": "Active Operations", "type": "stat", "targets": [{"expr": "worksheet_question_active_operations", "legendFormat": "Active Operations", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "min": 0}}}, {"id": 8, "title": "Database Connections", "type": "stat", "targets": [{"expr": "worksheet_question_db_connections", "legendFormat": "DB Connections", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "min": 0}}}, {"id": 9, "title": "Background Job Queue", "type": "graph", "targets": [{"expr": "worksheet_question_background_jobs_pending", "legendFormat": "Pending Jobs", "refId": "A"}, {"expr": "worksheet_question_background_jobs_processing", "legendFormat": "Processing Jobs", "refId": "B"}, {"expr": "worksheet_question_background_jobs_completed", "legendFormat": "Completed Jobs", "refId": "C"}], "yAxes": [{"label": "Job Count", "min": 0}]}, {"id": 10, "title": "Rate Limiting", "type": "graph", "targets": [{"expr": "rate(worksheet_question_rate_limit_exceeded_total[5m])", "legendFormat": "Rate Limit Exceeded", "refId": "A"}], "yAxes": [{"label": "Rate Limit Violations/sec", "min": 0}]}, {"id": 11, "title": "Collaboration Metrics", "type": "stat", "targets": [{"expr": "worksheet_question_active_collaborators", "legendFormat": "Active Collaborators", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "min": 0}}}, {"id": 12, "title": "Performance Test Results", "type": "table", "targets": [{"expr": "worksheet_question_performance_test_duration_seconds", "legendFormat": "{{test_name}}", "refId": "A", "format": "table"}], "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"test_name": "Test Name", "Value": "Duration (s)"}}}]}], "templating": {"list": [{"name": "environment", "type": "query", "query": "label_values(worksheet_question_api_requests_total, environment)", "refresh": 1, "includeAll": false, "multi": false}, {"name": "user_role", "type": "query", "query": "label_values(worksheet_question_api_requests_total, user_role)", "refresh": 1, "includeAll": true, "multi": true}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "prometheus", "expr": "worksheet_question_deployment_timestamp", "titleFormat": "Deployment", "textFormat": "Version: {{version}}"}, {"name": "Performance Tests", "datasource": "prometheus", "expr": "worksheet_question_performance_test_completed", "titleFormat": "Performance Test", "textFormat": "Test: {{test_name}}"}]}, "links": [{"title": "Capacity Planning Report", "url": "/api/worksheet-questions/performance/capacity-report", "type": "link"}, {"title": "Performance Test Suite", "url": "/api/worksheet-questions/performance/test-suite", "type": "link"}]}, "alerts": [{"name": "High Response Time", "condition": "avg(worksheet_question_api_duration_seconds) > 0.5", "frequency": "10s", "message": "Worksheet question API response time is above 500ms", "severity": "warning"}, {"name": "High Error Rate", "condition": "rate(worksheet_question_api_errors_total[5m]) / rate(worksheet_question_api_requests_total[5m]) > 0.05", "frequency": "30s", "message": "Worksheet question API error rate is above 5%", "severity": "critical"}, {"name": "Low Cache Hit Rate", "condition": "rate(worksheet_question_cache_hits_total[5m]) / (rate(worksheet_question_cache_hits_total[5m]) + rate(worksheet_question_cache_misses_total[5m])) < 0.7", "frequency": "60s", "message": "Worksheet question cache hit rate is below 70%", "severity": "warning"}, {"name": "High Memory Usage", "condition": "worksheet_question_memory_usage_bytes > 200000000", "frequency": "30s", "message": "Worksheet question service memory usage is above 200MB", "severity": "warning"}]}