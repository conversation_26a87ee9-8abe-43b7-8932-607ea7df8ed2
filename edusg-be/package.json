{"name": "edusg-be", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "npm run build && ts-node ./node_modules/typeorm/cli", "migration:run": "npm run typeorm migration:run -- -d ./src/core/configs/database/typeorm.config.ts", "migration:generate": "npm run typeorm -- -d ./src/core/configs/database/typeorm.config.ts migration:generate ./migrations/$npm_config_name", "migration:create": "npm run typeorm -- migration:create ./migrations/$npm_config_name", "migration:revert": "npm run typeorm -- -d ./src/core/configs/database/typeorm.config.ts migration:revert", "script:update-worksheet-ids": "ts-node scripts/update-worksheet-question-ids.ts", "script:verify-worksheet-ids": "ts-node scripts/verify-worksheet-question-ids.ts", "debug-sync": "ts-node scripts/debug-worksheet-question-sync.ts"}, "dependencies": {"@google/generative-ai": "^0.24.0", "@llamaindex/google": "^0.2.3", "@llamaindex/pinecone": "^0.1.2", "@llamaindex/qdrant": "^0.1.11", "@llamaindex/readers": "^3.0.2", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.17", "@nestjs/platform-socket.io": "^11.1.0", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^11.1.6", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.0", "@pinecone-database/pinecone": "^5.1.1", "@qdrant/js-client-rest": "^1.13.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bullmq": "^5.49.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "handlebars": "^4.7.8", "ioredis": "^5.6.1", "js-yaml": "^4.1.0", "jsdom": "^26.1.0", "llamaindex": "^0.9.19", "mime-types": "^3.0.1", "mongoose": "^8.13.2", "nest-commander": "^3.17.0", "nodemailer": "^7.0.3", "openai": "^4.94.0", "passport-jwt": "^4.0.1", "pg": "^8.14.1", "prom-client": "^15.1.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sanitize-filename": "^1.6.3", "socket.io": "^4.8.1", "stripe": "^18.2.1", "typeorm": "^0.3.22", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.3", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/jsdom": "^21.1.7", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.12", "@types/node": "^22.10.7", "@types/stripe": "^8.0.417", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}