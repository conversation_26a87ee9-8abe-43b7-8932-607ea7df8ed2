import { MongoClient, Db } from 'mongodb';
import { Logger } from '@nestjs/common';

/**
 * MongoDB Migration Script: Create Worksheet Questions Collection
 * This script creates the worksheet_questions collection with proper indexes
 * and schema validation for optimal performance and data integrity.
 */

const logger = new Logger('MongoDBMigration');

export interface MigrationResult {
  success: boolean;
  message: string;
  details?: any;
}

export class CreateWorksheetQuestionsCollectionMigration {
  private db: Db;

  constructor(db: Db) {
    this.db = db;
  }

  /**
   * Execute the migration
   */
  async up(): Promise<MigrationResult> {
    try {
      logger.log('Starting MongoDB migration: Create worksheet_questions collection');

      // Check if collection already exists
      const collections = await this.db.listCollections({ name: 'worksheet_questions' }).toArray();
      if (collections.length > 0) {
        logger.warn('Collection worksheet_questions already exists, skipping creation');
        return {
          success: true,
          message: 'Collection already exists, skipping creation'
        };
      }

      // Create the collection with schema validation
      await this.createCollectionWithValidation();

      // Create all required indexes
      await this.createIndexes();

      // Insert sample data if needed (for development/testing)
      if (process.env.NODE_ENV === 'development') {
        await this.insertSampleData();
      }

      logger.log('✅ MongoDB migration completed successfully');
      return {
        success: true,
        message: 'Worksheet questions collection created successfully with indexes'
      };
    } catch (error) {
      logger.error('❌ MongoDB migration failed', error);
      return {
        success: false,
        message: `Migration failed: ${error.message}`,
        details: error
      };
    }
  }

  /**
   * Rollback the migration
   */
  async down(): Promise<MigrationResult> {
    try {
      logger.log('Rolling back MongoDB migration: Drop worksheet_questions collection');

      // Drop the collection
      await this.db.collection('worksheet_questions').drop();

      logger.log('✅ MongoDB migration rollback completed successfully');
      return {
        success: true,
        message: 'Worksheet questions collection dropped successfully'
      };
    } catch (error) {
      if (error.message.includes('ns not found')) {
        logger.warn('Collection worksheet_questions does not exist, nothing to rollback');
        return {
          success: true,
          message: 'Collection does not exist, nothing to rollback'
        };
      }

      logger.error('❌ MongoDB migration rollback failed', error);
      return {
        success: false,
        message: `Rollback failed: ${error.message}`,
        details: error
      };
    }
  }

  /**
   * Create collection with schema validation
   */
  private async createCollectionWithValidation(): Promise<void> {
    const validationSchema = {
      $jsonSchema: {
        bsonType: 'object',
        required: ['worksheetId', 'questionId', 'type', 'content', 'options', 'answer', 'explain', 'position', 'audit'],
        properties: {
          worksheetId: {
            bsonType: 'string',
            description: 'Worksheet ID is required and must be a string'
          },
          questionId: {
            bsonType: 'string',
            description: 'Question ID is required and must be a string'
          },
          type: {
            enum: ['multiple_choice', 'true_false', 'short_answer', 'long_answer', 'fill_in_the_blank', 'matching', 'ordering', 'calculation', 'diagram', 'essay'],
            description: 'Question type must be one of the allowed values'
          },
          content: {
            bsonType: 'string',
            maxLength: 2000,
            description: 'Question content is required and must be a string with max 2000 characters'
          },
          options: {
            bsonType: 'array',
            items: {
              bsonType: 'string'
            },
            description: 'Options must be an array of strings'
          },
          answer: {
            bsonType: 'array',
            items: {
              bsonType: 'string'
            },
            description: 'Answer must be an array of strings'
          },
          explain: {
            bsonType: 'string',
            maxLength: 1000,
            description: 'Explanation is required and must be a string with max 1000 characters'
          },
          position: {
            bsonType: 'int',
            minimum: 1,
            description: 'Position must be a positive integer'
          },
          points: {
            bsonType: 'number',
            minimum: 0,
            description: 'Points must be a non-negative number'
          },
          status: {
            enum: ['active', 'inactive', 'locked', 'pending_review', 'approved', 'rejected'],
            description: 'Status must be one of the allowed values'
          },
          schoolId: {
            bsonType: ['string', 'null'],
            description: 'School ID must be a string or null'
          },
          audit: {
            bsonType: 'object',
            required: ['createdAt', 'updatedAt', 'createdBy', 'version'],
            properties: {
              createdAt: {
                bsonType: 'date',
                description: 'Created date is required'
              },
              updatedAt: {
                bsonType: 'date',
                description: 'Updated date is required'
              },
              createdBy: {
                bsonType: 'string',
                description: 'Created by user ID is required'
              },
              version: {
                bsonType: 'int',
                minimum: 1,
                description: 'Version must be a positive integer'
              }
            }
          },
          schemaVersion: {
            bsonType: 'int',
            minimum: 1,
            description: 'Schema version must be a positive integer'
          }
        }
      }
    };

    await this.db.createCollection('worksheet_questions', {
      validator: validationSchema,
      validationLevel: 'strict',
      validationAction: 'error'
    });

    logger.log('Created worksheet_questions collection with schema validation');
  }

  /**
   * Create all required indexes
   */
  private async createIndexes(): Promise<void> {
    const collection = this.db.collection('worksheet_questions');

    // Core indexes for basic operations
    await collection.createIndex(
      { worksheetId: 1, position: 1 },
      { unique: true, name: 'worksheet_position_unique', background: true }
    );

    await collection.createIndex(
      { worksheetId: 1, status: 1 },
      { name: 'worksheet_status', background: true }
    );

    await collection.createIndex(
      { schoolId: 1, worksheetId: 1 },
      { name: 'school_worksheet', background: true, sparse: true }
    );

    // Performance indexes for common query patterns
    await collection.createIndex(
      { schoolId: 1, subject: 1, grade: 1 },
      { name: 'school_subject_grade', background: true, sparse: true }
    );

    await collection.createIndex(
      { type: 1, difficulty: 1 },
      { name: 'type_difficulty', background: true, sparse: true }
    );

    await collection.createIndex(
      { topic: 1, childSubject: 1 },
      { name: 'topic_child_subject', background: true, sparse: true }
    );

    await collection.createIndex(
      { questionId: 1 },
      { name: 'question_id', background: true }
    );

    await collection.createIndex(
      { worksheetId: 1, position: 1, status: 1 },
      { name: 'worksheet_position_status', background: true }
    );

    // Search indexes
    await collection.createIndex(
      {
        content: 'text',
        explain: 'text',
        'metadata.tags': 'text',
        'metadata.keywords': 'text'
      },
      {
        name: 'content_search',
        background: true,
        weights: {
          content: 10,
          explain: 5,
          'metadata.tags': 3,
          'metadata.keywords': 2
        }
      }
    );

    await collection.createIndex(
      { 'metadata.tags': 1 },
      { name: 'metadata_tags', background: true, sparse: true }
    );

    // Audit indexes
    await collection.createIndex(
      { 'audit.createdAt': -1 },
      { name: 'audit_created_desc', background: true }
    );

    await collection.createIndex(
      { 'audit.updatedAt': -1 },
      { name: 'audit_updated_desc', background: true }
    );

    await collection.createIndex(
      { 'audit.createdBy': 1, 'audit.createdAt': -1 },
      { name: 'audit_created_by', background: true }
    );

    await collection.createIndex(
      { 'audit.updatedBy': 1, 'audit.updatedAt': -1 },
      { name: 'audit_updated_by', background: true, sparse: true }
    );

    // Analytics and lock management indexes
    await collection.createIndex(
      { 'analytics.totalAttempts': -1 },
      { name: 'analytics_attempts', background: true, sparse: true }
    );

    await collection.createIndex(
      { lockedBy: 1, lockedAt: 1 },
      { name: 'lock_management', background: true, sparse: true }
    );

    // TTL index for automatic expiration
    await collection.createIndex(
      { expiresAt: 1 },
      { name: 'ttl_expiration', expireAfterSeconds: 0, background: true, sparse: true }
    );

    logger.log('Created all indexes for worksheet_questions collection');
  }

  /**
   * Insert sample data for development/testing
   */
  private async insertSampleData(): Promise<void> {
    const collection = this.db.collection('worksheet_questions');

    const sampleQuestions = [
      {
        worksheetId: 'sample-worksheet-1',
        questionId: 'sample-question-1',
        type: 'multiple_choice',
        content: 'What is the capital of France?',
        options: ['Paris', 'London', 'Berlin', 'Madrid'],
        answer: ['Paris'],
        explain: 'Paris is the capital and largest city of France.',
        position: 1,
        points: 1,
        status: 'active',
        subject: 'Geography',
        grade: '6',
        difficulty: 'easy',
        audit: {
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'system',
          version: 1,
          changeLog: []
        },
        schemaVersion: 1
      }
    ];

    await collection.insertMany(sampleQuestions);
    logger.log('Inserted sample data for development');
  }
}

/**
 * Main migration function
 */
export async function runMigration(mongoUrl: string): Promise<MigrationResult> {
  const client = new MongoClient(mongoUrl);
  
  try {
    await client.connect();
    const db = client.db();
    
    const migration = new CreateWorksheetQuestionsCollectionMigration(db);
    return await migration.up();
  } finally {
    await client.close();
  }
}

/**
 * Main rollback function
 */
export async function rollbackMigration(mongoUrl: string): Promise<MigrationResult> {
  const client = new MongoClient(mongoUrl);
  
  try {
    await client.connect();
    const db = client.db();
    
    const migration = new CreateWorksheetQuestionsCollectionMigration(db);
    return await migration.down();
  } finally {
    await client.close();
  }
}
