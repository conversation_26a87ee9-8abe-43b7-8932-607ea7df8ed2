import { runMigration } from './mongodb-migrations/001-create-worksheet-questions-collection';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function main() {
  const mongoUrl = process.env.MONGODB_URI || 'mongodb://localhost:27017/your-database';
  
  try {
    const result = await runMigration(mongoUrl);
    console.log('Migration result:', result);
    
    if (result.success) {
      console.log('✅ Migration completed successfully');
      process.exit(0);
    } else {
      console.error('❌ Migration failed:', result.message);
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Migration execution failed:', error);
    process.exit(1);
  }
}

main();