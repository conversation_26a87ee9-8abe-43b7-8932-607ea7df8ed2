import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import questionPoolConfig, { QuestionPoolConfigType } from './question-pool.config';

describe('QuestionPoolConfig', () => {
  let configService: ConfigService;

  beforeEach(async () => {
    // Clear environment variables before each test
    delete process.env.QUESTION_POOL_ENABLED;
    delete process.env.DEFAULT_SELECTION_STRATEGY;
    delete process.env.MIN_POOL_QUESTIONS_THRESHOLD;
    delete process.env.ALLOW_POOL_ONLY_STRATEGY;
    delete process.env.ALLOW_AI_ONLY_STRATEGY;
    delete process.env.ALLOW_HYBRID_STRATEGY;
    delete process.env.ALLOW_MIXED_STRATEGY;

    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [questionPoolConfig],
          isGlobal: true,
        }),
      ],
    }).compile();

    configService = module.get<ConfigService>(ConfigService);
  });

  describe('Default Configuration', () => {
    it('should load default configuration when no environment variables are set', () => {
      const config = configService.get<QuestionPoolConfigType>('questionPool');

      expect(config).toBeDefined();
      expect(config!.enabled).toBe(true);
      expect(config!.defaultSelectionStrategy).toBe('hybrid');
      expect(config!.minPoolQuestionsThreshold).toBe(10);
      expect(config!.featureFlags.allowPoolOnlyStrategy).toBe(true);
      expect(config!.featureFlags.allowAiOnlyStrategy).toBe(true);
      expect(config!.featureFlags.allowHybridStrategy).toBe(true);
      expect(config!.featureFlags.allowMixedStrategy).toBe(true);
    });
  });

  describe('Environment Variable Overrides', () => {
    it('should override enabled setting from environment variable', async () => {
      process.env.QUESTION_POOL_ENABLED = 'false';

      const module: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            load: [questionPoolConfig],
            isGlobal: true,
          }),
        ],
      }).compile();

      const testConfigService = module.get<ConfigService>(ConfigService);
      const config = testConfigService.get<QuestionPoolConfigType>('questionPool');

      expect(config!.enabled).toBe(false);
    });

    it('should override default selection strategy from environment variable', async () => {
      process.env.DEFAULT_SELECTION_STRATEGY = 'pool-only';

      const module: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            load: [questionPoolConfig],
            isGlobal: true,
          }),
        ],
      }).compile();

      const testConfigService = module.get<ConfigService>(ConfigService);
      const config = testConfigService.get<QuestionPoolConfigType>('questionPool');

      expect(config!.defaultSelectionStrategy).toBe('pool-only');
    });

    it('should override min pool questions threshold from environment variable', async () => {
      process.env.MIN_POOL_QUESTIONS_THRESHOLD = '25';

      const module: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            load: [questionPoolConfig],
            isGlobal: true,
          }),
        ],
      }).compile();

      const testConfigService = module.get<ConfigService>(ConfigService);
      const config = testConfigService.get<QuestionPoolConfigType>('questionPool');

      expect(config!.minPoolQuestionsThreshold).toBe(25);
    });

    it('should override feature flags from environment variables', async () => {
      process.env.ALLOW_POOL_ONLY_STRATEGY = 'false';
      process.env.ALLOW_AI_ONLY_STRATEGY = 'false';

      const module: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            load: [questionPoolConfig],
            isGlobal: true,
          }),
        ],
      }).compile();

      const testConfigService = module.get<ConfigService>(ConfigService);
      const config = testConfigService.get<QuestionPoolConfigType>('questionPool');

      expect(config!.featureFlags.allowPoolOnlyStrategy).toBe(false);
      expect(config!.featureFlags.allowAiOnlyStrategy).toBe(false);
      expect(config!.featureFlags.allowHybridStrategy).toBe(true); // Should remain default
      expect(config!.featureFlags.allowMixedStrategy).toBe(true); // Should remain default
    });
  });

  describe('Configuration Validation', () => {
    it('should handle invalid threshold values gracefully', async () => {
      process.env.MIN_POOL_QUESTIONS_THRESHOLD = 'invalid';

      const module: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            load: [questionPoolConfig],
            isGlobal: true,
          }),
        ],
      }).compile();

      const testConfigService = module.get<ConfigService>(ConfigService);
      const config = testConfigService.get<QuestionPoolConfigType>('questionPool');

      expect(config!.minPoolQuestionsThreshold).toBeNaN();
    });

    it('should handle all selection strategies', async () => {
      const strategies = ['pool-only', 'ai-only', 'hybrid', 'mixed'];

      for (const strategy of strategies) {
        process.env.DEFAULT_SELECTION_STRATEGY = strategy;

        const module: TestingModule = await Test.createTestingModule({
          imports: [
            ConfigModule.forRoot({
              load: [questionPoolConfig],
              isGlobal: true,
            }),
          ],
        }).compile();

        const testConfigService = module.get<ConfigService>(ConfigService);
        const config = testConfigService.get<QuestionPoolConfigType>('questionPool');

        expect(config!.defaultSelectionStrategy).toBe(strategy);
      }
    });
  });
});
