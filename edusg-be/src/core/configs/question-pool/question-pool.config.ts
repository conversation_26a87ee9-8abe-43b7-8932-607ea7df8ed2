import { registerAs } from '@nestjs/config';
import { configService } from '../env/env.config';
import { DifficultyDistribution, QuestionTypeBalancing, DiversityConfig, QualityValidationConfig, FallbackConfig } from '../../../modules/question-pool/interfaces/distribution.interface';

export interface QuestionPoolConfigType {
  enabled: boolean;
  defaultSelectionStrategy: 'pool-only' | 'ai-only' | 'hybrid' | 'mixed';
  minPoolQuestionsThreshold: number;
  featureFlags: {
    allowPoolOnlyStrategy: boolean;
    allowAiOnlyStrategy: boolean;
    allowHybridStrategy: boolean;
    allowMixedStrategy: boolean;
  };
  distribution: {
    defaultDifficultyDistribution: DifficultyDistribution;
    defaultQuestionTypeBalancing: QuestionTypeBalancing;
    defaultDiversityConfig: DiversityConfig;
    defaultQualityValidationConfig: QualityValidationConfig;
    defaultFallbackConfig: FallbackConfig;
  };
}

export default registerAs('questionPool', (): QuestionPoolConfigType => {
  return {
    enabled: configService.get('QUESTION_POOL_ENABLED') === 'true' ||
             configService.get('QUESTION_POOL_ENABLED') === undefined, // Default to true
    defaultSelectionStrategy: (configService.get('DEFAULT_SELECTION_STRATEGY') as 'pool-only' | 'ai-only' | 'hybrid' | 'mixed') || 'hybrid',
    minPoolQuestionsThreshold: parseInt(
      configService.get('MIN_POOL_QUESTIONS_THRESHOLD') ?? '10',
      10,
    ),
    featureFlags: {
      allowPoolOnlyStrategy: configService.get('ALLOW_POOL_ONLY_STRATEGY') !== 'false', // Default to true
      allowAiOnlyStrategy: configService.get('ALLOW_AI_ONLY_STRATEGY') !== 'false', // Default to true
      allowHybridStrategy: configService.get('ALLOW_HYBRID_STRATEGY') !== 'false', // Default to true
      allowMixedStrategy: configService.get('ALLOW_MIXED_STRATEGY') !== 'false', // Default to true
    },
    distribution: {
      defaultDifficultyDistribution: {
        Easy: parseFloat(configService.get('DEFAULT_EASY_PERCENTAGE') ?? '0.2'),
        Medium: parseFloat(configService.get('DEFAULT_MEDIUM_PERCENTAGE') ?? '0.6'),
        Advanced: parseFloat(configService.get('DEFAULT_ADVANCED_PERCENTAGE') ?? '0.2'),
      },
      defaultQuestionTypeBalancing: {
        enabled: configService.get('QUESTION_TYPE_BALANCING_ENABLED') !== 'false', // Default to true
        preferDiversity: configService.get('PREFER_QUESTION_TYPE_DIVERSITY') !== 'false', // Default to true
      },
      defaultDiversityConfig: {
        enabled: configService.get('DIVERSITY_ALGORITHMS_ENABLED') !== 'false', // Default to true
        recencyPenaltyWeight: parseFloat(configService.get('RECENCY_PENALTY_WEIGHT') ?? '0.3'),
        frequencyPenaltyWeight: parseFloat(configService.get('FREQUENCY_PENALTY_WEIGHT') ?? '0.2'),
        recencyThresholdHours: parseInt(configService.get('RECENCY_THRESHOLD_HOURS') ?? '24', 10),
      },
      defaultQualityValidationConfig: {
        enabled: configService.get('QUALITY_VALIDATION_ENABLED') !== 'false', // Default to true
        failureHandlingStrategy: (configService.get('VALIDATION_FAILURE_STRATEGY') as 'discard' | 'replace' | 'log_and_proceed') || 'replace',
        maxReplacementAttempts: parseInt(configService.get('MAX_REPLACEMENT_ATTEMPTS') ?? '3', 10),
        minValidationSuccessRate: parseFloat(configService.get('MIN_VALIDATION_SUCCESS_RATE') ?? '0.8'),
      },
      defaultFallbackConfig: {
        allowBestEffort: configService.get('ALLOW_BEST_EFFORT_SELECTION') !== 'false', // Default to true
        relaxDistributionOnShortfall: configService.get('RELAX_DISTRIBUTION_ON_SHORTFALL') !== 'false', // Default to true
        logFallbackReasons: configService.get('LOG_FALLBACK_REASONS') !== 'false', // Default to true
      },
    },
  };
});
