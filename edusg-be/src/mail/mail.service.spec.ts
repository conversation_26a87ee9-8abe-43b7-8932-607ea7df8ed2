import { Test, TestingModule } from '@nestjs/testing';
import { MailerService } from '@nestjs-modules/mailer';
import { InternalServerErrorException } from '@nestjs/common';
import { MailService } from './mail.service';
import { User } from '../modules/user/entities/user.entity';

describe('MailService', () => {
  let service: MailService;

  const mockMailerService = {
    sendMail: jest.fn(),
  };

  const mockUser: Partial<User> = {
    id: '123',
    email: '<EMAIL>',
    name: 'Test User',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MailService,
        {
          provide: MailerService,
          useValue: mockMailerService,
        },
      ],
    }).compile();

    service = module.get<MailService>(MailService);

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendUserPasswordReset', () => {
    const resetUrl = 'http://localhost:3000/reset-password?token=abc123';

    it('should send password reset email successfully', async () => {
      // Arrange
      mockMailerService.sendMail.mockResolvedValue(undefined);

      // Act
      await service.sendUserPasswordReset(mockUser as User, resetUrl);

      // Assert
      expect(mockMailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Reset your password',
        template: './reset-password',
        context: {
          name: 'Test User',
          url: resetUrl,
        },
      });
    });

    it('should throw InternalServerErrorException when email fails', async () => {
      // Arrange
      mockMailerService.sendMail.mockRejectedValue(new Error('SMTP Error'));

      // Act & Assert
      await expect(service.sendUserPasswordReset(mockUser as User, resetUrl))
        .rejects.toThrow(InternalServerErrorException);

      expect(mockMailerService.sendMail).toHaveBeenCalled();
    });

    it('should log email sending process', async () => {
      // Arrange
      mockMailerService.sendMail.mockResolvedValue(undefined);
      const logSpy = jest.spyOn(service['logger'], 'log').mockImplementation();

      // Act
      await service.sendUserPasswordReset(mockUser as User, resetUrl);

      // Assert
      expect(logSpy).toHaveBeenCalledWith('Sending password reset <NAME_EMAIL>');
      expect(logSpy).toHaveBeenCalledWith('Password reset email sent <NAME_EMAIL>');

      logSpy.mockRestore();
    });
  });
});
