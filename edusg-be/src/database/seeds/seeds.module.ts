import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UpdateWorksheetQuestionIdsSeed } from './update-worksheet-question-ids.seed';
import { UpdateWorksheetQuestionIdsCommand } from './commands/update-worksheet-question-ids.command';
import { WorksheetPromptResult, WorksheetPromptResultSchema } from '../../modules/mongodb/schemas/worksheet-prompt-result.schema';
import { Worksheet } from '../../modules/worksheet/entities/worksheet.entity';

@Module({
  imports: [
    // MongoDB schemas
    MongooseModule.forFeature([
      { name: WorksheetPromptResult.name, schema: WorksheetPromptResultSchema },
    ]),
    // PostgreSQL entities
    TypeOrmModule.forFeature([Worksheet]),
  ],
  providers: [
    UpdateWorksheetQuestionIdsSeed,
    UpdateWorksheetQuestionIdsCommand,
  ],
  exports: [
    UpdateWorksheetQuestionIdsSeed,
    UpdateWorksheetQuestionIdsCommand,
  ],
})
export class SeedsModule {}
