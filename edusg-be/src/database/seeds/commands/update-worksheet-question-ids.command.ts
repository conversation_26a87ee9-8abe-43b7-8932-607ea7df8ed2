import { Command, CommandRunner, Option } from 'nest-commander';
import { Logger } from '@nestjs/common';
import { UpdateWorksheetQuestionIdsSeed } from '../update-worksheet-question-ids.seed';

@Command({
  name: 'update-worksheet-question-ids',
  description: 'Update worksheet questions with stable IDs for proper ordering',
})
export class UpdateWorksheetQuestionIdsCommand extends CommandRunner {
  private readonly logger = new Logger(UpdateWorksheetQuestionIdsCommand.name);

  constructor(private readonly seedService: UpdateWorksheetQuestionIdsSeed) {
    super();
  }

  async run(passedParams: string[], options?: Record<string, any>): Promise<void> {
    this.logger.log('Starting worksheet question IDs update command...');

    try {
      if (options?.dryRun) {
        this.logger.log('Running in DRY RUN mode - no changes will be made');
        await this.seedService.dryRun();
      } else {
        this.logger.log('Running migration - this will update your database');
        
        if (!options?.force) {
          this.logger.warn('This will modify your database. Use --force to confirm or --dry-run to preview changes');
          return;
        }

        await this.seedService.run();
      }

      this.logger.log('Command completed successfully');
    } catch (error) {
      this.logger.error(`Command failed: ${error.message}`, error.stack);
      process.exit(1);
    }
  }

  @Option({
    flags: '--dry-run',
    description: 'Preview what would be updated without making changes',
  })
  parseDryRun(): boolean {
    return true;
  }

  @Option({
    flags: '--force',
    description: 'Confirm that you want to update the database',
  })
  parseForce(): boolean {
    return true;
  }
}
