import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Price } from './entities/price.entity';
import { Repository } from 'typeorm';
import Stripe from 'stripe';
import { Package } from '../packages/entities/package.entity';

@Injectable()
export class PriceService {
  private readonly logger = new Logger(PriceService.name);

  constructor(
    @InjectRepository(Price)
    private priceRepo: Repository<Price>,
    @InjectRepository(Package)
    private packageRepo: Repository<Package>,
  ) {}

  async saveOrUpdateStripePrice(stripePrice: Stripe.Price): Promise<Price> {
    const stripeProductId =
      typeof stripePrice.product === 'string'
        ? stripePrice.product
        : stripePrice.product.id;

    let pkg = await this.packageRepo.findOne({ where: { stripeProductId } });

    if (!pkg) {
      // Bạn có thể tạo mới nếu Product chưa có, hoặc bỏ qua nếu muốn.
      this.logger.warn(
        `Package with stripeProductId ${stripeProductId} not found. Creating a placeholder.`,
      );
      pkg = this.packageRepo.create({
        stripeProductId,
        name: 'Unnamed Product',
        description: 'Auto-generated from Stripe price event.',
        image: '',
      });
      await this.packageRepo.save(pkg);
    }

    const priceData: Partial<Price> = {
      stripePriceId: stripePrice.id,
      currency: stripePrice.currency,
      unitAmount: stripePrice.unit_amount ?? 0,
      nickname: stripePrice.nickname ?? '',
      active: stripePrice.active,
      type: stripePrice.type,
      interval: stripePrice.recurring?.interval,
      intervalCount: stripePrice.recurring?.interval_count,
      trialPeriodDays: stripePrice.recurring?.trial_period_days ?? undefined,
      usageType: stripePrice.recurring?.usage_type,
      package: pkg,
    };

    try {
      const existingPrice = await this.priceRepo.findOne({
        where: { stripePriceId: stripePrice.id },
      });

      if (existingPrice) {
        Object.assign(existingPrice, priceData);
        return this.priceRepo.save(existingPrice);
      } else {
        const newPrice = this.priceRepo.create(priceData);
        return this.priceRepo.save(newPrice);
      }
    } catch (error) {
      this.logger.error(
        `Failed to save or update price ${stripePrice.id}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        'Could not save or update price.',
      );
    }
  }
}
