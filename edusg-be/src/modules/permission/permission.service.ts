import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { SubscriptionService } from '../subscription/subscription.service';
import {
  PackagePermissions,
  UserPermissions,
  DEFAULT_PERMISSIONS
} from './interfaces/permission.interface';
import { isUnlimitedLimit } from './constants/permission.constants';

@Injectable()
export class PermissionService {
  private readonly logger = new Logger(PermissionService.name);

  constructor(
    private readonly subscriptionService: SubscriptionService,
  ) {}

  /**
   * Get all permissions for a user based on their subscription
   * @param userId - The user ID to get permissions for
   * @returns User permissions or default permissions if no subscription
   */
  async getUserPermissions(userId: string): Promise<UserPermissions> {
    this.logger.debug(`Getting permissions for user: ${userId}`);

    try {
      const subscriptionWithPackage = await this.subscriptionService.findByUserId(userId);
      
      if (!subscriptionWithPackage || !subscriptionWithPackage.package) {
        this.logger.debug(`No subscription or package found for user ${userId}, returning default permissions`);
        return DEFAULT_PERMISSIONS;
      }

      const { package: packageEntity, status } = subscriptionWithPackage;
      const packagePermissions = packageEntity.permissions as PackagePermissions;

      if (!packagePermissions) {
        this.logger.warn(`Package ${packageEntity.id} has no permissions defined, returning default permissions`);
        return DEFAULT_PERMISSIONS;
      }

      const userPermissions: UserPermissions = {
        features: packagePermissions.features || [],
        limits: packagePermissions.limits || {},
        access: packagePermissions.access || {},
        packageName: packageEntity.name,
        subscriptionStatus: status,
        hasActiveSubscription: status === 'active',
      };

      this.logger.debug(`Retrieved permissions for user ${userId}: ${JSON.stringify(userPermissions)}`);
      return userPermissions;

    } catch (error) {
      if (error instanceof NotFoundException) {
        this.logger.debug(`No subscription found for user ${userId}, returning default permissions`);
        return DEFAULT_PERMISSIONS;
      }
      
      this.logger.error(`Error getting permissions for user ${userId}: ${error.message}`);
      // Return default permissions on error to ensure system continues to work
      return DEFAULT_PERMISSIONS;
    }
  }

  /**
   * Check if user has a specific feature
   * @param userId - The user ID to check
   * @param feature - The feature name to check for
   * @returns True if user has the feature, false otherwise
   */
  async hasFeature(userId: string, feature: string): Promise<boolean> {
    this.logger.debug(`Checking if user ${userId} has feature: ${feature}`);
    
    const permissions = await this.getUserPermissions(userId);
    const hasFeature = permissions.features.includes(feature);
    
    this.logPermissionCheck(userId, 'feature', feature, hasFeature);
    return hasFeature;
  }

  /**
   * Get a specific limit value for a user
   * @param userId - The user ID to check
   * @param limitKey - The limit key to get value for
   * @returns The limit value or null if not defined
   */
  async getLimit(userId: string, limitKey: string): Promise<number | null> {
    this.logger.debug(`Getting limit ${limitKey} for user: ${userId}`);
    
    const permissions = await this.getUserPermissions(userId);
    const limitValue = permissions.limits[limitKey] ?? null;
    
    this.logger.debug(`Limit ${limitKey} for user ${userId}: ${limitValue}`);
    return limitValue;
  }

  /**
   * Check if user has a specific access permission
   * @param userId - The user ID to check
   * @param accessKey - The access key to check for
   * @returns True if user has access, false otherwise
   */
  async hasAccess(userId: string, accessKey: string): Promise<boolean> {
    this.logger.debug(`Checking if user ${userId} has access: ${accessKey}`);
    
    const permissions = await this.getUserPermissions(userId);
    const hasAccess = permissions.access[accessKey] === true;
    
    this.logPermissionCheck(userId, 'access', accessKey, hasAccess);
    return hasAccess;
  }

  /**
   * Check if user is within a specific limit
   * @param userId - The user ID to check
   * @param limitKey - The limit key to check against
   * @param currentUsage - The current usage count
   * @returns True if under limit, false if over limit or limit not defined
   */
  async checkLimit(userId: string, limitKey: string, currentUsage: number): Promise<boolean> {
    this.logger.debug(`Checking limit ${limitKey} for user ${userId}: ${currentUsage} usage`);
    
    const limitValue = await this.getLimit(userId, limitKey);
    
    if (limitValue === null) {
      this.logger.warn(`Limit ${limitKey} not defined for user ${userId}, denying access`);
      return false;
    }

    // Handle unlimited limit
    if (isUnlimitedLimit(limitValue)) {
      this.logger.debug(`Unlimited limit for user ${userId}, feature ${limitKey}. Always within limit.`);
      this.logPermissionCheck(userId, 'limit', `${limitKey}(${currentUsage}/unlimited)`, true);
      return true;
    }

    const withinLimit = currentUsage < limitValue;
    this.logPermissionCheck(userId, 'limit', `${limitKey}(${currentUsage}/${limitValue})`, withinLimit);

    return withinLimit;
  }

  /**
   * Check if user has an active subscription
   * @param userId - The user ID to check
   * @returns True if user has active subscription, false otherwise
   */
  async hasActiveSubscription(userId: string): Promise<boolean> {
    this.logger.debug(`Checking if user ${userId} has active subscription`);
    
    const permissions = await this.getUserPermissions(userId);
    const isActive = permissions.hasActiveSubscription;
    
    this.logPermissionCheck(userId, 'subscription', 'active', isActive);
    return isActive;
  }

  /**
   * Log permission check for audit purposes
   * @param userId - The user ID
   * @param type - The type of permission check
   * @param key - The permission key
   * @param granted - Whether permission was granted
   */
  private logPermissionCheck(userId: string, type: string, key: string, granted: boolean): void {
    const status = granted ? 'GRANTED' : 'DENIED';
    const logLevel = granted ? 'debug' : 'warn';
    
    this.logger[logLevel](`Permission ${status}: User ${userId} ${type} check for '${key}'`);
  }
}
