import { Module, forwardRef } from '@nestjs/common';
import { PermissionService } from './permission.service';
import { PermissionGuard } from './guards/permission.guard';
import { SubscriptionModule } from '../subscription/subscription.module';
import { UsageTrackingModule } from '../usage-tracking/usage-tracking.module';

@Module({
  imports: [
    forwardRef(() => SubscriptionModule), // Import SubscriptionModule to get SubscriptionService
    forwardRef(() => UsageTrackingModule), // Import UsageTrackingModule to get UsageTrackingService
  ],
  providers: [PermissionService, PermissionGuard],
  exports: [PermissionService, PermissionGuard], // Export for use in other modules
})
export class PermissionModule {}
