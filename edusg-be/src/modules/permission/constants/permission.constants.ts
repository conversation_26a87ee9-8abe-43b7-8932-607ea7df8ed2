/**
 * Permission system constants
 */

/**
 * Special value indicating unlimited limit
 * When a limit is set to this value, it means there's no restriction
 */
export const UNLIMITED_LIMIT = -1;

/**
 * Check if a limit value represents unlimited
 * @param limit - The limit value to check
 * @returns True if the limit is unlimited
 */
export function isUnlimitedLimit(limit: number | null): boolean {
  return limit === UNLIMITED_LIMIT;
}

/**
 * Format a limit value for display
 * @param limit - The limit value
 * @returns Formatted string representation
 */
export function formatLimit(limit: number | null): string {
  if (limit === null) return 'Not defined';
  if (limit === UNLIMITED_LIMIT) return 'Unlimited';
  return limit.toString();
}
