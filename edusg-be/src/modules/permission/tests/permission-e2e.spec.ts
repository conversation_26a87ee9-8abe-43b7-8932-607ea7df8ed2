import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';
import request from 'supertest';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { RedisService } from '../../redis/redis.service';
import { PermissionModule } from '../permission.module';
import { UsageTrackingModule } from '../../usage-tracking/usage-tracking.module';
import { SubscriptionModule } from '../../subscription/subscription.module';
import { AuthModule } from '../../auth/auth.module';
import { WorksheetModule } from '../../worksheet/worksheet.module';
import { ExamModule } from '../../exam/exam.module';
import { User } from '../../user/entities/user.entity';
import { School } from '../../school/entities/school.entity';
import { Package } from '../../subscription/entities/package.entity';
import { Subscription } from '../../subscription/entities/subscription.entity';
import { Worksheet } from '../../worksheet/entities/worksheet.entity';
import { Exam } from '../../exam/entities/exam.entity';

describe('Permission E2E Tests', () => {
  let app: INestApplication;
  let jwtService: JwtService;
  let userRepository: Repository<User>;
  let schoolRepository: Repository<School>;
  let packageRepository: Repository<Package>;
  let subscriptionRepository: Repository<Subscription>;
  let worksheetRepository: Repository<Worksheet>;
  let examRepository: Repository<Exam>;
  let redisService: RedisService;

  // Test data
  let testSchool: School;
  let basicPackage: Package;
  let premiumPackage: Package;
  let adminPackage: Package;
  let basicUser: User;
  let premiumUser: User;
  let adminUser: User;
  let basicUserToken: string;
  let premiumUserToken: string;
  let adminUserToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [User, School, Package, Subscription, Worksheet, Exam],
          synchronize: true,
          logging: false,
        }),
        MongooseModule.forRoot('mongodb://localhost:27017/edusg-test'),
        JwtModule.register({
          secret: 'test-secret-key',
          signOptions: { expiresIn: '1h' },
        }),
        AuthModule,
        PermissionModule,
        UsageTrackingModule,
        SubscriptionModule,
        WorksheetModule,
        ExamModule,
      ],
    })
    .overrideProvider(RedisService)
    .useValue({
      getClient: jest.fn().mockReturnValue({
        get: jest.fn().mockResolvedValue('0'),
        set: jest.fn().mockResolvedValue('OK'),
        incrby: jest.fn().mockResolvedValue(1),
        expire: jest.fn().mockResolvedValue(1),
      }),
    })
    .compile();

    app = moduleFixture.createNestApplication();
    jwtService = moduleFixture.get<JwtService>(JwtService);
    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    schoolRepository = moduleFixture.get<Repository<School>>(getRepositoryToken(School));
    packageRepository = moduleFixture.get<Repository<Package>>(getRepositoryToken(Package));
    subscriptionRepository = moduleFixture.get<Repository<Subscription>>(getRepositoryToken(Subscription));
    worksheetRepository = moduleFixture.get<Repository<Worksheet>>(getRepositoryToken(Worksheet));
    examRepository = moduleFixture.get<Repository<Exam>>(getRepositoryToken(Exam));
    redisService = moduleFixture.get<RedisService>(RedisService);

    await app.init();
    await setupTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
    await app.close();
  });

  beforeEach(async () => {
    // Reset Redis usage counters before each test
    const redisClient = redisService.getClient('COMMON_CACHE_NAME');
    jest.clearAllMocks();
    redisClient.get.mockResolvedValue('0');
    redisClient.incrby.mockResolvedValue(1);
  });

  async function setupTestData() {
    // Create test school
    testSchool = schoolRepository.create({
      name: 'Test School',
      address: '123 Test St',
      phone: '************',
      email: '<EMAIL>',
    });
    await schoolRepository.save(testSchool);

    // Create packages with different permission levels
    basicPackage = packageRepository.create({
      name: 'Basic Package',
      description: 'Basic subscription package',
      price: 9.99,
      permissions: {
        features: ['basic_worksheets'],
        limits: {
          maxStudents: 5,
          maxWorksheets: 3,
          maxExams: 1,
        },
        access: {
          adminPanel: false,
          analytics: false,
          advancedReports: false,
        },
      },
    });

    premiumPackage = packageRepository.create({
      name: 'Premium Package',
      description: 'Premium subscription package',
      price: 29.99,
      permissions: {
        features: ['basic_worksheets', 'exam_creation', 'student_management'],
        limits: {
          maxStudents: 100,
          maxWorksheets: 50,
          maxExams: 25,
        },
        access: {
          adminPanel: false,
          analytics: true,
          advancedReports: true,
        },
      },
    });

    adminPackage = packageRepository.create({
      name: 'Admin Package',
      description: 'Admin subscription package',
      price: 99.99,
      permissions: {
        features: ['basic_worksheets', 'exam_creation', 'student_management'],
        limits: {
          maxStudents: 1000,
          maxWorksheets: 500,
          maxExams: 100,
        },
        access: {
          adminPanel: true,
          analytics: true,
          advancedReports: true,
        },
      },
    });

    await packageRepository.save([basicPackage, premiumPackage, adminPackage]);

    // Create test users
    basicUser = userRepository.create({
      email: '<EMAIL>',
      password: 'hashedpassword',
      firstName: 'Basic',
      lastName: 'User',
      role: 'TEACHER',
      schoolId: testSchool.id,
    });

    premiumUser = userRepository.create({
      email: '<EMAIL>',
      password: 'hashedpassword',
      firstName: 'Premium',
      lastName: 'User',
      role: 'TEACHER',
      schoolId: testSchool.id,
    });

    adminUser = userRepository.create({
      email: '<EMAIL>',
      password: 'hashedpassword',
      firstName: 'Admin',
      lastName: 'User',
      role: 'ADMIN',
      schoolId: testSchool.id,
    });

    await userRepository.save([basicUser, premiumUser, adminUser]);

    // Create subscriptions
    const basicSubscription = subscriptionRepository.create({
      user_id: basicUser.id,
      package_id: basicPackage.id,
      status: 'active',
      start_date: new Date(),
      end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    });

    const premiumSubscription = subscriptionRepository.create({
      user_id: premiumUser.id,
      package_id: premiumPackage.id,
      status: 'active',
      start_date: new Date(),
      end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    });

    const adminSubscription = subscriptionRepository.create({
      user_id: adminUser.id,
      package_id: adminPackage.id,
      status: 'active',
      start_date: new Date(),
      end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    });

    await subscriptionRepository.save([basicSubscription, premiumSubscription, adminSubscription]);

    // Generate JWT tokens
    basicUserToken = jwtService.sign({
      sub: basicUser.id,
      email: basicUser.email,
      role: basicUser.role,
      schoolId: basicUser.schoolId,
    });

    premiumUserToken = jwtService.sign({
      sub: premiumUser.id,
      email: premiumUser.email,
      role: premiumUser.role,
      schoolId: premiumUser.schoolId,
    });

    adminUserToken = jwtService.sign({
      sub: adminUser.id,
      email: adminUser.email,
      role: adminUser.role,
      schoolId: adminUser.schoolId,
    });
  }

  async function cleanupTestData() {
    await subscriptionRepository.delete({});
    await packageRepository.delete({});
    await userRepository.delete({});
    await schoolRepository.delete({});
    await worksheetRepository.delete({});
    await examRepository.delete({});
  }

  describe('Worksheet Creation E2E', () => {
    it('should allow premium user to create worksheet', async () => {
      const response = await request(app.getHttpServer())
        .post('/worksheets')
        .set('Authorization', `Bearer ${premiumUserToken}`)
        .send({
          title: 'Test Worksheet',
          description: 'Test Description',
          schoolId: testSchool.id,
        });

      expect(response.status).toBe(201);
      expect(response.body.title).toBe('Test Worksheet');
    });

    it('should deny basic user when worksheet limit exceeded', async () => {
      // Mock Redis to return limit reached
      const redisClient = redisService.getClient('COMMON_CACHE_NAME');
      redisClient.get.mockResolvedValue('3'); // At limit

      const response = await request(app.getHttpServer())
        .post('/worksheets')
        .set('Authorization', `Bearer ${basicUserToken}`)
        .send({
          title: 'Test Worksheet',
          description: 'Test Description',
          schoolId: testSchool.id,
        });

      expect(response.status).toBe(429);
      expect(response.body.message).toContain('Usage limit exceeded');
    });
  });

  describe('Exam Creation E2E', () => {
    it('should allow premium user to create exam', async () => {
      // First create a worksheet
      const worksheetResponse = await request(app.getHttpServer())
        .post('/worksheets')
        .set('Authorization', `Bearer ${premiumUserToken}`)
        .send({
          title: 'Test Worksheet for Exam',
          description: 'Test Description',
          schoolId: testSchool.id,
        });

      const worksheetId = worksheetResponse.body.id;

      const response = await request(app.getHttpServer())
        .post('/exams')
        .set('Authorization', `Bearer ${premiumUserToken}`)
        .send({
          worksheetId: worksheetId,
          title: 'Test Exam',
        });

      expect(response.status).toBe(201);
      expect(response.body.title).toBe('Test Exam');
    });

    it('should deny basic user exam creation (no feature)', async () => {
      const response = await request(app.getHttpServer())
        .post('/exams')
        .set('Authorization', `Bearer ${basicUserToken}`)
        .send({
          worksheetId: 'some-worksheet-id',
          title: 'Test Exam',
        });

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Feature not available');
    });
  });

  describe('Admin Access E2E', () => {
    it('should allow admin to access sync endpoint', async () => {
      // First create a worksheet
      const worksheetResponse = await request(app.getHttpServer())
        .post('/worksheets')
        .set('Authorization', `Bearer ${adminUserToken}`)
        .send({
          title: 'Test Worksheet for Sync',
          description: 'Test Description',
          schoolId: testSchool.id,
        });

      const worksheetId = worksheetResponse.body.id;

      const response = await request(app.getHttpServer())
        .post(`/worksheets/${worksheetId}/sync`)
        .set('Authorization', `Bearer ${adminUserToken}`)
        .query({ dryRun: true });

      expect(response.status).toBe(200);
    });

    it('should deny non-admin access to sync endpoint', async () => {
      const response = await request(app.getHttpServer())
        .post('/worksheets/some-id/sync')
        .set('Authorization', `Bearer ${basicUserToken}`)
        .query({ dryRun: true });

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Access denied');
    });
  });

  describe('Usage Tracking E2E', () => {
    it('should track and enforce usage limits', async () => {
      const redisClient = redisService.getClient('COMMON_CACHE_NAME');
      
      // First request should succeed
      redisClient.get.mockResolvedValueOnce('2'); // Under limit
      
      const response1 = await request(app.getHttpServer())
        .post('/worksheets')
        .set('Authorization', `Bearer ${basicUserToken}`)
        .send({
          title: 'Worksheet 1',
          description: 'Test Description',
          schoolId: testSchool.id,
        });

      expect(response1.status).toBe(201);

      // Second request should fail (at limit)
      redisClient.get.mockResolvedValueOnce('3'); // At limit
      
      const response2 = await request(app.getHttpServer())
        .post('/worksheets')
        .set('Authorization', `Bearer ${basicUserToken}`)
        .send({
          title: 'Worksheet 2',
          description: 'Test Description',
          schoolId: testSchool.id,
        });

      expect(response2.status).toBe(429);
    });
  });
});
