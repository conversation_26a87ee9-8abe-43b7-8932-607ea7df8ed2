import { SetMetadata } from '@nestjs/common';

/**
 * Metadata key for required features
 */
export const REQUIRED_FEATURES_KEY = 'requiredFeatures';

/**
 * Decorator to specify required subscription features for accessing an endpoint
 * @param features - Array of feature names that the user must have
 * 
 * @example
 * ```typescript
 * @RequiresFeature('exam_creation')
 * async createExam() {
 *   // Only users with 'exam_creation' feature can access
 * }
 * 
 * @RequiresFeature('basic_worksheets', 'student_management')
 * async manageStudents() {
 *   // User must have both features
 * }
 * ```
 */
export const RequiresFeature = (...features: string[]) => 
  SetMetadata(REQUIRED_FEATURES_KEY, features);
