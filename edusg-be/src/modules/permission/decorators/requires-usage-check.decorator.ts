import { SetMetadata } from '@nestjs/common';
import { UsagePeriod } from '../../usage-tracking/interfaces/usage-tracking.interface';

/**
 * Metadata key for required usage checks
 */
export const REQUIRED_USAGE_CHECK_KEY = 'requiredUsageCheck';

/**
 * Interface for usage check configuration
 */
export interface UsageCheckConfig {
  /**
   * The feature to check usage for (e.g., 'maxWorksheets', 'maxStudents')
   */
  feature: string;
  
  /**
   * The time period for the usage check (default: 'daily')
   */
  period?: UsagePeriod;
}

/**
 * Decorator to specify required usage limit checks for accessing an endpoint
 * This decorator works with the PermissionGuard to enforce usage limits
 * Multiple usage checks can be applied to the same endpoint
 * 
 * @param feature - The feature name to check usage for
 * @param period - The time period for the check (default: 'daily')
 * 
 * @example
 * ```typescript
 * @RequiresUsageCheck('maxWorksheets')
 * async createWorksheet() {
 *   // Only users within their worksheet limit can access
 * }
 * 
 * @RequiresUsageCheck('maxStudents', 'monthly')
 * async addStudent() {
 *   // Check monthly student limit
 * }
 * 
 * @RequiresUsageCheck('maxWorksheets', 'daily')
 * @RequiresUsageCheck('maxQuestionsPerWorksheet', 'daily')
 * async createWorksheetWithQuestions() {
 *   // Check both worksheet and question limits
 * }
 * ```
 */
export const RequiresUsageCheck = (feature: string, period: UsagePeriod = 'daily') => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const config: UsageCheckConfig = { feature, period };
    
    // Get existing usage check configurations or initialize empty array
    const existingUsageChecks: UsageCheckConfig[] = 
      Reflect.getMetadata(REQUIRED_USAGE_CHECK_KEY, descriptor.value) || [];
    
    // Add the new usage check to the array
    const updatedUsageChecks = [...existingUsageChecks, config];
    
    // Set the updated array as metadata
    SetMetadata(REQUIRED_USAGE_CHECK_KEY, updatedUsageChecks)(target, propertyKey, descriptor);
  };
};
