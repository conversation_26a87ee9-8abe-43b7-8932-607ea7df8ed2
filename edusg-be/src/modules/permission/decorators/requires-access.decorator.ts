import { SetMetadata } from '@nestjs/common';

/**
 * Metadata key for required access permissions
 */
export const REQUIRED_ACCESS_KEY = 'requiredAccess';

/**
 * Decorator to specify required subscription access permissions for accessing an endpoint
 * @param accessKeys - Array of access keys that the user must have
 * 
 * @example
 * ```typescript
 * @RequiresAccess('adminPanel')
 * async adminFunction() {
 *   // Only users with 'adminPanel' access can use this
 * }
 * 
 * @RequiresAccess('analytics', 'advancedReports')
 * async getAdvancedAnalytics() {
 *   // User must have both access permissions
 * }
 * ```
 */
export const RequiresAccess = (...accessKeys: string[]) => 
  SetMetadata(REQUIRED_ACCESS_KEY, accessKeys);
