import { Injectable, CanActivate, ExecutionContext, Logger, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PermissionService } from '../permission.service';
import { UsageTrackingService } from '../../usage-tracking/services/usage-tracking.service';
import { REQUIRED_FEATURES_KEY } from '../decorators/requires-feature.decorator';
import { REQUIRED_ACCESS_KEY } from '../decorators/requires-access.decorator';
import { REQUIRED_USAGE_CHECK_KEY, UsageCheckConfig } from '../decorators/requires-usage-check.decorator';
import { UserContext } from '../../auth/services/rbac.service';
import { RbacExceptions } from '../../auth/exceptions/rbac-exceptions';
import { TooManyRequestsException } from '../exceptions/too-many-requests.exception';

@Injectable()
export class PermissionGuard implements CanActivate {
  private readonly logger = new Logger(PermissionGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly permissionService: PermissionService,
    private readonly usageTrackingService: UsageTrackingService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: UserContext = request.user;
    const endpoint = `${request.method} ${request.route?.path || request.url}`;

    // User must be authenticated (should be handled by AuthGuard first)
    if (!user) {
      this.logger.warn(`Permission check failed: No user context for ${endpoint}`);
      return false;
    }

    // Get required features, access permissions, and usage checks from decorators
    const requiredFeatures = this.reflector.get<string[]>(
      REQUIRED_FEATURES_KEY,
      context.getHandler(),
    );

    const requiredAccess = this.reflector.get<string[]>(
      REQUIRED_ACCESS_KEY,
      context.getHandler(),
    );

    const usageCheckConfigs = this.reflector.get<UsageCheckConfig[]>(
      REQUIRED_USAGE_CHECK_KEY,
      context.getHandler(),
    );

    // If no permission or usage requirements, allow access
    if ((!requiredFeatures || requiredFeatures.length === 0) &&
        (!requiredAccess || requiredAccess.length === 0) &&
        (!usageCheckConfigs || usageCheckConfigs.length === 0)) {
      return true;
    }

    try {
      // Check required features
      if (requiredFeatures && requiredFeatures.length > 0) {
        for (const feature of requiredFeatures) {
          const hasFeature = await this.permissionService.hasFeature(user.sub, feature);
          if (!hasFeature) {
            this.logger.warn(
              `Permission denied: User ${user.sub} lacks required feature '${feature}' for ${endpoint}`
            );
            throw RbacExceptions.createForbiddenException(
              `Access denied: Required feature '${feature}' not found in subscription`,
              'PERMISSION_FEATURE_DENIED'
            );
          }
        }
      }

      // Check required access permissions
      if (requiredAccess && requiredAccess.length > 0) {
        for (const accessKey of requiredAccess) {
          const hasAccess = await this.permissionService.hasAccess(user.sub, accessKey);
          if (!hasAccess) {
            this.logger.warn(
              `Permission denied: User ${user.sub} lacks required access '${accessKey}' for ${endpoint}`
            );
            throw RbacExceptions.createForbiddenException(
              `Access denied: Required access permission '${accessKey}' not found`,
              'PERMISSION_ACCESS_DENIED'
            );
          }
        }
      }

      // Check usage limits if required
      if (usageCheckConfigs && usageCheckConfigs.length > 0) {
        for (const usageCheckConfig of usageCheckConfigs) {
          const { feature, period = 'daily' } = usageCheckConfig;

          this.logger.debug(
            `Checking usage limit for user ${user.sub}, feature ${feature}, period ${period}`
          );

          const limitCheck = await this.usageTrackingService.checkUsageLimit(
            user.sub,
            feature,
            period
          );

          if (!limitCheck.withinLimit) {
            this.logger.warn(
              `Usage limit exceeded: User ${user.sub} has reached limit for '${feature}' (${limitCheck.current}/${limitCheck.limit}) for ${endpoint}`
            );
            throw new TooManyRequestsException({
              message: `Usage limit exceeded for ${feature}. Current: ${limitCheck.current}/${limitCheck.limit} (${period})`,
              error: 'Too Many Requests',
              statusCode: 429,
              code: 'USAGE_LIMIT_EXCEEDED',
              timestamp: new Date().toISOString(),
              details: {
                feature,
                period,
                current: limitCheck.current,
                limit: limitCheck.limit,
                remaining: limitCheck.remaining,
              }
            });
          }

          this.logger.debug(
            `Usage check passed: User ${user.sub} is within limit for '${feature}' (${limitCheck.current}/${limitCheck.limit})`
          );
        }
      }

      this.logger.debug(
        `All checks passed: User ${user.sub} has all required permissions and is within usage limits for ${endpoint}`
      );
      return true;

    } catch (error) {
      this.logger.error(
        `Permission check error for user ${user.sub} on ${endpoint}: ${error.message}`
      );

      // Re-throw permission and usage limit exceptions to be handled by NestJS
      if (error instanceof ForbiddenException ||
          error instanceof TooManyRequestsException) {
        throw error;
      }

      // For unexpected errors, deny access safely
      throw RbacExceptions.createForbiddenException(
        'Permission check failed due to system error',
        'PERMISSION_SYSTEM_ERROR'
      );
    }
  }
}
