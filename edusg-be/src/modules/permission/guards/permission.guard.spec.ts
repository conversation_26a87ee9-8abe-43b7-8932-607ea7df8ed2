import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PermissionGuard } from './permission.guard';
import { PermissionService } from '../permission.service';
import { UsageTrackingService } from '../../usage-tracking/services/usage-tracking.service';
import { TooManyRequestsException } from '../exceptions/too-many-requests.exception';
import { RbacExceptions } from '../../auth/exceptions/rbac-exceptions';

describe('PermissionGuard', () => {
  let guard: PermissionGuard;
  let permissionService: jest.Mocked<PermissionService>;
  let usageTrackingService: jest.Mocked<UsageTrackingService>;
  let reflector: jest.Mocked<Reflector>;

  const mockExecutionContext = {
    switchToHttp: () => ({
      getRequest: () => ({
        user: {
          sub: 'user-123',
          email: '<EMAIL>',
          role: 'TEACHER',
          schoolId: 'school-456'
        },
        method: 'POST',
        url: '/api/test'
      })
    }),
    getHandler: jest.fn(),
    getClass: jest.fn()
  } as unknown as ExecutionContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PermissionGuard,
        {
          provide: PermissionService,
          useValue: {
            hasFeature: jest.fn(),
            hasAccess: jest.fn()
          }
        },
        {
          provide: UsageTrackingService,
          useValue: {
            checkUsageLimit: jest.fn()
          }
        },
        {
          provide: Reflector,
          useValue: {
            get: jest.fn()
          }
        }
      ]
    }).compile();

    guard = module.get<PermissionGuard>(PermissionGuard);
    permissionService = module.get(PermissionService);
    usageTrackingService = module.get(UsageTrackingService);
    reflector = module.get(Reflector);
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  it('should allow access when no decorators are present', async () => {
    reflector.get.mockReturnValue(undefined);

    const result = await guard.canActivate(mockExecutionContext);

    expect(result).toBe(true);
  });

  it('should check features when @RequiresFeature is present', async () => {
    reflector.get
      .mockReturnValueOnce(['feature1']) // REQUIRED_FEATURES_KEY
      .mockReturnValueOnce(undefined) // REQUIRED_ACCESS_KEY
      .mockReturnValueOnce(undefined); // REQUIRED_USAGE_CHECK_KEY

    permissionService.hasFeature.mockResolvedValue(true);

    const result = await guard.canActivate(mockExecutionContext);

    expect(result).toBe(true);
    expect(permissionService.hasFeature).toHaveBeenCalledWith('user-123', 'feature1');
  });

  it('should throw ForbiddenException when features are not available', async () => {
    reflector.get
      .mockReturnValueOnce(['feature1']) // REQUIRED_FEATURES_KEY
      .mockReturnValueOnce(undefined) // REQUIRED_ACCESS_KEY
      .mockReturnValueOnce(undefined); // REQUIRED_USAGE_CHECK_KEY

    permissionService.hasFeature.mockResolvedValue(false);

    await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(ForbiddenException);
  });

  it('should check usage limits when @RequiresUsageCheck is present', async () => {
    reflector.get
      .mockReturnValueOnce(undefined) // REQUIRED_FEATURES_KEY
      .mockReturnValueOnce(undefined) // REQUIRED_ACCESS_KEY
      .mockReturnValueOnce({ feature: 'maxWorksheets', period: 'daily' }); // REQUIRED_USAGE_CHECK_KEY

    usageTrackingService.checkUsageLimit.mockResolvedValue({
      withinLimit: true,
      current: 5,
      limit: 10,
      remaining: 5,
      period: 'daily',
      feature: 'maxWorksheets'
    });

    const result = await guard.canActivate(mockExecutionContext);

    expect(result).toBe(true);
    expect(usageTrackingService.checkUsageLimit).toHaveBeenCalledWith('user-123', 'maxWorksheets', 'daily');
  });

  it('should throw TooManyRequestsException when usage limit is exceeded', async () => {
    reflector.get
      .mockReturnValueOnce(undefined) // REQUIRED_FEATURES_KEY
      .mockReturnValueOnce(undefined) // REQUIRED_ACCESS_KEY
      .mockReturnValueOnce({ feature: 'maxWorksheets', period: 'daily' }); // REQUIRED_USAGE_CHECK_KEY

    usageTrackingService.checkUsageLimit.mockResolvedValue({
      withinLimit: false,
      current: 10,
      limit: 10,
      remaining: 0,
      period: 'daily',
      feature: 'maxWorksheets'
    });

    await expect(guard.canActivate(mockExecutionContext)).rejects.toThrow(TooManyRequestsException);
  });

  it('should handle all checks when multiple decorators are present', async () => {
    reflector.get
      .mockReturnValueOnce(['feature1']) // REQUIRED_FEATURES_KEY
      .mockReturnValueOnce(['access1']) // REQUIRED_ACCESS_KEY
      .mockReturnValueOnce({ feature: 'maxWorksheets', period: 'daily' }); // REQUIRED_USAGE_CHECK_KEY

    permissionService.hasFeature.mockResolvedValue(true);
    permissionService.hasAccess.mockResolvedValue(true);
    usageTrackingService.checkUsageLimit.mockResolvedValue({
      withinLimit: true,
      current: 5,
      limit: 10,
      remaining: 5,
      period: 'daily',
      feature: 'maxWorksheets'
    });

    const result = await guard.canActivate(mockExecutionContext);

    expect(result).toBe(true);
    expect(permissionService.hasFeature).toHaveBeenCalledWith('user-123', 'feature1');
    expect(permissionService.hasAccess).toHaveBeenCalledWith('user-123', 'access1');
    expect(usageTrackingService.checkUsageLimit).toHaveBeenCalledWith('user-123', 'maxWorksheets', 'daily');
  });
});
