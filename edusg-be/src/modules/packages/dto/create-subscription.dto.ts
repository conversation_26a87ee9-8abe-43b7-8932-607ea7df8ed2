import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUrl } from 'class-validator';

export class CreateSubscriptionDto {
  @ApiProperty({
    description: 'The ID of the package to subscribe to.',
    example: '5deab9d0-fbcf-4975-8f79-dd2496677103',
  })
  @IsString()
  @IsNotEmpty()
  packageId: string;

  @ApiProperty({
    description: 'The URL to redirect to after a successful checkout. Should contain {CHECKOUT_SESSION_ID}.',
    example: 'http://localhost:3000/pricing/success?session_id={CHECKOUT_SESSION_ID}',
  })
  @IsUrl({ require_tld: false })
  @IsNotEmpty()
  successUrl: string;

  @ApiProperty({
    description: 'The URL to redirect to after a cancelled checkout.',
    example: 'http://localhost:3000/pricing',
  })
  @IsUrl({ require_tld: false })
  @IsNotEmpty()
  cancelUrl: string;
} 