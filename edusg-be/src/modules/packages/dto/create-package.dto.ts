import { IsString, IsNotEmpty, IsOptional, IsUrl, IsArray, ValidateNested, IsBoolean, IsNumber, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class CreatePriceForPackageDto {
  @ApiProperty({
    description: 'Currency code (ISO 4217)',
    example: 'usd',
  })
  @IsString()
  @IsNotEmpty()
  currency: string;

  @ApiProperty({
    description: 'Price amount in smallest currency unit (e.g. cents for USD)',
    example: 2000,
  })
  @IsNotEmpty()
  unitAmount: number;

  @ApiProperty({
    description: 'Nickname for the price',
    example: 'Monthly Premium',
    required: false,
  })
  @IsOptional()
  @IsString()
  nickname?: string;

  @ApiProperty({
    description: 'Whether the price is active',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  active?: boolean;

  @ApiProperty({
    description: 'Type of pricing',
    enum: ['one_time', 'recurring'],
    example: 'recurring',
  })
  @IsEnum(['one_time', 'recurring'])
  type: 'one_time' | 'recurring';

  @ApiProperty({
    description: 'Billing interval for recurring prices (required if type is recurring)',
    enum: ['day', 'week', 'month', 'year'],
    example: 'month',
    required: false,
  })
  @IsOptional()
  @IsEnum(['day', 'week', 'month', 'year'])
  interval?: 'day' | 'week' | 'month' | 'year';

  @ApiProperty({
    description: 'Number of intervals between billings (required if type is recurring)',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  intervalCount?: number;

  @ApiProperty({
    description: 'Trial period in days',
    example: 7,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  trialPeriodDays?: number;

  @ApiProperty({
    description: 'Usage type for recurring prices',
    enum: ['licensed', 'metered'],
    example: 'licensed',
    required: false,
  })
  @IsOptional()
  @IsEnum(['licensed', 'metered'])
  usageType?: 'licensed' | 'metered';
}

export class CreatePackageDto {
  @ApiProperty({
    description: 'The name of the package',
    example: 'Premium Plan',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Description of the package',
    example: 'Premium subscription plan with all features',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Image URL for the package',
    example: 'https://example.com/package-image.png',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  image?: string;

  @ApiProperty({
    description: 'Stripe Product ID (optional, will be created if not provided)',
    example: 'prod_1234567890',
    required: false,
  })
  @IsOptional()
  @IsString()
  stripeProductId?: string;

  @ApiProperty({
    description: 'Array of prices to create for this package',
    type: [CreatePriceForPackageDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreatePriceForPackageDto)
  prices?: CreatePriceForPackageDto[];
}
