/**
 * Event interfaces for monitoring and analytics system
 */

export interface BaseMonitoringEvent {
  eventId: string;
  timestamp: Date;
  userId?: string;
  worksheetId?: string;
  sessionId?: string;
}

export interface QuestionSelectionEvent extends BaseMonitoringEvent {
  type: 'question_selection';
  source: 'pool' | 'ai_generation';
  selectionParams: {
    subject?: string;
    parentSubject?: string;
    childSubject?: string;
    questionType?: string;
    gradeLevel?: string;
    language?: string;
    difficulty?: string;
    requestedCount: number;
  };
  result: {
    returnedCount: number;
    selectedQuestions: Array<{
      questionId: string;
      questionType: string;
      difficulty: string;
      subject: string;
      lastUsed?: Date;
      usageCount?: number;
    }>;
    executionTimeMs: number;
    cacheStatus: 'hit' | 'miss' | 'disabled';
    fallbackTriggered: boolean;
  };
}

export interface QuestionGenerationEvent extends BaseMonitoringEvent {
  type: 'question_generation';
  generationParams: {
    subject: string;
    questionType: string;
    gradeLevel: string;
    requestedCount: number;
    aiProvider: string;
  };
  result: {
    generatedCount: number;
    executionTimeMs: number;
    success: boolean;
    errorMessage?: string;
    fallbacksUsed: string[];
  };
}

export interface ValidationAttemptEvent extends BaseMonitoringEvent {
  type: 'validation_attempt';
  validationParams: {
    questionId: string;
    questionType: string;
    validationType: 'format' | 'content' | 'appropriateness' | 'structure' | 'cultural';
  };
  result: {
    isValid: boolean;
    score: number;
    issues: Array<{
      type: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      message: string;
    }>;
    executionTimeMs: number;
  };
}

export interface CacheInteractionEvent extends BaseMonitoringEvent {
  type: 'cache_interaction';
  operation: 'get' | 'set' | 'delete' | 'clear';
  cacheType: 'question_pool' | 'worksheet_document' | 'query_cache';
  cacheKey: string;
  result: {
    hit: boolean;
    executionTimeMs: number;
    dataSize?: number;
    ttl?: number;
  };
}

export interface WorksheetGenerationEvent extends BaseMonitoringEvent {
  type: 'worksheet_generation';
  generationParams: {
    selectionStrategy: 'pool-only' | 'ai-only' | 'hybrid' | 'mixed';
    totalQuestionsRequested: number;
    questionTypes: string[];
    subjects: string[];
    gradeLevel: string;
  };
  result: {
    totalQuestionsGenerated: number;
    questionsFromPool: number;
    questionsFromAI: number;
    executionTimeMs: number;
    success: boolean;
    validationResults: {
      totalValidated: number;
      validationSuccessRate: number;
    };
    distributionAdherence: {
      difficultyDistribution: Record<string, number>;
      typeDistribution: Record<string, number>;
      targetAchieved: boolean;
    };
  };
}

export interface DistributionAnalysisEvent extends BaseMonitoringEvent {
  type: 'distribution_analysis';
  analysisParams: {
    targetDistribution: {
      difficulty: Record<string, number>;
      questionTypes: Record<string, number>;
    };
    actualDistribution: {
      difficulty: Record<string, number>;
      questionTypes: Record<string, number>;
    };
  };
  result: {
    adherenceScore: number;
    deviations: Array<{
      category: 'difficulty' | 'question_type';
      expected: number;
      actual: number;
      deviation: number;
    }>;
  };
}

export type MonitoringEvent = 
  | QuestionSelectionEvent 
  | QuestionGenerationEvent 
  | ValidationAttemptEvent 
  | CacheInteractionEvent 
  | WorksheetGenerationEvent
  | DistributionAnalysisEvent;

/**
 * Event emission interface for services to implement
 */
export interface EventEmitter {
  emitEvent(event: MonitoringEvent): Promise<void>;
}

/**
 * Aggregated metrics interfaces
 */
export interface PoolUtilizationMetrics {
  timeframe: 'hourly' | 'daily' | 'weekly' | 'monthly';
  timestamp: Date;
  totalUniqueQuestionsInPool: number;
  uniqueQuestionsUsed: number;
  utilizationRate: number;
  subjectBreakdown: Record<string, {
    totalQuestions: number;
    usedQuestions: number;
    utilizationRate: number;
  }>;
}

export interface QuestionReuseMetrics {
  timeframe: 'hourly' | 'daily' | 'weekly' | 'monthly';
  timestamp: Date;
  averageReuseFrequency: number;
  mostReusedQuestions: Array<{
    questionId: string;
    usageCount: number;
    questionType: string;
    subject: string;
  }>;
  reuseDistribution: Record<string, number>; // usage count -> number of questions
}

export interface GenerationTimeMetrics {
  timeframe: 'hourly' | 'daily' | 'weekly' | 'monthly';
  timestamp: Date;
  poolSelectionTime: {
    average: number;
    p50: number;
    p95: number;
    p99: number;
  };
  aiGenerationTime: {
    average: number;
    p50: number;
    p95: number;
    p99: number;
  };
  comparisonRatio: number; // pool time / ai time
}

export interface ValidationMetrics {
  timeframe: 'hourly' | 'daily' | 'weekly' | 'monthly';
  timestamp: Date;
  totalValidations: number;
  successfulValidations: number;
  successRate: number;
  issueBreakdown: Record<string, {
    count: number;
    severity: Record<string, number>;
  }>;
}

export interface CacheMetrics {
  timeframe: 'hourly' | 'daily' | 'weekly' | 'monthly';
  timestamp: Date;
  cacheType: string;
  totalRequests: number;
  hits: number;
  misses: number;
  hitRate: number;
  averageResponseTime: number;
}
