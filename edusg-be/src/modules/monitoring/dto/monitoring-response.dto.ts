import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SubjectBreakdownDto {
  @ApiProperty({ description: 'Total questions in this subject' })
  totalQuestions: number;

  @ApiProperty({ description: 'Number of questions used from this subject' })
  usedQuestions: number;

  @ApiProperty({ description: 'Utilization rate for this subject (0-1)' })
  utilizationRate: number;
}

export class PoolUtilizationResponseDto {
  @ApiProperty({ description: 'Timeframe for the metrics' })
  timeframe: string;

  @ApiProperty({ description: 'Timestamp when metrics were calculated' })
  timestamp: Date;

  @ApiProperty({ description: 'Total unique questions in the pool' })
  totalUniqueQuestionsInPool: number;

  @ApiProperty({ description: 'Number of unique questions used' })
  uniqueQuestionsUsed: number;

  @ApiProperty({ description: 'Overall utilization rate (0-1)' })
  utilizationRate: number;

  @ApiProperty({
    description: 'Breakdown by subject',
    type: 'object',
    additionalProperties: true
  })
  subjectBreakdown: Record<string, SubjectBreakdownDto>;
}

export class MostReusedQuestionDto {
  @ApiProperty({ description: 'Question ID' })
  questionId: string;

  @ApiProperty({ description: 'Number of times this question was used' })
  usageCount: number;

  @ApiProperty({ description: 'Type of the question' })
  questionType: string;

  @ApiProperty({ description: 'Subject of the question' })
  subject: string;
}

export class QuestionReuseResponseDto {
  @ApiProperty({ description: 'Timeframe for the metrics' })
  timeframe: string;

  @ApiProperty({ description: 'Timestamp when metrics were calculated' })
  timestamp: Date;

  @ApiProperty({ description: 'Average reuse frequency across all questions' })
  averageReuseFrequency: number;

  @ApiProperty({ 
    description: 'Top 10 most reused questions',
    type: [MostReusedQuestionDto]
  })
  mostReusedQuestions: MostReusedQuestionDto[];

  @ApiProperty({ 
    description: 'Distribution of reuse counts',
    type: 'object',
    additionalProperties: { type: 'number' }
  })
  reuseDistribution: Record<string, number>;
}

export class TimeStatsDto {
  @ApiProperty({ description: 'Average time in milliseconds' })
  average: number;

  @ApiProperty({ description: '50th percentile time in milliseconds' })
  p50: number;

  @ApiProperty({ description: '95th percentile time in milliseconds' })
  p95: number;

  @ApiProperty({ description: '99th percentile time in milliseconds' })
  p99: number;
}

export class GenerationTimeResponseDto {
  @ApiProperty({ description: 'Timeframe for the metrics' })
  timeframe: string;

  @ApiProperty({ description: 'Timestamp when metrics were calculated' })
  timestamp: Date;

  @ApiProperty({ description: 'Pool selection time statistics' })
  poolSelectionTime: TimeStatsDto;

  @ApiProperty({ description: 'AI generation time statistics' })
  aiGenerationTime: TimeStatsDto;

  @ApiProperty({ description: 'Ratio of pool time to AI time' })
  comparisonRatio: number;
}

export class IssueBreakdownDto {
  @ApiProperty({ description: 'Total count of this issue type' })
  count: number;

  @ApiProperty({ 
    description: 'Breakdown by severity level',
    type: 'object',
    additionalProperties: { type: 'number' }
  })
  severity: Record<string, number>;
}

export class ValidationResponseDto {
  @ApiProperty({ description: 'Timeframe for the metrics' })
  timeframe: string;

  @ApiProperty({ description: 'Timestamp when metrics were calculated' })
  timestamp: Date;

  @ApiProperty({ description: 'Total number of validations performed' })
  totalValidations: number;

  @ApiProperty({ description: 'Number of successful validations' })
  successfulValidations: number;

  @ApiProperty({ description: 'Success rate (0-1)' })
  successRate: number;

  @ApiProperty({
    description: 'Breakdown of issues by type',
    type: 'object',
    additionalProperties: true
  })
  issueBreakdown: Record<string, IssueBreakdownDto>;
}

export class CacheResponseDto {
  @ApiProperty({ description: 'Timeframe for the metrics' })
  timeframe: string;

  @ApiProperty({ description: 'Timestamp when metrics were calculated' })
  timestamp: Date;

  @ApiProperty({ description: 'Cache type' })
  cacheType: string;

  @ApiProperty({ description: 'Total number of cache requests' })
  totalRequests: number;

  @ApiProperty({ description: 'Number of cache hits' })
  hits: number;

  @ApiProperty({ description: 'Number of cache misses' })
  misses: number;

  @ApiProperty({ description: 'Cache hit rate (0-1)' })
  hitRate: number;

  @ApiProperty({ description: 'Average response time in milliseconds' })
  averageResponseTime: number;
}

export class DashboardMetricsResponseDto {
  @ApiProperty({ description: 'Pool utilization metrics' })
  poolUtilization: PoolUtilizationResponseDto;

  @ApiProperty({ description: 'Question reuse metrics' })
  questionReuse: QuestionReuseResponseDto;

  @ApiProperty({ description: 'Generation time comparison metrics' })
  generationTimeComparison: GenerationTimeResponseDto;

  @ApiProperty({ description: 'Validation success metrics' })
  validationMetrics: ValidationResponseDto;

  @ApiProperty({
    description: 'Cache performance metrics',
    type: 'object',
    additionalProperties: true
  })
  cacheMetrics: {
    questionPool: CacheResponseDto;
    worksheetDocument: CacheResponseDto;
  };

  @ApiProperty({ description: 'When the metrics were generated' })
  generatedAt: Date;

  @ApiProperty({ description: 'Timeframe used for aggregation' })
  timeframe: string;

  @ApiProperty({ 
    description: 'Date range for the metrics',
    type: 'object',
    properties: {
      startDate: { type: 'string', format: 'date-time' },
      endDate: { type: 'string', format: 'date-time' }
    }
  })
  dateRange: {
    startDate: Date;
    endDate: Date;
  };
}
