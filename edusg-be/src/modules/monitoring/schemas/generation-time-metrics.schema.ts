import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * Generation time comparison metrics schema
 */
@Schema({ 
  collection: 'generation_time_metrics',
  timestamps: true
})
export class GenerationTimeMetrics extends Document {
  @Prop({ required: true, index: true })
  timeframe: string; // 'hourly' | 'daily' | 'weekly' | 'monthly'

  @Prop({ required: true, index: true })
  timestamp: Date;

  @Prop({
    type: {
      average: { type: Number, required: true },
      p50: { type: Number, required: true },
      p95: { type: Number, required: true },
      p99: { type: Number, required: true }
    },
    required: true
  })
  poolSelectionTime: {
    average: number;
    p50: number;
    p95: number;
    p99: number;
  };

  @Prop({
    type: {
      average: { type: Number, required: true },
      p50: { type: Number, required: true },
      p95: { type: Number, required: true },
      p99: { type: Number, required: true }
    },
    required: true
  })
  aiGenerationTime: {
    average: number;
    p50: number;
    p95: number;
    p99: number;
  };

  @Prop({ required: true })
  comparisonRatio: number; // pool time / ai time

  @Prop({ default: Date.now })
  createdAt: Date;
}

export const GenerationTimeMetricsSchema = SchemaFactory.createForClass(GenerationTimeMetrics);

// Create compound indexes
GenerationTimeMetricsSchema.index({ timeframe: 1, timestamp: -1 });
GenerationTimeMetricsSchema.index({ timestamp: -1 });
