import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * Cache performance metrics schema
 */
@Schema({ 
  collection: 'cache_metrics',
  timestamps: true
})
export class CacheMetrics extends Document {
  @Prop({ required: true, index: true })
  timeframe: string; // 'hourly' | 'daily' | 'weekly' | 'monthly'

  @Prop({ required: true, index: true })
  timestamp: Date;

  @Prop({ required: true, index: true })
  cacheType: string;

  @Prop({ required: true })
  totalRequests: number;

  @Prop({ required: true })
  hits: number;

  @Prop({ required: true })
  misses: number;

  @Prop({ required: true })
  hitRate: number;

  @Prop({ required: true })
  averageResponseTime: number;

  @Prop({ default: Date.now })
  createdAt: Date;
}

export const CacheMetricsSchema = SchemaFactory.createForClass(CacheMetrics);

// Create compound indexes
CacheMetricsSchema.index({ timeframe: 1, timestamp: -1 });
CacheMetricsSchema.index({ cacheType: 1, timestamp: -1 });
CacheMetricsSchema.index({ timestamp: -1 });
