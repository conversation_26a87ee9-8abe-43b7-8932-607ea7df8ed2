import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * Validation success rate metrics schema
 */
@Schema({ 
  collection: 'validation_metrics',
  timestamps: true
})
export class ValidationMetrics extends Document {
  @Prop({ required: true, index: true })
  timeframe: string; // 'hourly' | 'daily' | 'weekly' | 'monthly'

  @Prop({ required: true, index: true })
  timestamp: Date;

  @Prop({ required: true })
  totalValidations: number;

  @Prop({ required: true })
  successfulValidations: number;

  @Prop({ required: true })
  successRate: number;

  @Prop({ type: Object })
  issueBreakdown: Record<string, {
    count: number;
    severity: Record<string, number>;
  }>;

  @Prop({ default: Date.now })
  createdAt: Date;
}

export const ValidationMetricsSchema = SchemaFactory.createForClass(ValidationMetrics);

// Create compound indexes
ValidationMetricsSchema.index({ timeframe: 1, timestamp: -1 });
ValidationMetricsSchema.index({ timestamp: -1 });
