import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * Raw monitoring event storage schema
 */
@Schema({ 
  collection: 'monitoring_events',
  timestamps: true,
  // TTL index to automatically delete old events after 90 days
  expires: 90 * 24 * 60 * 60 // 90 days in seconds
})
export class MonitoringEvent extends Document {
  @Prop({ required: true, index: true })
  eventId: string;

  @Prop({ required: true, index: true })
  type: string;

  @Prop({ required: true, index: true })
  timestamp: Date;

  @Prop({ index: true })
  userId?: string;

  @Prop({ index: true })
  worksheetId?: string;

  @Prop()
  sessionId?: string;

  @Prop({ type: Object })
  eventData: Record<string, any>;

  @Prop({ default: Date.now })
  createdAt: Date;
}

export const MonitoringEventSchema = SchemaFactory.createForClass(MonitoringEvent);

// Create compound indexes for efficient querying
MonitoringEventSchema.index({ type: 1, timestamp: -1 });
MonitoringEventSchema.index({ userId: 1, timestamp: -1 });
MonitoringEventSchema.index({ worksheetId: 1, timestamp: -1 });
MonitoringEventSchema.index({ timestamp: -1 }); // For time-based queries
