/**
 * Interfaces for content validation
 */

/**
 * Validation result for a single question
 */
export interface ValidationResult {
  isValid: boolean;
  score: number; // Validation score (0-1, where 1 is perfect)
  issues: ValidationIssue[];
  metadata?: Record<string, any>;
}

/**
 * Validation issue details
 */
export interface ValidationIssue {
  type: 'format' | 'content' | 'appropriateness' | 'structure' | 'cultural_sensitivity';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  field?: string; // Which field has the issue
  suggestion?: string; // Suggested fix
}

/**
 * Batch validation result
 */
export interface BatchValidationResult {
  results: ValidationResult[];
  summary: {
    totalValidated: number;
    passed: number;
    failed: number;
    successRate: number;
    averageScore: number;
  };
  processingTime: number; // Time taken in milliseconds
}

/**
 * Validation configuration
 */
export interface ValidationConfig {
  enableFormatValidation: boolean;
  enableContentValidation: boolean;
  enableAppropriatenessCheck: boolean;
  enableStructureValidation: boolean;
  enableCulturalSensitivityCheck: boolean;
  minimumPassingScore: number; // Minimum score to consider valid (0-1)
  strictMode: boolean; // If true, any critical issue fails validation
}

/**
 * Question data for validation
 */
export interface QuestionForValidation {
  id?: string;
  type: string;
  content: string;
  options?: string[];
  answer?: string[];
  explain?: string;
  subject?: string;
  grade?: string;
  difficulty?: string;
  [key: string]: any;
}
