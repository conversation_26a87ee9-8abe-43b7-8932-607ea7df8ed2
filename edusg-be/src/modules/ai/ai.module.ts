import { Module } from '@nestjs/common';
import { AiService } from './ai.service';
import { ModelConfigService } from './model-config.service';
import { GoogleAiService } from './google-ai.service';
import { AiOrchestrationService } from './ai-orchestration.service';

@Module({
  imports:[],
  providers: [AiService, ModelConfigService, GoogleAiService, AiOrchestrationService],
  exports: [AiService, ModelConfigService, GoogleAiService, AiOrchestrationService],
})
export class AiModule {}
