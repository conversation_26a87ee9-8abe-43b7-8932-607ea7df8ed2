import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleGenerativeAI, GenerativeModel } from '@google/generative-ai';

export interface GoogleAIResponse {
  content: string;
  finishReason?: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

@Injectable()
export class GoogleAiService implements OnModuleInit {
  private readonly logger = new Logger(GoogleAiService.name);
  private genAI: GoogleGenerativeAI;
  private model: GenerativeModel;

  constructor(private readonly configService: ConfigService) {}

  onModuleInit() {
    const apiKey = this.configService.get<string>('GOOGLE_API_KEY');
    if (!apiKey) {
      this.logger.warn('Google AI API key not found. Google AI services will be unavailable.');
      return;
    }

    try {
      this.genAI = new GoogleGenerativeAI(apiKey);
      // Use a default model, can be made configurable later
      this.model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
      this.logger.log('Google AI service initialized successfully');
    } catch (error) {
      this.logger.error(`Failed to initialize Google AI service: ${error.message}`, error.stack);
    }
  }

  /**
   * Check if Google AI service is available
   */
  isAvailable(): boolean {
    return !!this.genAI && !!this.model;
  }

  /**
   * Generate text using Google AI
   * @param prompt The prompt to send to Google AI
   * @param options Optional generation options
   * @returns Generated text response
   */
  async generateText(
    prompt: string,
    options?: {
      temperature?: number;
      maxOutputTokens?: number;
      topP?: number;
      topK?: number;
    }
  ): Promise<GoogleAIResponse> {
    if (!this.isAvailable()) {
      throw new Error('Google AI service is not available. Check API key configuration.');
    }

    try {
      this.logger.debug(`Generating text with Google AI, prompt length: ${prompt.length}`);

      const generationConfig = {
        temperature: options?.temperature ?? 0.7,
        maxOutputTokens: options?.maxOutputTokens ?? 2048,
        topP: options?.topP ?? 0.8,
        topK: options?.topK ?? 40,
      };

      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig,
      });

      const response = await result.response;
      const text = response.text();

      if (!text) {
        throw new Error('Empty response from Google AI');
      }

      this.logger.debug(`Google AI response generated successfully, length: ${text.length}`);

      return {
        content: text,
        finishReason: response.candidates?.[0]?.finishReason,
        usage: {
          promptTokens: response.usageMetadata?.promptTokenCount || 0,
          completionTokens: response.usageMetadata?.candidatesTokenCount || 0,
          totalTokens: response.usageMetadata?.totalTokenCount || 0,
        },
      };
    } catch (error) {
      this.logger.error(`Google AI generation failed: ${error.message}`, error.stack);
      throw new Error(`Google AI generation failed: ${error.message}`);
    }
  }

  /**
   * Generate JSON response using Google AI
   * @param prompt The prompt to send to Google AI
   * @param options Optional generation options
   * @returns Generated JSON response
   */
  async generateJSON(
    prompt: string,
    options?: {
      temperature?: number;
      maxOutputTokens?: number;
      topP?: number;
      topK?: number;
    }
  ): Promise<GoogleAIResponse> {
    const jsonPrompt = `${prompt}\n\nPlease respond with valid JSON only. Do not include any other text or formatting.`;
    return this.generateText(jsonPrompt, options);
  }

  /**
   * Generate questions using Google AI with specific formatting
   * @param systemPrompt System instructions for question generation
   * @param userPrompt User request for questions
   * @param options Optional generation options
   * @returns Generated questions in JSON format
   */
  async generateQuestions(
    systemPrompt: string,
    userPrompt: string,
    options?: {
      temperature?: number;
      maxOutputTokens?: number;
    }
  ): Promise<GoogleAIResponse> {
    const combinedPrompt = `${systemPrompt}\n\nUser Request: ${userPrompt}\n\nPlease generate educational questions in valid JSON format according to the system instructions.`;
    
    return this.generateJSON(combinedPrompt, {
      temperature: options?.temperature ?? 0.3, // Lower temperature for more consistent question generation
      maxOutputTokens: options?.maxOutputTokens ?? 4096,
    });
  }
}
