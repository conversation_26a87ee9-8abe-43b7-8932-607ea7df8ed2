import { Test, TestingModule } from '@nestjs/testing';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { RbacService } from '../auth/services/rbac.service';
import { EUserRole } from './dto/create-user.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { FilterUserDto } from './dto/filter-user.dto';
import { RbacExceptions } from '../auth/exceptions/rbac-exceptions';

describe('UserController RBAC', () => {
  let controller: UserController;
  let userService: UserService;
  let rbacService: RbacService;

  const mockUserService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findById: jest.fn(),
    update: jest.fn(),
    findStudentDetailById: jest.fn(),
  };

  const mockRbacService = {
    isAdmin: jest.fn(),
    isSchoolManager: jest.fn(),
    validateSchoolManagerUserCreation: jest.fn(),
    validateSchoolManagerUserAssignment: jest.fn(),
    getEffectiveSchoolId: jest.fn(),

  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        { provide: UserService, useValue: mockUserService },
        { provide: RbacService, useValue: mockRbacService },
      ],
    }).compile();

    controller = module.get<UserController>(UserController);
    userService = module.get<UserService>(UserService);
    rbacService = module.get<RbacService>(RbacService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const createUserDto: CreateUserDto = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      role: EUserRole.TEACHER,
      schoolId: 'school-1',
    };

    it('should allow admin to create any user', async () => {
      const adminUser = { sub: '1', email: '<EMAIL>', role: EUserRole.ADMIN };
      const req = { user: adminUser };

      mockRbacService.isSchoolManager.mockReturnValue(false);
      mockUserService.create.mockResolvedValue({ id: '1', ...createUserDto });

      await controller.create(createUserDto, req);

      expect(mockUserService.create).toHaveBeenCalledWith(createUserDto);
      expect(mockRbacService.validateSchoolManagerUserCreation).not.toHaveBeenCalled();
    });

    it('should allow school manager to create user for their school', async () => {
      const schoolManagerUser = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
        schoolId: 'school-1',
      };
      const req = { user: schoolManagerUser };

      mockRbacService.isSchoolManager.mockReturnValue(true);
      mockUserService.create.mockResolvedValue({ id: '1', ...createUserDto });

      await controller.create(createUserDto, req);

      expect(mockRbacService.validateSchoolManagerUserCreation).toHaveBeenCalledWith(
        schoolManagerUser,
        createUserDto.role,
      );
      expect(mockRbacService.validateSchoolManagerUserAssignment).toHaveBeenCalledWith(
        schoolManagerUser,
        createUserDto.schoolId,
      );
      expect(mockUserService.create).toHaveBeenCalledWith(createUserDto);
    });

    it('should throw exception when school manager has no assigned school', async () => {
      const schoolManagerUser = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
      };
      const req = { user: schoolManagerUser };

      mockRbacService.isSchoolManager.mockReturnValue(true);

      await expect(controller.create(createUserDto, req)).rejects.toThrow(
        RbacExceptions.schoolManagerNoSchool(),
      );
    });

    it('should assign school manager school when no schoolId provided', async () => {
      const schoolManagerUser = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
        schoolId: 'school-1',
      };
      const req = { user: schoolManagerUser };
      const createUserDtoNoSchool = { ...createUserDto, schoolId: undefined };

      mockRbacService.isSchoolManager.mockReturnValue(true);
      mockUserService.create.mockResolvedValue({ id: '1', ...createUserDtoNoSchool });

      await controller.create(createUserDtoNoSchool, req);

      expect(createUserDtoNoSchool.schoolId).toBe('school-1');
      expect(mockUserService.create).toHaveBeenCalledWith(createUserDtoNoSchool);
    });
  });

  describe('findAll', () => {
    const filterDto: FilterUserDto = { role: EUserRole.TEACHER };

    it('should allow admin to see all users without filtering', async () => {
      const adminUser = { sub: '1', email: '<EMAIL>', role: EUserRole.ADMIN };
      const req = { user: adminUser };

      mockRbacService.isSchoolManager.mockReturnValue(false);
      mockUserService.findAll.mockResolvedValue([]);

      await controller.findAll(filterDto, req);

      expect(mockUserService.findAll).toHaveBeenCalledWith(filterDto);
      expect(mockRbacService.getEffectiveSchoolId).not.toHaveBeenCalled();
    });

    it('should filter users by school for school manager', async () => {
      const schoolManagerUser = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
        schoolId: 'school-1',
      };
      const req = { user: schoolManagerUser };

      mockRbacService.isSchoolManager.mockReturnValue(true);
      mockRbacService.getEffectiveSchoolId.mockReturnValue('school-1');
      mockUserService.findAll.mockResolvedValue([]);

      await controller.findAll(filterDto, req);

      expect(mockRbacService.getEffectiveSchoolId).toHaveBeenCalledWith(
        schoolManagerUser,
        filterDto.schoolId,
      );
      expect(filterDto.schoolId).toBe('school-1');
      expect(mockUserService.findAll).toHaveBeenCalledWith(filterDto);
    });

    it('should throw exception when school manager has no assigned school', async () => {
      const schoolManagerUser = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
      };
      const req = { user: schoolManagerUser };

      mockRbacService.isSchoolManager.mockReturnValue(true);

      await expect(controller.findAll(filterDto, req)).rejects.toThrow(
        RbacExceptions.schoolManagerNoSchool(),
      );
    });
  });

  describe('findOne', () => {
    const userId = 'user-1';
    const mockUser = {
      id: userId,
      name: 'Test User',
      email: '<EMAIL>',
      role: EUserRole.TEACHER,
      schoolId: 'school-1',
    };

    it('should allow admin to access any user', async () => {
      const adminUser = { sub: '1', email: '<EMAIL>', role: EUserRole.ADMIN };
      const req = { user: adminUser };

      mockRbacService.isSchoolManager.mockReturnValue(false);
      mockUserService.findById.mockResolvedValue(mockUser);

      const result = await controller.findOne(userId, req);

      expect(result).toBe(mockUser);
      expect(mockUserService.findById).toHaveBeenCalledWith(userId);
    });

    it('should allow school manager to access user from their school', async () => {
      const schoolManagerUser = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
        schoolId: 'school-1',
      };
      const req = { user: schoolManagerUser };

      mockRbacService.isSchoolManager.mockReturnValue(true);
      mockUserService.findById.mockResolvedValue(mockUser);

      const result = await controller.findOne(userId, req);

      expect(result).toBe(mockUser);
      expect(mockUserService.findById).toHaveBeenCalledWith(userId);
    });

    it('should throw exception when school manager tries to access user from different school', async () => {
      const schoolManagerUser = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
        schoolId: 'school-2',
      };
      const req = { user: schoolManagerUser };
      const userFromDifferentSchool = { ...mockUser, schoolId: 'school-1' };

      mockRbacService.isSchoolManager.mockReturnValue(true);
      mockUserService.findById.mockResolvedValue(userFromDifferentSchool);

      await expect(controller.findOne(userId, req)).rejects.toThrow(
        RbacExceptions.schoolUsersOnly(),
      );
    });
  });

  describe('update', () => {
    const userId = 'user-1';
    const updateUserDto: UpdateUserDto = { name: 'Updated Name' };
    const mockUser = {
      id: userId,
      name: 'Test User',
      email: '<EMAIL>',
      role: EUserRole.TEACHER,
      schoolId: 'school-1',
    };

    it('should allow admin to update any user', async () => {
      const adminUser = { sub: '1', email: '<EMAIL>', role: EUserRole.ADMIN };
      const req = { user: adminUser };

      mockRbacService.isSchoolManager.mockReturnValue(false);
      mockUserService.update.mockResolvedValue({ ...mockUser, ...updateUserDto });

      await controller.update(userId, updateUserDto, req);

      expect(mockUserService.update).toHaveBeenCalledWith(userId, updateUserDto);
    });

    it('should allow school manager to update user from their school', async () => {
      const schoolManagerUser = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
        schoolId: 'school-1',
      };
      const req = { user: schoolManagerUser };

      mockRbacService.isSchoolManager.mockReturnValue(true);
      mockUserService.findById.mockResolvedValue(mockUser);
      mockUserService.update.mockResolvedValue({ ...mockUser, ...updateUserDto });

      await controller.update(userId, updateUserDto, req);

      expect(mockRbacService.validateSchoolManagerUserAssignment).toHaveBeenCalledWith(
        schoolManagerUser,
        updateUserDto.schoolId,
      );
      expect(mockUserService.update).toHaveBeenCalledWith(userId, updateUserDto);
    });

    it('should throw exception when school manager tries to update user from different school', async () => {
      const schoolManagerUser = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
        schoolId: 'school-2',
      };
      const req = { user: schoolManagerUser };
      const userFromDifferentSchool = { ...mockUser, schoolId: 'school-1' };

      mockRbacService.isSchoolManager.mockReturnValue(true);
      mockUserService.findById.mockResolvedValue(userFromDifferentSchool);

      await expect(controller.update(userId, updateUserDto, req)).rejects.toThrow(
        RbacExceptions.schoolUsersOnly(),
      );
    });
  });

  describe('getStudentDetail', () => {
    const studentId = 'student-1';
    const mockStudentDetail = {
      id: studentId,
      name: 'Test Student',
      email: '<EMAIL>',
      examResults: [
        {
          examId: 'exam-1',
          examTitle: 'Math Test',
          subject: 'Mathematics',
          score: 85,
          total: 100,
          percentage: 85,
          status: 'passed',
          submittedAt: new Date(),
        },
      ],
    };

    it('should allow teacher to access student detail from their school', async () => {
      const teacherUser = {
        sub: 'teacher-1',
        email: '<EMAIL>',
        role: EUserRole.TEACHER,
        schoolId: 'school-1',
      };
      const req = { user: teacherUser };

      mockUserService.findStudentDetailById.mockResolvedValue(mockStudentDetail);

      const result = await controller.getStudentDetail(studentId, req);

      expect(result).toBe(mockStudentDetail);
      expect(mockUserService.findStudentDetailById).toHaveBeenCalledWith(studentId, teacherUser);
    });

    it('should allow independent teacher to access student detail from their school', async () => {
      const independentTeacherUser = {
        sub: 'teacher-2',
        email: '<EMAIL>',
        role: EUserRole.INDEPENDENT_TEACHER,
        schoolId: 'school-1',
      };
      const req = { user: independentTeacherUser };

      mockUserService.findStudentDetailById.mockResolvedValue(mockStudentDetail);

      const result = await controller.getStudentDetail(studentId, req);

      expect(result).toBe(mockStudentDetail);
      expect(mockUserService.findStudentDetailById).toHaveBeenCalledWith(studentId, independentTeacherUser);
    });

    it('should throw exception when teacher has no school assignment', async () => {
      const teacherUser = {
        sub: 'teacher-3',
        email: '<EMAIL>',
        role: EUserRole.TEACHER,
        schoolId: null,
      };
      const req = { user: teacherUser };

      await expect(controller.getStudentDetail(studentId, req)).rejects.toThrow(
        RbacExceptions.schoolManagerNoSchool(),
      );

      expect(mockUserService.findStudentDetailById).not.toHaveBeenCalled();
    });
  });
});
