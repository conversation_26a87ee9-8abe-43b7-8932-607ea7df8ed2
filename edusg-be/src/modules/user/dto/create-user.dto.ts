import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum EUserRole {
  TEACHER = 'teacher',
  ADMIN = 'admin',
  STUDENT = 'student',
  SCHOOL_MANAGER = 'school_manager',
  INDEPENDENT_TEACHER = 'independent_teacher',
}

export enum UserStatus {
  INVITED = 'invited',
  ACTIVE = 'active',
}

export class CreateUserDto {
  @ApiProperty({
    description: 'User full name',
    example: '<PERSON>',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User password (minimum 6 characters)',
    example: 'password123',
    minLength: 6,
  })
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @ApiProperty({
    description: 'User role',
    example: EUserRole.TEACHER,
    enum: EUserRole,
    enumName: 'EUserRole',
  })
  @IsNotEmpty()
  @IsString()
  role: EUserRole;

  @ApiPropertyOptional({
    description: 'School ID (required for TEACHER, STUDENT, and SCHOOL_MANAGER roles)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  schoolId?: string | null;

  @ApiPropertyOptional({
    description: 'User status',
    example: UserStatus.ACTIVE,
    enum: UserStatus,
    enumName: 'UserStatus',
    default: UserStatus.ACTIVE
  })
  @IsOptional()
  status?: UserStatus;
}
