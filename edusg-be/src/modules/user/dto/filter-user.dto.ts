import { IsOptional, IsString, IsEnum } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { EUserRole } from './create-user.dto';

export class FilterUserDto {
  @ApiPropertyOptional({
    description: 'Filter users by school ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsOptional()
  @IsString()
  schoolId?: string;

  @ApiPropertyOptional({
    description: 'Filter users by role',
    example: 'teacher',
    enum: EUserRole,
  })
  @IsOptional()
  @IsEnum(EUserRole)
  role?: EUserRole;

  @ApiPropertyOptional({
    description: 'Filter users by a list of roles',
    example: ['teacher', 'student'],
    enum: EUserRole,
    isArray: true,
  })
  @IsOptional()
  @IsEnum(EUserRole, { each: true })
  roles?: EUserRole[];
}

export class FilterStudentsDto {
  @ApiPropertyOptional({
    description: 'Filter students by school ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsOptional()
  @IsString()
  schoolId?: string;
}