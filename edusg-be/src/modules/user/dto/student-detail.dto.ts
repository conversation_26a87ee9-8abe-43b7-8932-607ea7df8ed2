import { ApiProperty } from '@nestjs/swagger';

export class StudentExamSummaryDto {
  @ApiProperty({ description: 'Exam ID' })
  examId: string;

  @ApiProperty({ description: 'Exam title' })
  examTitle: string;

  @ApiProperty({ description: 'Subject or topic of the exam' })
  subject: string;

  @ApiProperty({ description: 'Score achieved by student' })
  score: number;

  @ApiProperty({ description: 'Total possible score' })
  total: number;

  @ApiProperty({ description: 'Percentage score' })
  percentage: number;

  @ApiProperty({ enum: ['passed', 'failed'], description: 'Pass/fail status based on passing score' })
  status: 'passed' | 'failed';

  @ApiProperty({ description: 'Submission timestamp' })
  submittedAt: Date;
}

export class StudentDetailDto {
  @ApiProperty({ description: 'Student ID' })
  id: string;

  @ApiProperty({ description: 'Student full name' })
  name: string;

  @ApiProperty({ description: 'Student email address' })
  email: string;

  @ApiProperty({ type: [StudentExamSummaryDto], description: 'Array of exam results summaries' })
  examResults: StudentExamSummaryDto[];
} 