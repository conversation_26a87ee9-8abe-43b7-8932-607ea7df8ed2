import { Injectable, NotFoundException, ForbiddenException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { CreateUserDto, EUserRole } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { FilterUserDto } from './dto/filter-user.dto';
import { StudentDetailDto, StudentExamSummaryDto } from './dto/student-detail.dto';
import { User } from './entities/user.entity';
import { ExamService } from '../exam/exam.service';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @Inject(forwardRef(() => ExamService))
    private readonly examService: ExamService,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const user = this.userRepository.create(createUserDto);
    return this.userRepository.save(user);
  }

  async findAll(filterUserDto: FilterUserDto) {
    const { schoolId, role, roles } = filterUserDto;
    const where: any = {};

    if (schoolId) {
      where.schoolId = schoolId;
    }

    if (role) {
      where.role = role;
    }

    if (roles && roles.length > 0) {
      where.role = In(roles);
    }

    return this.userRepository.find({ where });
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { email } }); // Ensure this query works
  }

  async findByPasswordResetToken(token: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { passwordResetToken: token } });
  }

  async findById(id: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { id } });
  }

  async findOne(id: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { id } });
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findById(id);

    if (!user) {
      throw new Error('User not found');
    }

    // Password will be hashed in the BeforeUpdate hook if needed
    // No need to hash it here

    // Update user properties
    Object.assign(user, updateUserDto);

    return this.userRepository.save(user);
  }

  async updateSchoolId(userId: string, schoolId: string): Promise<User> {
    const user = await this.findById(userId);

    if (!user) {
      throw new Error('User not found');
    }

    user.schoolId = schoolId;
    return this.userRepository.save(user);
  }

  async findStudentDetailById(studentId: string, teacher: any): Promise<StudentDetailDto> {
    // First, validate that the user is a student
    const student = await this.userRepository.findOne({
      where: { id: studentId, role: EUserRole.STUDENT },
    });

    if (!student) {
      throw new NotFoundException('Student not found');
    }

    // Validate that teacher has permission to view this student (same school)
    if (student.schoolId !== teacher.schoolId) {
      throw new ForbiddenException('You can only access students from your school');
    }

    // Get exam results summaries for this student
    const examSummaries = await this.examService.getStudentExamSummaries(studentId);

    const studentDetail: StudentDetailDto = {
      id: student.id,
      name: student.name || 'Unknown',
      email: student.email,
      examResults: examSummaries,
    };

    return studentDetail;
  }
}
