import { ForbiddenException, UnauthorizedException } from '@nestjs/common';

/**
 * Standardized RBAC exception messages and factory functions
 */
export class RbacExceptions {
  // Standard error messages
  static readonly MESSAGES = {
    INSUFFICIENT_ROLE: 'Insufficient permissions for this operation',
    <PERSON>H<PERSON><PERSON>_ACCESS_DENIED: 'You do not have access to this school',
    SCHOOL_MANAGER_NO_SCHOOL: 'School manager must be assigned to a school',
    SCHOOL_USERS_ONLY: 'You can only access users from your own school',
    SCHOOL_UPDATE_ONLY: 'You can only update your own school',
    SCHOOL_UPLOAD_ONLY: 'You can only upload examination formats for your own school',
    CANNOT_CREATE_ADMIN: 'You cannot create admin or school manager users',
    CANNOT_TRANSFER_USERS: 'You cannot assign users to a different school',
    ADMI<PERSON>_ONLY: 'This operation requires administrator privileges',
    AUTHENTICATION_REQUIRED: 'Authentication required',
    INVALID_TOKEN: 'Invalid or expired authentication token',
  } as const;

  // Error codes for programmatic handling
  static readonly CODES = {
    RBAC_INSUFFICIENT_ROLE: 'RBAC_INSUFFICIENT_ROLE',
    RB<PERSON>_SCHOOL_ACCESS_DENIED: 'RBAC_SCHOOL_ACCESS_DENIED',
    RBAC_SCHOOL_MANAGER_NO_SCHOOL: 'RBAC_SCHOOL_MANAGER_NO_SCHOOL',
    RBAC_SCHOOL_USERS_ONLY: 'RBAC_SCHOOL_USERS_ONLY',
    RBAC_SCHOOL_UPDATE_ONLY: 'RBAC_SCHOOL_UPDATE_ONLY',
    RBAC_SCHOOL_UPLOAD_ONLY: 'RBAC_SCHOOL_UPLOAD_ONLY',
    RBAC_CANNOT_CREATE_ADMIN: 'RBAC_CANNOT_CREATE_ADMIN',
    RBAC_CANNOT_TRANSFER_USERS: 'RBAC_CANNOT_TRANSFER_USERS',
    RBAC_ADMIN_ONLY: 'RBAC_ADMIN_ONLY',
    AUTH_REQUIRED: 'AUTH_REQUIRED',
    AUTH_INVALID_TOKEN: 'AUTH_INVALID_TOKEN',
  } as const;

  /**
   * Create a standardized ForbiddenException with error code
   */
  static createForbiddenException(message: string, code: string): ForbiddenException {
    return new ForbiddenException({
      message,
      error: 'Forbidden',
      statusCode: 403,
      code,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Create a standardized UnauthorizedException with error code
   */
  static createUnauthorizedException(message: string, code: string): UnauthorizedException {
    return new UnauthorizedException({
      message,
      error: 'Unauthorized',
      statusCode: 401,
      code,
      timestamp: new Date().toISOString(),
    });
  }

  // Factory methods for common exceptions
  static insufficientRole(): ForbiddenException {
    return this.createForbiddenException(
      this.MESSAGES.INSUFFICIENT_ROLE,
      this.CODES.RBAC_INSUFFICIENT_ROLE
    );
  }

  static schoolAccessDenied(): ForbiddenException {
    return this.createForbiddenException(
      this.MESSAGES.SCHOOL_ACCESS_DENIED,
      this.CODES.RBAC_SCHOOL_ACCESS_DENIED
    );
  }

  static schoolManagerNoSchool(): ForbiddenException {
    return this.createForbiddenException(
      this.MESSAGES.SCHOOL_MANAGER_NO_SCHOOL,
      this.CODES.RBAC_SCHOOL_MANAGER_NO_SCHOOL
    );
  }

  static schoolUsersOnly(): ForbiddenException {
    return this.createForbiddenException(
      this.MESSAGES.SCHOOL_USERS_ONLY,
      this.CODES.RBAC_SCHOOL_USERS_ONLY
    );
  }

  static schoolUpdateOnly(): ForbiddenException {
    return this.createForbiddenException(
      this.MESSAGES.SCHOOL_UPDATE_ONLY,
      this.CODES.RBAC_SCHOOL_UPDATE_ONLY
    );
  }

  static schoolUploadOnly(): ForbiddenException {
    return this.createForbiddenException(
      this.MESSAGES.SCHOOL_UPLOAD_ONLY,
      this.CODES.RBAC_SCHOOL_UPLOAD_ONLY
    );
  }

  static cannotCreateAdmin(): ForbiddenException {
    return this.createForbiddenException(
      this.MESSAGES.CANNOT_CREATE_ADMIN,
      this.CODES.RBAC_CANNOT_CREATE_ADMIN
    );
  }

  static cannotTransferUsers(): ForbiddenException {
    return this.createForbiddenException(
      this.MESSAGES.CANNOT_TRANSFER_USERS,
      this.CODES.RBAC_CANNOT_TRANSFER_USERS
    );
  }

  static adminOnly(): ForbiddenException {
    return this.createForbiddenException(
      this.MESSAGES.ADMIN_ONLY,
      this.CODES.RBAC_ADMIN_ONLY
    );
  }

  static authenticationRequired(): UnauthorizedException {
    return this.createUnauthorizedException(
      this.MESSAGES.AUTHENTICATION_REQUIRED,
      this.CODES.AUTH_REQUIRED
    );
  }

  static invalidToken(): UnauthorizedException {
    return this.createUnauthorizedException(
      this.MESSAGES.INVALID_TOKEN,
      this.CODES.AUTH_INVALID_TOKEN
    );
  }
}
