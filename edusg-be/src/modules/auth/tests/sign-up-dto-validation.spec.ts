import { validate } from 'class-validator';
import { SignUpDto } from '../dto/sign-up';
import { EUserRole } from '../../user/dto/create-user.dto';

describe('SignUpDto Validation - INDEPENDENT_TEACHER Role', () => {
  describe('INDEPENDENT_TEACHER role validation', () => {
    it('should pass validation when INDEPENDENT_TEACHER has no schoolId', async () => {
      const dto = new SignUpDto();
      dto.name = 'John Independent';
      dto.email = '<EMAIL>';
      dto.password = 'password123';
      dto.role = EUserRole.INDEPENDENT_TEACHER;
      // schoolId is intentionally omitted

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation when INDEPENDENT_TEACHER has undefined schoolId', async () => {
      const dto = new SignUpDto();
      dto.name = 'Jane Independent';
      dto.email = '<EMAIL>';
      dto.password = 'password123';
      dto.role = EUserRole.INDEPENDENT_TEACHER;
      dto.schoolId = undefined;

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass DTO validation when INDEPENDENT_TEACHER has a schoolId provided (business logic will handle rejection)', async () => {
      const dto = new SignUpDto();
      dto.name = 'John Independent';
      dto.email = '<EMAIL>';
      dto.password = 'password123';
      dto.role = EUserRole.INDEPENDENT_TEACHER;
      dto.schoolId = 'some-school-id';

      const errors = await validate(dto);
      // DTO validation should pass, business logic in AuthService will reject this
      expect(errors).toHaveLength(0);
    });

    it('should pass DTO validation when INDEPENDENT_TEACHER has invalid UUID format for schoolId (business logic will handle rejection)', async () => {
      const dto = new SignUpDto();
      dto.name = 'John Independent';
      dto.email = '<EMAIL>';
      dto.password = 'password123';
      dto.role = EUserRole.INDEPENDENT_TEACHER;
      dto.schoolId = 'invalid-uuid';

      const errors = await validate(dto);
      // DTO validation should pass for INDEPENDENT_TEACHER, business logic will reject this
      expect(errors).toHaveLength(0);
    });
  });

  describe('Other roles validation (regression tests)', () => {
    it('should fail validation when TEACHER has no schoolId', async () => {
      const dto = new SignUpDto();
      dto.name = 'Regular Teacher';
      dto.email = '<EMAIL>';
      dto.password = 'password123';
      dto.role = EUserRole.TEACHER;
      // schoolId is intentionally omitted

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      // Find the schoolId validation error
      const schoolIdError = errors.find(error => error.property === 'schoolId');
      expect(schoolIdError).toBeDefined();
      expect(schoolIdError?.constraints).toHaveProperty('isNotEmpty');
    });

    it('should pass validation when TEACHER has valid schoolId', async () => {
      const dto = new SignUpDto();
      dto.name = 'Regular Teacher';
      dto.email = '<EMAIL>';
      dto.password = 'password123';
      dto.role = EUserRole.TEACHER;
      dto.schoolId = '123e4567-e89b-12d3-a456-426614174000';

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should fail validation when SCHOOL_MANAGER has no schoolId', async () => {
      const dto = new SignUpDto();
      dto.name = 'School Manager';
      dto.email = '<EMAIL>';
      dto.password = 'password123';
      dto.role = EUserRole.SCHOOL_MANAGER;
      // schoolId is intentionally omitted

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      // Find the schoolId validation error
      const schoolIdError = errors.find(error => error.property === 'schoolId');
      expect(schoolIdError).toBeDefined();
      expect(schoolIdError?.constraints).toHaveProperty('isNotEmpty');
    });

    it('should pass validation when SCHOOL_MANAGER has valid schoolId', async () => {
      const dto = new SignUpDto();
      dto.name = 'School Manager';
      dto.email = '<EMAIL>';
      dto.password = 'password123';
      dto.role = EUserRole.SCHOOL_MANAGER;
      dto.schoolId = '123e4567-e89b-12d3-a456-426614174000';

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should fail validation when STUDENT has no schoolId', async () => {
      const dto = new SignUpDto();
      dto.name = 'Student';
      dto.email = '<EMAIL>';
      dto.password = 'password123';
      dto.role = EUserRole.STUDENT;
      // schoolId is intentionally omitted

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      // Find the schoolId validation error
      const schoolIdError = errors.find(error => error.property === 'schoolId');
      expect(schoolIdError).toBeDefined();
      expect(schoolIdError?.constraints).toHaveProperty('isNotEmpty');
    });

    it('should pass validation when STUDENT has valid schoolId', async () => {
      const dto = new SignUpDto();
      dto.name = 'Student';
      dto.email = '<EMAIL>';
      dto.password = 'password123';
      dto.role = EUserRole.STUDENT;
      dto.schoolId = '123e4567-e89b-12d3-a456-426614174000';

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation when ADMIN has no schoolId (system role)', async () => {
      const dto = new SignUpDto();
      dto.name = 'System Admin';
      dto.email = '<EMAIL>';
      dto.password = 'password123';
      dto.role = EUserRole.ADMIN;
      // schoolId is intentionally omitted for system admin

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });

  describe('General validation tests', () => {
    it('should fail validation with invalid email format', async () => {
      const dto = new SignUpDto();
      dto.name = 'John Independent';
      dto.email = 'invalid-email';
      dto.password = 'password123';
      dto.role = EUserRole.INDEPENDENT_TEACHER;

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const emailError = errors.find(error => error.property === 'email');
      expect(emailError).toBeDefined();
      expect(emailError?.constraints).toHaveProperty('isEmail');
    });

    it('should fail validation with short password', async () => {
      const dto = new SignUpDto();
      dto.name = 'John Independent';
      dto.email = '<EMAIL>';
      dto.password = '123'; // Too short
      dto.role = EUserRole.INDEPENDENT_TEACHER;

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const passwordError = errors.find(error => error.property === 'password');
      expect(passwordError).toBeDefined();
      expect(passwordError?.constraints).toHaveProperty('minLength');
    });

    it('should fail validation with empty name', async () => {
      const dto = new SignUpDto();
      dto.name = ''; // Empty name
      dto.email = '<EMAIL>';
      dto.password = 'password123';
      dto.role = EUserRole.INDEPENDENT_TEACHER;

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const nameError = errors.find(error => error.property === 'name');
      expect(nameError).toBeDefined();
      expect(nameError?.constraints).toHaveProperty('isNotEmpty');
    });
  });
});
