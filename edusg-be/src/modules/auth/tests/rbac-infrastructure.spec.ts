import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RoleGuard } from '../guards/role.guard';
import { Roles } from '../decorators/role.decorator';
import { EUserRole } from '../../user/dto/create-user.dto';

describe('RBAC Infrastructure Tests', () => {
  let roleGuard: RoleGuard;
  let reflector: Reflector;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RoleGuard,
        {
          provide: Reflector,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    roleGuard = module.get<RoleGuard>(RoleGuard);
    reflector = module.get<Reflector>(Reflector);
  });

  describe('RoleGuard', () => {
    let mockExecutionContext: Partial<ExecutionContext>;
    let mockRequest: any;

    beforeEach(() => {
      mockRequest = {
        user: null,
      };

      mockExecutionContext = {
        switchToHttp: () => ({
          getRequest: () => mockRequest,
          getResponse: jest.fn(),
          getNext: jest.fn(),
        }),
        getHandler: jest.fn(),
        getClass: jest.fn(),
        getArgs: jest.fn(),
        getArgByIndex: jest.fn(),
        switchToRpc: jest.fn(),
        switchToWs: jest.fn(),
        getType: jest.fn(),
      };
    });

    it('should be defined', () => {
      expect(roleGuard).toBeDefined();
    });

    it('should allow access when no roles are required', () => {
      jest.spyOn(reflector, 'get').mockReturnValue(null);

      const result = roleGuard.canActivate(mockExecutionContext as ExecutionContext);

      expect(result).toBe(true);
    });

    it('should deny access when user is not present', () => {
      jest.spyOn(reflector, 'get').mockReturnValue([EUserRole.ADMIN]);
      mockRequest.user = null;

      const result = roleGuard.canActivate(mockExecutionContext as ExecutionContext);

      expect(result).toBe(false);
    });

    it('should allow access for ADMIN to any resource', () => {
      jest.spyOn(reflector, 'get').mockReturnValue([EUserRole.TEACHER]);
      mockRequest.user = { role: EUserRole.ADMIN };

      const result = roleGuard.canActivate(mockExecutionContext as ExecutionContext);

      expect(result).toBe(true);
    });

    it('should allow access when user has required role', () => {
      jest.spyOn(reflector, 'get').mockReturnValue([EUserRole.TEACHER]);
      mockRequest.user = { role: EUserRole.TEACHER };

      const result = roleGuard.canActivate(mockExecutionContext as ExecutionContext);

      expect(result).toBe(true);
    });

    it('should allow access when user has one of multiple required roles', () => {
      jest.spyOn(reflector, 'get').mockReturnValue([EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER]);
      mockRequest.user = { role: EUserRole.SCHOOL_MANAGER };

      const result = roleGuard.canActivate(mockExecutionContext as ExecutionContext);

      expect(result).toBe(true);
    });

    it('should deny access when user does not have required role', () => {
      jest.spyOn(reflector, 'get').mockReturnValue([EUserRole.ADMIN]);
      mockRequest.user = { role: EUserRole.TEACHER };

      const result = roleGuard.canActivate(mockExecutionContext as ExecutionContext);

      expect(result).toBe(false);
    });

    it('should deny access when user role is not in multiple required roles', () => {
      jest.spyOn(reflector, 'get').mockReturnValue([EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER]);
      mockRequest.user = { role: EUserRole.STUDENT };

      const result = roleGuard.canActivate(mockExecutionContext as ExecutionContext);

      expect(result).toBe(false);
    });
  });

  describe('@Roles() Decorator', () => {
    it('should set metadata correctly for single role', () => {
      const decorator = Roles(EUserRole.ADMIN);
      const target = {};
      const key = 'testMethod';

      // Mock SetMetadata behavior
      const mockSetMetadata = jest.fn();
      jest.doMock('@nestjs/common', () => ({
        SetMetadata: mockSetMetadata,
      }));

      // The decorator should call SetMetadata with 'roles' key and array of roles
      expect(typeof decorator).toBe('function');
    });

    it('should set metadata correctly for multiple roles', () => {
      const decorator = Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER);
      expect(typeof decorator).toBe('function');
    });
  });

  describe('EUserRole Enum', () => {
    it('should have all expected roles', () => {
      expect(EUserRole.ADMIN).toBe('admin');
      expect(EUserRole.SCHOOL_MANAGER).toBe('school_manager');
      expect(EUserRole.TEACHER).toBe('teacher');
      expect(EUserRole.STUDENT).toBe('student');
      expect(EUserRole.INDEPENDENT_TEACHER).toBe('independent_teacher');
    });

    it('should have exactly 5 roles', () => {
      const roleValues = Object.values(EUserRole);
      expect(roleValues).toHaveLength(5);
    });
  });
});
