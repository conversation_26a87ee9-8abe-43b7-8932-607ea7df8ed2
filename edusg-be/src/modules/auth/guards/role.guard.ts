import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { EUserRole } from 'src/modules/user/dto/create-user.dto';

@Injectable()
export class RoleGuard implements CanActivate {
  private readonly logger = new Logger(RoleGuard.name);

  constructor(private reflector: Reflector) {}

  // Check if user has required roles
  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.get<EUserRole[]>(
      'roles',
      context.getHandler(),
    );

    // No role restriction - allow all authenticated users
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const endpoint = `${request.method} ${request.route?.path || request.url}`;

    // User not authenticated
    if (!user) {
      this.logger.warn(`Access denied: No user context for ${endpoint}`);
      return false;
    }

    // Ad<PERSON> has access to all resources (role hierarchy)
    if (user.role === EUserRole.ADMIN) {
      this.logger.debug(`Access granted: Admin user ${user.sub} accessing ${endpoint}`);
      return true;
    }

    // Check if user's role is in the required roles
    const hasRequiredRole = requiredRoles.includes(user.role);

    if (hasRequiredRole) {
      this.logger.debug(`Access granted: User ${user.sub} with role ${user.role} accessing ${endpoint}`);
    } else {
      this.logger.warn(`Access denied: User ${user.sub} with role ${user.role} attempted to access ${endpoint}. Required roles: ${requiredRoles.join(', ')}`);
    }

    return hasRequiredRole;
  }
}
