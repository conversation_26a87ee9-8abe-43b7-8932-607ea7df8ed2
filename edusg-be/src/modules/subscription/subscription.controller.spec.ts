import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { SubscriptionController } from './subscription.controller';
import { SubscriptionService } from './subscription.service';
import { AuthGuard } from '../auth/guards/auth.guard';
import { ActiveUserData } from '../auth/decorators/active-user.decorator';

describe('SubscriptionController', () => {
  let controller: SubscriptionController;
  let subscriptionService: jest.Mocked<SubscriptionService>;

  const mockUser: ActiveUserData = {
    sub: 'user-123',
    email: '<EMAIL>',
    role: 'TEACHER' as any,
  };

  const mockSubscriptionWithPackage = {
    id: 'sub-uuid',
    user_id: 'user-123',
    stripe_subscription_id: 'sub_stripe_123',
    stripe_customer_id: 'cus_stripe_123',
    stripe_price_id: 'price_stripe_123',
    stripe_product_id: 'prod_stripe_123',
    start_date: new Date('2024-01-01'),
    current_period_start: new Date('2024-01-01'),
    current_period_end: new Date('2024-02-01'),
    cancel_at: null,
    canceled_at: null,
    ended_at: null,
    cancel_at_period_end: false,
    status: 'active',
    price_unit_amount: 2000,
    currency: 'usd',
    metadata: {},
    latest_invoice_id: 'in_stripe_123',
    payment_method: 'card',
    raw_data: {},
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    package: {
      id: 1,
      name: 'Premium Package',
      description: 'Premium subscription package',
      stripeProductId: 'prod_stripe_123',
    },
  } as any;

  const mockInvoices = [
    {
      id: 'in_stripe_123',
      number: 'INV-001',
      status: 'paid',
      amount_paid: 2000,
      amount_due: 0,
      currency: 'usd',
      created: 1640995200,
      invoice_pdf: 'https://stripe.com/invoice.pdf',
      hosted_invoice_url: 'https://stripe.com/invoice',
      period_start: 1640995200,
      period_end: **********,
    },
    {
      id: 'in_stripe_456',
      number: 'INV-002',
      status: 'paid',
      amount_paid: 2000,
      amount_due: 0,
      currency: 'usd',
      created: **********,
      invoice_pdf: 'https://stripe.com/invoice2.pdf',
      hosted_invoice_url: 'https://stripe.com/invoice2',
      period_start: **********,
      period_end: **********,
    },
  ] as any[];

  beforeEach(async () => {
    const mockSubscriptionService = {
      findByUserId: jest.fn(),
      getUserInvoices: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [SubscriptionController],
      providers: [
        {
          provide: SubscriptionService,
          useValue: mockSubscriptionService,
        },
      ],
    })
      .overrideGuard(AuthGuard)
      .useValue({
        canActivate: () => true,
      })
      .compile();

    controller = module.get<SubscriptionController>(SubscriptionController);
    subscriptionService = module.get(SubscriptionService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getMySubscription', () => {
    it('should return subscription with package details when found', async () => {
      subscriptionService.findByUserId.mockResolvedValue(mockSubscriptionWithPackage);

      const result = await controller.getMySubscription(mockUser);

      expect(subscriptionService.findByUserId).toHaveBeenCalledWith('user-123');
      expect(result).toEqual(mockSubscriptionWithPackage);
    });

    it('should throw NotFoundException when subscription service throws NotFoundException', async () => {
      const notFoundError = new NotFoundException('No subscription found for user ID: user-123');
      subscriptionService.findByUserId.mockRejectedValue(notFoundError);

      await expect(controller.getMySubscription(mockUser)).rejects.toThrow(
        new NotFoundException('No subscription found for user ID: user-123'),
      );

      expect(subscriptionService.findByUserId).toHaveBeenCalledWith('user-123');
    });

    it('should re-throw InternalServerErrorException from service', async () => {
      const internalError = new InternalServerErrorException('Could not retrieve user subscription.');
      subscriptionService.findByUserId.mockRejectedValue(internalError);

      await expect(controller.getMySubscription(mockUser)).rejects.toThrow(
        InternalServerErrorException,
      );

      expect(subscriptionService.findByUserId).toHaveBeenCalledWith('user-123');
    });

    it('should re-throw other errors from service as-is', async () => {
      const genericError = new Error('Database connection failed');
      subscriptionService.findByUserId.mockRejectedValue(genericError);

      await expect(controller.getMySubscription(mockUser)).rejects.toThrow(
        'Database connection failed',
      );

      expect(subscriptionService.findByUserId).toHaveBeenCalledWith('user-123');
    });

    it('should use the correct user ID from @ActiveUser decorator', async () => {
      const differentUser: ActiveUserData = {
        sub: 'user-456',
        email: '<EMAIL>',
        role: 'ADMIN' as any,
      };

      subscriptionService.findByUserId.mockResolvedValue(mockSubscriptionWithPackage);

      await controller.getMySubscription(differentUser);

      expect(subscriptionService.findByUserId).toHaveBeenCalledWith('user-456');
    });
  });

  describe('getMyInvoices', () => {
    it('should return user invoices when found', async () => {
      subscriptionService.getUserInvoices.mockResolvedValue(mockInvoices);

      const result = await controller.getMyInvoices(mockUser);

      expect(subscriptionService.getUserInvoices).toHaveBeenCalledWith('user-123');
      expect(result).toEqual(mockInvoices);
    });

    it('should return empty array when no invoices found', async () => {
      subscriptionService.getUserInvoices.mockResolvedValue([]);

      const result = await controller.getMyInvoices(mockUser);

      expect(subscriptionService.getUserInvoices).toHaveBeenCalledWith('user-123');
      expect(result).toEqual([]);
    });

    it('should throw NotFoundException when subscription service throws NotFoundException', async () => {
      const notFoundError = new NotFoundException('No subscription found for user ID: user-123');
      subscriptionService.getUserInvoices.mockRejectedValue(notFoundError);

      await expect(controller.getMyInvoices(mockUser)).rejects.toThrow(
        new NotFoundException('No subscription found for user ID: user-123'),
      );

      expect(subscriptionService.getUserInvoices).toHaveBeenCalledWith('user-123');
    });

    it('should throw InternalServerErrorException when service throws it', async () => {
      const internalError = new InternalServerErrorException('Failed to retrieve invoices from Stripe.');
      subscriptionService.getUserInvoices.mockRejectedValue(internalError);

      await expect(controller.getMyInvoices(mockUser)).rejects.toThrow(
        InternalServerErrorException,
      );

      expect(subscriptionService.getUserInvoices).toHaveBeenCalledWith('user-123');
    });

    it('should use the correct user ID from @ActiveUser decorator', async () => {
      const differentUser: ActiveUserData = {
        sub: 'user-789',
        email: '<EMAIL>',
        role: 'SCHOOL_MANAGER' as any,
      };

      subscriptionService.getUserInvoices.mockResolvedValue(mockInvoices);

      await controller.getMyInvoices(differentUser);

      expect(subscriptionService.getUserInvoices).toHaveBeenCalledWith('user-789');
    });

    it('should handle Stripe API errors thrown by service', async () => {
      const stripeError = new InternalServerErrorException('Stripe API error occurred.');
      subscriptionService.getUserInvoices.mockRejectedValue(stripeError);

      await expect(controller.getMyInvoices(mockUser)).rejects.toThrow(
        'Stripe API error occurred.',
      );

      expect(subscriptionService.getUserInvoices).toHaveBeenCalledWith('user-123');
    });
  });

  describe('Guard Application', () => {
    it('should be protected by AuthGuard', () => {
      const guards = Reflect.getMetadata('__guards__', SubscriptionController);
      expect(guards).toBeDefined();
      expect(guards).toContain(AuthGuard);
    });

    it('should use @UseGuards(AuthGuard) at class level', () => {
      // This test verifies that the controller class has the AuthGuard applied
      // The actual guard functionality is tested in integration tests
      const controllerInstance = new SubscriptionController(subscriptionService);
      expect(controllerInstance).toBeDefined();
    });
  });

  describe('API Documentation', () => {
    it('should have proper API tags and bearer auth', () => {
      // Verify that the controller has the correct Swagger decorators
      // These are important for API documentation
      const controllerInstance = new SubscriptionController(subscriptionService);
      expect(controllerInstance).toBeDefined();
    });
  });
}); 