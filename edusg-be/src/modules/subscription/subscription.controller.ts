import {
  Controller,
  Get,
  UseGuards,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { SubscriptionService } from './subscription.service';
import { AuthGuard } from '../auth/guards/auth.guard';
import { ActiveUser, ActiveUserData } from '../auth/decorators/active-user.decorator';

@ApiTags('Subscription')
@ApiBearerAuth()
@Controller('subscription')
@UseGuards(AuthGuard)
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Get('me')
  @ApiOperation({ summary: 'Get current user subscription details' })
  @ApiResponse({
    status: 200,
    description: 'User subscription retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number' },
        user_id: { type: 'string' },
        stripe_subscription_id: { type: 'string' },
        stripe_customer_id: { type: 'string' },
        stripe_price_id: { type: 'string' },
        stripe_product_id: { type: 'string' },
        start_date: { type: 'string', format: 'date-time' },
        current_period_start: { type: 'string', format: 'date-time' },
        current_period_end: { type: 'string', format: 'date-time' },
        status: { type: 'string' },
        price_unit_amount: { type: 'number' },
        currency: { type: 'string' },
        cancel_at_period_end: { type: 'boolean' },
        package: {
          type: 'object',
          properties: {
            id: { type: 'number' },
            name: { type: 'string' },
            description: { type: 'string' },
            stripeProductId: { type: 'string' },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'No subscription found for user' })
  async getMySubscription(@ActiveUser() user: ActiveUserData) {
    try {
      return await this.subscriptionService.findByUserId(user.sub);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      // Re-throw any other errors as-is since service handles internal errors
      throw error;
    }
  }

  @Get('me/invoices')
  @ApiOperation({ summary: 'Get current user invoice history' })
  @ApiResponse({
    status: 200,
    description: 'User invoices retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          number: { type: 'string' },
          status: { type: 'string' },
          amount_paid: { type: 'number' },
          amount_due: { type: 'number' },
          currency: { type: 'string' },
          created: { type: 'number' },
          invoice_pdf: { type: 'string' },
          hosted_invoice_url: { type: 'string' },
          period_start: { type: 'number' },
          period_end: { type: 'number' },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'No subscription found for user' })
  async getMyInvoices(@ActiveUser() user: ActiveUserData) {
    return await this.subscriptionService.getUserInvoices(user.sub);
  }
} 