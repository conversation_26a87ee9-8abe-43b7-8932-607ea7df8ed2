import { ForbiddenException, NotFoundException } from '@nestjs/common';
import { ExamController } from '../exam.controller';
import { ExamService } from '../exam.service';
import { EUserRole } from '../../user/dto/create-user.dto';
import { CreateExamDto } from '../dto/create-exam.dto';
import { SubmitExamDto } from '../dto/submit-exam.dto';

describe('ExamController - INDEPENDENT_TEACHER Integration', () => {
  let controller: ExamController;
  let examService: jest.Mocked<ExamService>;

  const mockIndependentTeacher = {
    sub: 'independent-teacher-id',
    email: '<EMAIL>',
    role: EUserRole.INDEPENDENT_TEACHER,
    schoolId: 'independent-school-id',
  };

  const mockOtherSchoolUser = {
    sub: 'other-user-id',
    email: '<EMAIL>',
    role: EUserRole.TEACHER,
    schoolId: 'other-school-id',
  };

  const mockExam = {
    id: 'exam-id',
    title: 'Test Exam',
    description: 'Test Description',
    schoolId: 'independent-school-id',
    userId: 'independent-teacher-id',
    worksheetId: 'worksheet-id',
    status: 'in_progress',
    questions: [],
  };

  const mockOtherSchoolExam = {
    id: 'other-exam-id',
    title: 'Other School Exam',
    description: 'Other Description',
    schoolId: 'other-school-id',
    userId: 'other-user-id',
    worksheetId: 'other-worksheet-id',
    status: 'in_progress',
    questions: [],
  };

  beforeEach(() => {
    examService = {
      createExam: jest.fn(),
      getExamById: jest.fn(),
      submitExam: jest.fn(),
      getExamsByWorksheetId: jest.fn(),
    } as any;

    // Create controller instance directly without NestJS DI
    controller = new ExamController(examService);
  });

  describe('createExam', () => {
    it('should allow INDEPENDENT_TEACHER to create exams', async () => {
      const createDto: CreateExamDto = {
        worksheetId: 'worksheet-id',
        title: 'New Exam',
        description: 'New Description',
        selectedOptions: [],
        questions: [],
      };

      examService.createExam.mockResolvedValue(mockExam as any);

      const result = await controller.createExam(mockIndependentTeacher as any, createDto);

      expect(examService.createExam).toHaveBeenCalledWith(
        mockIndependentTeacher.sub,
        createDto,
        mockIndependentTeacher.schoolId
      );
      expect(result).toEqual(mockExam);
    });

    it('should associate exam with INDEPENDENT_TEACHER schoolId', async () => {
      const createDto: CreateExamDto = {
        worksheetId: 'worksheet-id',
        title: 'New Exam',
        description: 'New Description',
        selectedOptions: [],
        questions: [],
      };

      examService.createExam.mockResolvedValue({
        ...mockExam,
        schoolId: mockIndependentTeacher.schoolId,
      } as any);

      await controller.createExam(mockIndependentTeacher as any, createDto);

      expect(examService.createExam).toHaveBeenCalledWith(
        mockIndependentTeacher.sub,
        createDto,
        mockIndependentTeacher.schoolId
      );
    });
  });

  describe('getExam', () => {
    it('should allow INDEPENDENT_TEACHER to access their own school exams', async () => {
      examService.getExamById.mockResolvedValue(mockExam as any);

      const result = await controller.getExam(mockIndependentTeacher as any, 'exam-id');

      expect(examService.getExamById).toHaveBeenCalledWith(
        mockIndependentTeacher.sub,
        'exam-id',
        mockIndependentTeacher.role,
        mockIndependentTeacher.schoolId
      );
      expect(result).toEqual(mockExam);
    });

    it('should prevent INDEPENDENT_TEACHER from accessing other school exams', async () => {
      examService.getExamById.mockRejectedValue(new NotFoundException('Exam not found'));

      await expect(
        controller.getExam(mockIndependentTeacher as any, 'other-exam-id')
      ).rejects.toThrow(NotFoundException);

      expect(examService.getExamById).toHaveBeenCalledWith(
        mockIndependentTeacher.sub,
        'other-exam-id',
        mockIndependentTeacher.role,
        mockIndependentTeacher.schoolId
      );
    });
  });

  describe('submitExam', () => {
    it('should allow INDEPENDENT_TEACHER to submit their own school exams', async () => {
      const submitDto: SubmitExamDto = {
        answers: [{ questionIndex: 0, userAnswer: ['A'] }],
      };

      const mockResult = {
        score: 80,
        total: 100,
        detail: { correct: 4, incorrect: 1 },
      };

      examService.submitExam.mockResolvedValue(mockResult);

      const result = await controller.submitExam(
        mockIndependentTeacher as any,
        'exam-id',
        submitDto
      );

      expect(examService.submitExam).toHaveBeenCalledWith(
        mockIndependentTeacher.sub,
        'exam-id',
        submitDto,
        mockIndependentTeacher.schoolId
      );
      expect(result).toEqual(mockResult);
    });

    it('should prevent INDEPENDENT_TEACHER from submitting other school exams', async () => {
      const submitDto: SubmitExamDto = {
        answers: [{ questionIndex: 0, userAnswer: ['A'] }],
      };

      examService.submitExam.mockRejectedValue(new NotFoundException('Exam not found'));

      await expect(
        controller.submitExam(mockIndependentTeacher as any, 'other-exam-id', submitDto)
      ).rejects.toThrow(NotFoundException);

      expect(examService.submitExam).toHaveBeenCalledWith(
        mockIndependentTeacher.sub,
        'other-exam-id',
        submitDto,
        mockIndependentTeacher.schoolId
      );
    });
  });

  describe('getExamsByWorksheet', () => {
    it('should allow INDEPENDENT_TEACHER to get exams from their own school worksheets', async () => {
      const mockExams = [mockExam];
      examService.getExamsByWorksheetId.mockResolvedValue(mockExams as any);

      const result = await controller.getExamsByWorksheet('worksheet-id', mockIndependentTeacher as any);

      expect(examService.getExamsByWorksheetId).toHaveBeenCalledWith(
        'worksheet-id',
        mockIndependentTeacher.schoolId
      );
      expect(result).toEqual(mockExams);
    });

    it('should only return exams from INDEPENDENT_TEACHER school', async () => {
      const mockExams = [mockExam]; // Only exams from their school
      examService.getExamsByWorksheetId.mockResolvedValue(mockExams as any);

      const result = await controller.getExamsByWorksheet('worksheet-id', mockIndependentTeacher as any);

      expect(result).toHaveLength(1);
      expect(result[0].schoolId).toBe(mockIndependentTeacher.schoolId);
    });
  });
});
