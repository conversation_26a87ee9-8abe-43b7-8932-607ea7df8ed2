import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Not } from 'typeorm';
import { ExamAssignment, AssignmentStatus } from './entities/exam-assignment.entity';
import { Exam } from './entities/exam.entity';
import { User } from '../user/entities/user.entity';
import { ExamResult } from './entities/exam-result.entity';
import { FilterUserDto } from '../user/dto/filter-user.dto';
import { EUserRole } from '../user/dto/create-user.dto';
import { UpdateExamAssignmentDto } from './dto/update-exam-assignment.dto';

@Injectable()
export class ExamAssignmentService {
  private readonly logger = new Logger(ExamAssignmentService.name);

  constructor(
    @InjectRepository(ExamAssignment)
    private readonly examAssignmentRepository: Repository<ExamAssignment>,
    @InjectRepository(Exam)
    private readonly examRepository: Repository<Exam>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(ExamResult)
    private readonly examResultRepository: Repository<ExamResult>,
  ) {}

  /**
   * Bulk create assignments for multiple students with validation
   * @param examId - The ID of the exam to assign
   * @param studentIds - Array of student IDs to assign the exam to
   * @param expiresAt - Optional expiration date for the assignments
   * @param totalAttempts - Optional total number of attempts allowed
   * @returns Array of created exam assignments
   */
  async createAssignments(
    examId: string,
    studentIds: string[],
    expiresAt?: string,
    totalAttempts?: number,
  ): Promise<ExamAssignment[]> {
    // Validate exam exists
    const exam = await this.examRepository.findOne({ where: { id: examId } });
    if (!exam) {
      throw new NotFoundException('Exam not found');
    }

    // Remove duplicates from studentIds
    const uniqueStudentIds = [...new Set(studentIds)];

    // Validate all students exist
    const students = await this.userRepository.find({
      where: { id: In(uniqueStudentIds) },
    });

    if (students.length !== uniqueStudentIds.length) {
      const foundIds = students.map(s => s.id);
      const missingIds = uniqueStudentIds.filter(id => !foundIds.includes(id));
      throw new NotFoundException(
        `Students not found: ${missingIds.join(', ')}`,
      );
    }

    // Check for existing assignments
    const existingAssignments = await this.examAssignmentRepository.find({
      where: {
        examId,
        studentId: In(uniqueStudentIds),
      },
    });

    if (existingAssignments.length > 0) {
      const existingStudentIds = existingAssignments.map(a => a.studentId);
      throw new BadRequestException(
        `Assignments already exist for students: ${existingStudentIds.join(', ')}`,
      );
    }

    // Determine expiration date and total attempts using exam defaults as fallback
    let calculatedExpiresAt: Date | undefined = expiresAt ? new Date(expiresAt) : undefined;
    if (!calculatedExpiresAt && exam.defaultExpiresInDays) {
      calculatedExpiresAt = new Date();
      calculatedExpiresAt.setDate(calculatedExpiresAt.getDate() + exam.defaultExpiresInDays);
    }

    const calculatedTotalAttempts = totalAttempts ?? exam.defaultTotalAttempts ?? 1;

    // Create assignments
    const assignments: ExamAssignment[] = [];
    for (const studentId of uniqueStudentIds) {
      const assignment = this.examAssignmentRepository.create({
        examId,
        studentId,
        status: AssignmentStatus.ASSIGNED,
        score: 0,
        expiresAt: calculatedExpiresAt,
        totalAttempts: calculatedTotalAttempts,
      });
      assignments.push(assignment);
    }

    // Bulk save
    const savedAssignments = await this.examAssignmentRepository.save(assignments);
    
    this.logger.log(
      `Created ${savedAssignments.length} assignments for exam ${examId}`,
    );

    return savedAssignments;
  }

  /**
   * Fetch all exam assignments for a specific student with exam details
   * @param studentId - The ID of the student
   * @returns Array of exam assignments with exam details
   */
  async getAssignmentsForStudent(
    studentId: string,
  ): Promise<ExamAssignment[]> {
    const assignments = await this.examAssignmentRepository.find({
      where: { studentId },
      relations: ['exam', 'exam.user'],
      order: {
        createdAt: 'DESC',
      },
    });

    return assignments;
  }

  /**
   * Check if an assignment exists for a specific exam and student
   * @param examId - The ID of the exam
   * @param studentId - The ID of the student
   * @returns The exam assignment if found, undefined otherwise
   */
  async findAssignment(
    examId: string,
    studentId: string,
  ): Promise<ExamAssignment | undefined> {
    const assignment = await this.examAssignmentRepository.findOne({
      where: { examId, studentId },
      relations: ['exam', 'student'],
    });

    return assignment || undefined;
  }

  /**
   * Update the status of an exam assignment and update timestamps
   * @param examId - The ID of the exam
   * @param studentId - The ID of the student
   * @param status - The new assignment status
   * @returns The updated exam assignment
   */
  async updateAssignmentStatus(
    examId: string,
    studentId: string,
    status: AssignmentStatus,
  ): Promise<ExamAssignment> {
    const assignment = await this.examAssignmentRepository.findOne({
      where: { examId, studentId },
    });

    if (!assignment) {
      throw new NotFoundException('Assignment not found');
    }

    // Validate status transition
    if (assignment.status === AssignmentStatus.COMPLETED && 
        status !== AssignmentStatus.COMPLETED) {
      throw new BadRequestException(
        'Cannot change status of a completed assignment',
      );
    }

    // Update status
    assignment.status = status;
    
    // Update timestamps based on status
    const now = new Date();
    if (status === AssignmentStatus.IN_PROGRESS && !assignment.startedAt) {
      assignment.startedAt = now;
    } else if (status === AssignmentStatus.COMPLETED && !assignment.completedAt) {
      assignment.completedAt = now;
    }
    
    // Note: updatedAt is automatically updated by TypeORM due to @UpdateDateColumn in BaseEntity
    const updatedAssignment = await this.examAssignmentRepository.save(assignment);

    this.logger.log(
      `Updated assignment status for exam ${examId}, student ${studentId} to ${status}`,
    );

    return updatedAssignment;
  }

  /**
   * Get all assignments for a specific exam with student details
   * @param examId - The ID of the exam
   * @returns Array of exam assignments with student details
   */
  async getAssignmentsForExam(
    examId: string,
  ): Promise<ExamAssignment[]> {
    const assignments = await this.examAssignmentRepository.find({
      where: { examId },
      relations: ['student'],
      order: {
        assignedAt: 'ASC',
      },
    });

    this.logger.log(
      `Found ${assignments.length} assignments for exam ${examId}`,
    );

    return assignments;
  }

  /**
   * Get all students who are not assigned to a specific exam
   * @param examId - The ID of the exam
   * @param filterDto - Filter options for students
   * @param user - The authenticated user making the request
   * @returns Array of unassigned students
   */
  async getUnassignedStudents(
    examId: string,
    filterDto: FilterUserDto,
    user: any,
  ): Promise<User[]> {
    // Validate exam exists
    const exam = await this.examRepository.findOne({
      where: { id: examId },
      relations: ['user']
    });

    if (!exam) {
      throw new NotFoundException('Exam not found');
    }

    // Check permissions based on user role
    if (user.role === EUserRole.TEACHER || user.role === EUserRole.INDEPENDENT_TEACHER) {
      // Teachers can only see unassigned students for their own exams
      if (exam.userId !== user.sub) {
        throw new ForbiddenException('You can only view unassigned students for your own exams');
      }
    } else if (user.role === EUserRole.SCHOOL_MANAGER) {
      // School managers can only see unassigned students for exams from their school
      if (user.schoolId && exam.schoolId !== user.schoolId) {
        throw new ForbiddenException('You can only view unassigned students for exams from your own school');
      }
    }
    // Admin users can access any exam

    // Apply school filtering based on user role and permissions
    const studentFilter: any = { role: EUserRole.STUDENT };

    if (filterDto.schoolId) {
      // Explicit schoolId provided
      if (user.role === EUserRole.SCHOOL_MANAGER || user.role === EUserRole.INDEPENDENT_TEACHER) {
        // School managers and independent teachers can only filter by their own school
        if (user.schoolId && filterDto.schoolId !== user.schoolId) {
          throw new ForbiddenException('You can only filter students from your own school');
        }
      }
      studentFilter.schoolId = filterDto.schoolId;
    } else {
      // No explicit schoolId provided
      if (user.role !== EUserRole.ADMIN) {
        // Non-admin users use their own school
        if (user.schoolId) {
          studentFilter.schoolId = user.schoolId;
        } else {
          // User with no school association - return empty results
          return [];
        }
      }
      // Admin users without schoolId filter see all students
    }

    // Get all students based on filter
    const allStudents = await this.userRepository.find({
      where: studentFilter,
      select: ['id', 'name', 'email', 'schoolId'],
      order: { name: 'ASC' }
    });

    // Get all existing assignments for this exam
    const existingAssignments = await this.examAssignmentRepository.find({
      where: { examId },
      select: ['studentId']
    });

    // Extract assigned student IDs
    const assignedStudentIds = existingAssignments.map(assignment => assignment.studentId);

    // Filter out assigned students
    const unassignedStudents = allStudents.filter(
      student => !assignedStudentIds.includes(student.id)
    );

    this.logger.log(
      `Found ${unassignedStudents.length} unassigned students for exam ${examId}`,
    );

    return unassignedStudents;
  }

  /**
   * Update an exam assignment's expiration date and/or total attempts
   * @param assignmentId - The ID of the assignment to update
   * @param updateDto - The update data containing new values
   * @returns The updated exam assignment
   */
  async updateAssignment(
    assignmentId: string,
    updateDto: UpdateExamAssignmentDto,
  ): Promise<ExamAssignment> {
    const assignment = await this.examAssignmentRepository.findOne({
      where: { id: assignmentId },
      relations: ['exam', 'student'],
    });

    if (!assignment) {
      throw new NotFoundException('Assignment not found');
    }

    // Validate that assignment hasn't been completed
    if (assignment.status === AssignmentStatus.COMPLETED) {
      throw new BadRequestException(
        'Cannot update a completed assignment',
      );
    }

    // Update fields if provided
    if (updateDto.expiresAt !== undefined) {
      assignment.expiresAt = updateDto.expiresAt ? new Date(updateDto.expiresAt) : undefined;
    }

    if (updateDto.totalAttempts !== undefined) {
      assignment.totalAttempts = updateDto.totalAttempts;
    }

    const updatedAssignment = await this.examAssignmentRepository.save(assignment);

    this.logger.log(
      `Updated assignment ${assignmentId} with new settings`,
    );

    return updatedAssignment;
  }

  /**
   * Check if a student can start a new exam attempt
   * @param examId - The ID of the exam
   * @param studentId - The ID of the student
   * @returns Boolean indicating if the student can start a new attempt
   */
  async canStudentStartAttempt(
    examId: string,
    studentId: string,
  ): Promise<{ canStart: boolean; reason?: string }> {
    const assignment = await this.examAssignmentRepository.findOne({
      where: { examId, studentId },
    });

    if (!assignment) {
      return { canStart: false, reason: 'Assignment not found' };
    }

    // Check expiration
    if (assignment.expiresAt && new Date() > assignment.expiresAt) {
      return { canStart: false, reason: 'Assignment has expired' };
    }

    // Check attempt limits
    const existingAttempts = await this.examResultRepository.count({
      where: { examId, studentId },
    });

    if (existingAttempts >= assignment.totalAttempts) {
      return { 
        canStart: false, 
        reason: `Maximum attempts (${assignment.totalAttempts}) reached` 
      };
    }

    return { canStart: true };
  }

  /**
   * Validate if a student can start an exam attempt and throw exception if not
   * @param examId - The ID of the exam
   * @param studentId - The ID of the student
   */
  async validateStudentCanStartAttempt(
    examId: string,
    studentId: string,
  ): Promise<void> {
    const result = await this.canStudentStartAttempt(examId, studentId);
    
    if (!result.canStart) {
      throw new BadRequestException(result.reason);
    }
  }
}
