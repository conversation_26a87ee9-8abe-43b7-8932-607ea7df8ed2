import { Controller, Post, Get, Body, Param, UseGuards, Query, ForbiddenException, Patch } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { ExamService } from './exam.service';
import { CreateExamDto } from './dto/create-exam.dto';
import { SubmitExamDto } from './dto/submit-exam.dto';
import { GetUserExamsDto } from './dto/get-user-exams.dto';
import { GetExamResponseDto } from './dto/get-exam-response.dto';
import { AssignExamDto } from './dto/assign-exam.dto';
import { UpdateExamAssignmentDto } from './dto/update-exam-assignment.dto';
import { GetStudentExamResultDto } from './dto/get-student-exam-result.dto';
import { ActiveUser } from '../auth/decorators/active-user.decorator';
import { Roles } from '../auth/decorators/role.decorator';
import { AuthGuard } from '../auth/guards/auth.guard';
import { RoleGuard } from '../auth/guards/role.guard';
import { EUserRole } from '../user/dto/create-user.dto';
import { ExamAssignmentService } from './exam-assignment.service';
import { ExamAssignment } from './entities/exam-assignment.entity';
import { FilterUserDto } from '../user/dto/filter-user.dto';
import { User } from '../user/entities/user.entity';

@ApiTags('Exams')
@Controller('exams')
@UseGuards(AuthGuard, RoleGuard)
@ApiBearerAuth()
export class ExamController {
  constructor(
    private readonly examService: ExamService,
    private readonly examAssignmentService: ExamAssignmentService,
  ) {}

  @Post()
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ summary: 'Create a new exam' })
  @ApiResponse({ status: 201, description: 'Exam created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden - You already have an unfinished exam for this worksheet' })
  async createExam(@ActiveUser() user: any, @Body() dto: CreateExamDto) {
    const userId = user.sub;
    const userSchoolId = user.schoolId;
    return this.examService.createExam(userId, dto, userSchoolId);
  }

  @Get()
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER, EUserRole.STUDENT)
  @ApiOperation({ summary: 'Get all exams for the current user' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Exams retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        items: {
          type: 'array',
          items: { type: 'object' }
        },
        currentPage: { type: 'number' },
        pageSize: { type: 'number' },
        totalItems: { type: 'number' },
        totalPages: { type: 'number' }
      }
    }
  })
  async getExamsForUser(
    @ActiveUser() user: any,
    @Query() query: GetUserExamsDto,
  ) {
    const userId = user.sub;
    const role = user.role;
    const { page, limit } = query;
    const result = await this.examService.getExamsForUser(userId, { page, limit }, role);
    
    const totalPages = Math.ceil(result.total / limit);
    
    return {
      items: result.exams,
      currentPage: page,
      pageSize: limit,
      totalItems: result.total,
      totalPages,
    };
  }

  @Get(':id')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER, EUserRole.STUDENT)
  @ApiOperation({ summary: 'Get exam by ID with detailed statistics and results' })
  @ApiParam({ name: 'id', description: 'Exam ID' })
  @ApiResponse({
    status: 200,
    description: 'Exam retrieved successfully with statistics and results',
    type: GetExamResponseDto
  })
  @ApiResponse({ status: 404, description: 'Exam not found' })
  async getExam(@ActiveUser() user: any, @Param('id') id: string): Promise<GetExamResponseDto> {
    const userId = user.sub;
    const role = user.role;
    const userSchoolId = user.schoolId;
    return this.examService.getExamById(userId, id, role, userSchoolId);
  }

  @Get(':examId/results/:studentId')
  @Roles(EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ summary: 'Get detailed exam result for a specific student' })
  @ApiParam({ name: 'examId', description: 'Exam ID' })
  @ApiParam({ name: 'studentId', description: 'Student ID' })
  @ApiResponse({
    status: 200,
    description: 'Student exam result retrieved successfully',
    type: GetStudentExamResultDto
  })
  @ApiResponse({ status: 404, description: 'Exam not found, student result not found, or student not assigned to exam' })
  @ApiResponse({ status: 403, description: 'Forbidden - You can only view results for exams you own' })
  async getStudentExamResult(
    @ActiveUser() user: any,
    @Param('examId') examId: string,
    @Param('studentId') studentId: string,
  ): Promise<GetStudentExamResultDto> {
    const teacherId = user.sub;
    const teacherRole = user.role;
    return this.examService.getStudentExamResult(examId, studentId, teacherId, teacherRole);
  }

  @Post(':id/submit')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER, EUserRole.STUDENT)
  @ApiOperation({ summary: 'Submit exam answers' })
  @ApiParam({ name: 'id', description: 'Exam ID' })
  @ApiResponse({ status: 200, description: 'Exam submitted successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - You have already submitted this exam' })
  @ApiResponse({ status: 404, description: 'Exam not found' })
  async submitExam(
    @ActiveUser() user: any,
    @Param('id') id: string,
    @Body() dto: SubmitExamDto,
  ) {
    const userId = user.sub;
    const userSchoolId = user.schoolId;
    return this.examService.submitExam(userId, id, dto, userSchoolId);
  }

  @Get('by-worksheet/:worksheetId')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ summary: 'Get exams by worksheet ID' })
  @ApiParam({ name: 'worksheetId', description: 'Worksheet ID' })
  @ApiResponse({ status: 200, description: 'Exams retrieved successfully' })
  async getExamsByWorksheet(@Param('worksheetId') worksheetId: string, @ActiveUser() user: any) {
    return this.examService.getExamsByWorksheetId(worksheetId);
  }

  @Post(':id/assign')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ summary: 'Assign an exam to students' })
  @ApiParam({ name: 'id', description: 'Exam ID' })
  @ApiResponse({ 
    status: 201, 
    description: 'Exam assigned successfully',
    type: [ExamAssignment],
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid student IDs or assignments already exist' })
  @ApiResponse({ status: 404, description: 'Exam not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - You can only assign your own exams' })
  async assignExam(
    @ActiveUser() user: any,
    @Param('id') examId: string,
    @Body() dto: AssignExamDto,
  ): Promise<ExamAssignment[]> {
    const userId = user.sub;
    const userRole = user.role;
    const userSchoolId = user.schoolId;

    // Verify the exam belongs to the teacher (for non-admin users)
    const exam = await this.examService.getExamById(userId, examId, userRole, userSchoolId);
    
    // For teachers, ensure they own the exam
    if (userRole === EUserRole.TEACHER && exam.userId !== userId) {
      throw new ForbiddenException('You can only assign your own exams');
    }

    // For school managers, ensure the exam is from their school
    if (userRole === EUserRole.SCHOOL_MANAGER && userSchoolId && exam.schoolId !== userSchoolId) {
      throw new ForbiddenException('You can only assign exams from your own school');
    }

    return this.examAssignmentService.createAssignments(examId, dto.studentIds, dto.expiresAt, dto.totalAttempts);
  }

  @Patch(':examId/assignments/:assignmentId')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ summary: 'Update exam assignment expiration and attempt limits' })
  @ApiParam({ name: 'examId', description: 'Exam ID' })
  @ApiParam({ name: 'assignmentId', description: 'Assignment ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Assignment updated successfully',
    type: ExamAssignment,
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid data or assignment already completed' })
  @ApiResponse({ status: 404, description: 'Assignment not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - You can only update assignments for your own exams' })
  async updateExamAssignment(
    @ActiveUser() user: any,
    @Param('examId') examId: string,
    @Param('assignmentId') assignmentId: string,
    @Body() dto: UpdateExamAssignmentDto,
  ): Promise<ExamAssignment> {
    const userId = user.sub;
    const userRole = user.role;
    const userSchoolId = user.schoolId;

    // Verify the exam belongs to the teacher (for non-admin users)
    const exam = await this.examService.getExamById(userId, examId, userRole, userSchoolId);
    
    // For teachers, ensure they own the exam
    if (userRole === EUserRole.TEACHER && exam.userId !== userId) {
      throw new ForbiddenException('You can only update assignments for your own exams');
    }

    // For school managers, ensure the exam is from their school
    if (userRole === EUserRole.SCHOOL_MANAGER && userSchoolId && exam.schoolId !== userSchoolId) {
      throw new ForbiddenException('You can only update assignments for exams from your own school');
    }

    return this.examAssignmentService.updateAssignment(assignmentId, dto);
  }

  @Get(':id/unassigned-students')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ summary: 'Get students who are not assigned to a specific exam' })
  @ApiParam({ name: 'id', description: 'Exam ID' })
  @ApiQuery({
    name: 'schoolId',
    required: false,
    type: String,
    description: 'Filter students by school ID (UUID format). Admin users can specify any school ID. Non-admin users can only filter by their own school.'
  })
  @ApiResponse({
    status: 200,
    description: 'Unassigned students retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', description: 'Student ID' },
          name: { type: 'string', description: 'Student name' },
          email: { type: 'string', description: 'Student email' },
          schoolId: { type: 'string', nullable: true, description: 'School ID' },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Exam not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - You can only view unassigned students for exams you have access to' })
  async getUnassignedStudents(
    @ActiveUser() user: any,
    @Param('id') examId: string,
    @Query() filterDto: FilterUserDto,
  ): Promise<User[]> {
    return this.examAssignmentService.getUnassignedStudents(examId, filterDto, user);
  }
}
