import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsDateString, IsInt, Min } from 'class-validator';

export class UpdateExamAssignmentDto {
  @ApiProperty({
    description: 'The date and time when the exam assignment expires (ISO 8601 format)',
    example: '2024-01-20T23:59:59Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiProperty({
    description: 'The total number of attempts allowed for this assignment',
    example: 3,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  totalAttempts?: number;
} 