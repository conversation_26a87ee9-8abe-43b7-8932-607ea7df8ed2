import { ApiProperty } from '@nestjs/swagger';

export class QuestionResultDto {
  @ApiProperty({ description: 'Question index in the exam' })
  questionIndex: number;

  @ApiProperty({ description: 'Question content/text' })
  content: string;

  @ApiProperty({ description: 'Question type (single_choice, multiple_choice, fill_blank, creative_writing)' })
  type: string;

  @ApiProperty({ description: 'Student\'s submitted answer', type: [String] })
  studentAnswer: string[];

  @ApiProperty({ description: 'Correct answer', type: [String] })
  correctAnswer: string[];

  @ApiProperty({ description: 'Explanation for the correct answer', nullable: true })
  explanation?: string;

  @ApiProperty({ description: 'Whether the student\'s answer is correct' })
  isCorrect: boolean;

  @ApiProperty({ description: 'Score for this question (1 if correct, 0 if incorrect)' })
  score: number;
}

export class GetStudentExamResultDto {
  @ApiProperty({ description: 'Student ID' })
  studentId: string;

  @ApiProperty({ description: 'Student name' })
  studentName: string;

  @ApiProperty({ description: 'Exam ID' })
  examId: string;

  @ApiProperty({ description: 'Exam title' })
  examTitle: string;

  @ApiProperty({ description: 'Total score achieved by student' })
  score: number;

  @ApiProperty({ description: 'Total possible score' })
  total: number;

  @ApiProperty({ description: 'Percentage score' })
  percentage: number;

  @ApiProperty({ enum: ['passed', 'failed'], description: 'Pass/fail status based on passing score' })
  status: 'passed' | 'failed';

  @ApiProperty({ description: 'Time spent on exam in seconds', nullable: true })
  timeSpent?: number;

  @ApiProperty({ description: 'Submission timestamp' })
  submittedAt: Date;

  @ApiProperty({ type: [QuestionResultDto], description: 'Detailed breakdown of each question' })
  questions: QuestionResultDto[];
}
