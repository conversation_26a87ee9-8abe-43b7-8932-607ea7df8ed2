import { ApiProperty } from '@nestjs/swagger';
import { Exam } from '../entities/exam.entity';
import { AssignmentStatus } from '../entities/exam-assignment.entity';

// Define the structure for assigned student details
export class IExamAssignmentDetail {
  @ApiProperty({ description: 'Student ID' })
  studentId: string;

  @ApiProperty({ description: 'Student name' })
  studentName: string;

  @ApiProperty({ description: 'Student email' })
  studentEmail: string;

  @ApiProperty({ enum: AssignmentStatus, description: 'Assignment status' })
  status: AssignmentStatus;

  @ApiProperty({ description: 'When the exam was assigned to the student' })
  assignedAt: Date;

  @ApiProperty({ description: 'When the student first opened the exam', required: false })
  startedAt?: Date;

  @ApiProperty({ description: 'When the student submitted the exam', required: false })
  completedAt?: Date;

  @ApiProperty({ description: 'Score achieved (only available if completed)', required: false })
  score?: number;

  @ApiProperty({ description: 'Feedback for the assignment', required: false })
  feedback?: string;
}

// Define the structure for individual result details
export class IExamResultDetail {
  @ApiProperty({ description: 'Student ID' })
  studentId: string;

  @ApiProperty({ description: 'Student name' })
  studentName: string;

  @ApiProperty({ description: 'Score achieved' })
  score: number;

  @ApiProperty({ description: 'Total possible score' })
  total: number;

  @ApiProperty({ description: 'Number of correct answers' })
  correctAnswers: number;

  @ApiProperty({ description: 'Total number of questions' })
  totalQuestions: number;

  @ApiProperty({ enum: ['passed', 'failed'], description: 'Pass/fail status' })
  status: 'passed' | 'failed';

  @ApiProperty({ description: 'Submission timestamp' })
  submittedAt: Date;

  @ApiProperty({ description: 'Time spent on exam in seconds', required: false })
  timeSpent?: number;
}

// Define the structure for the exam statistics
export class ExamStats {
  @ApiProperty({ description: 'Total number of submissions' })
  totalSubmissions: number;

  @ApiProperty({ description: 'Pass rate as a percentage' })
  passRate: number;

  @ApiProperty({ description: 'Average score across all submissions' })
  averageScore: number;

  @ApiProperty({ description: 'Highest score achieved' })
  highestScore: number;

  @ApiProperty({ description: 'Lowest score achieved' })
  lowestScore: number;

  @ApiProperty({ description: 'Average time spent in seconds' })
  averageTimeSpent: number;
}

// Define the main response DTO
export class GetExamResponseDto extends Exam {
  @ApiProperty({ type: () => ExamStats, description: 'Exam statistics' })
  stats: ExamStats;

  @ApiProperty({ type: () => [IExamResultDetail], description: 'List of exam results (completed submissions only)' })
  results: IExamResultDetail[];

  @ApiProperty({ type: () => [IExamAssignmentDetail], description: 'List of all assigned students with their status' })
  assignments: IExamAssignmentDetail[];
}