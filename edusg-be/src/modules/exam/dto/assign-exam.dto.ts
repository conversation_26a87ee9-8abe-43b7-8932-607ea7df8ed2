import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON>rray, IsNotEmpty, IsUUID, ArrayMinSize, ArrayUnique, IsOptional, IsDateString, IsInt, Min } from 'class-validator';

export class AssignExamDto {
  @ApiProperty({
    description: 'Array of student IDs to assign the exam to',
    example: ['uuid-1', 'uuid-2', 'uuid-3'],
    type: [String],
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsUUID('4', { each: true })
  @IsNotEmpty()
  studentIds: string[];

  @ApiProperty({
    description: 'The date and time when the exam assignment expires (ISO 8601 format)',
    example: '2024-01-20T23:59:59Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiProperty({
    description: 'The total number of attempts allowed for this assignment',
    example: 3,
    default: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  totalAttempts?: number;
}
