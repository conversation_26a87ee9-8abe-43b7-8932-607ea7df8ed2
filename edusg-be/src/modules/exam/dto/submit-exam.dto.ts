import { IsA<PERSON>y, IsInt, IsString, ValidateNested, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

class ExamAnswerDto {
  @IsInt()
  questionIndex: number;

  @IsArray()
  @IsString({ each: true })
  userAnswer: string[];
}

export class SubmitExamDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExamAnswerDto)
  @ApiProperty({
    description: 'Array of exam answers',
    type: [ExamAnswerDto],
  })
  answers: ExamAnswerDto[];

  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(86400) // Maximum 24 hours in seconds
  @ApiProperty({
    description: 'Time spent on exam in seconds',
    example: 1800,
    minimum: 0,
    maximum: 86400,
    required: false,
  })
  timeSpent?: number;
}
