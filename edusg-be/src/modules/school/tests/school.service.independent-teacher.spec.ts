import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ForbiddenException, NotFoundException, BadRequestException } from '@nestjs/common';
import { SchoolService } from '../school.service';
import { School } from '../entities/school.entity';
import { BrandService } from '../../brand/brand.service';
import { UserService } from '../../user/user.service';
import { EUserRole } from '../../user/dto/create-user.dto';
import { CreateSchoolDto } from '../dto/create-school.dto';
import { UpdateSchoolDto } from '../dto/update-school.dto';

describe('SchoolService - Independent Teacher Features', () => {
  let service: SchoolService;
  let schoolRepository: jest.Mocked<Repository<School>>;
  let userService: jest.Mocked<UserService>;
  let brandService: jest.Mocked<BrandService>;

  const mockSchoolRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
    merge: jest.fn(),
    remove: jest.fn(),
  };

  const mockUserService = {
    findOne: jest.fn(),
    updateSchoolId: jest.fn(),
  };

  const mockBrandService = {
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SchoolService,
        {
          provide: getRepositoryToken(School),
          useValue: mockSchoolRepository,
        },
        {
          provide: UserService,
          useValue: mockUserService,
        },
        {
          provide: BrandService,
          useValue: mockBrandService,
        },
      ],
    }).compile();

    service = module.get<SchoolService>(SchoolService);
    schoolRepository = module.get(getRepositoryToken(School));
    userService = module.get(UserService);
    brandService = module.get(BrandService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create - INDEPENDENT_TEACHER', () => {
    const createSchoolDto: CreateSchoolDto = {
      name: 'Independent Teacher School',
      address: '123 Education St',
      phoneNumber: '******-123-4567',
      registeredNumber: 'REG12345',
      email: '<EMAIL>',
    };

    const independentTeacherId = 'teacher-123';
    const schoolId = 'school-456';

    it('should successfully create a school for INDEPENDENT_TEACHER', async () => {
      // Mock no existing school for the teacher
      schoolRepository.findOne.mockResolvedValue(null);
      
      const mockSchool = {
        id: schoolId,
        ...createSchoolDto,
        adminId: independentTeacherId,
      };
      
      schoolRepository.create.mockReturnValue(mockSchool as School);
      schoolRepository.save.mockResolvedValue(mockSchool as School);
      userService.updateSchoolId.mockResolvedValue({} as any);

      const result = await service.create(
        createSchoolDto,
        independentTeacherId,
        EUserRole.INDEPENDENT_TEACHER
      );

      expect(schoolRepository.findOne).toHaveBeenCalledWith({
        where: { adminId: independentTeacherId }
      });
      expect(schoolRepository.create).toHaveBeenCalledWith({
        ...createSchoolDto,
        adminId: independentTeacherId,
        brandId: undefined,
      });
      expect(schoolRepository.save).toHaveBeenCalledWith(mockSchool);
      expect(userService.updateSchoolId).toHaveBeenCalledWith(independentTeacherId, schoolId);
      expect(result).toEqual(mockSchool);
    });

    it('should throw ForbiddenException if INDEPENDENT_TEACHER already owns a school', async () => {
      const existingSchool = {
        id: 'existing-school',
        adminId: independentTeacherId,
        name: 'Existing School',
      };
      
      schoolRepository.findOne.mockResolvedValue(existingSchool as School);

      await expect(
        service.create(createSchoolDto, independentTeacherId, EUserRole.INDEPENDENT_TEACHER)
      ).rejects.toThrow(ForbiddenException);
      
      expect(schoolRepository.findOne).toHaveBeenCalledWith({
        where: { adminId: independentTeacherId }
      });
      expect(schoolRepository.create).not.toHaveBeenCalled();
      expect(userService.updateSchoolId).not.toHaveBeenCalled();
    });

    it('should handle brand validation for INDEPENDENT_TEACHER', async () => {
      const createSchoolWithBrandDto = {
        ...createSchoolDto,
        brandId: 'brand-123',
      };

      schoolRepository.findOne.mockResolvedValue(null);
      brandService.findOne.mockResolvedValue({} as any);
      
      const mockSchool = {
        id: schoolId,
        ...createSchoolWithBrandDto,
        adminId: independentTeacherId,
      };
      
      schoolRepository.create.mockReturnValue(mockSchool as School);
      schoolRepository.save.mockResolvedValue(mockSchool as School);
      userService.updateSchoolId.mockResolvedValue({} as any);

      await service.create(
        createSchoolWithBrandDto,
        independentTeacherId,
        EUserRole.INDEPENDENT_TEACHER
      );

      expect(brandService.findOne).toHaveBeenCalledWith('brand-123');
      expect(schoolRepository.create).toHaveBeenCalledWith({
        ...createSchoolDto,
        adminId: independentTeacherId,
        brandId: 'brand-123',
      });
    });
  });

  describe('updateOwnSchool', () => {
    const teacherId = 'teacher-123';
    const updateSchoolDto: UpdateSchoolDto = {
      name: 'Updated School Name',
      address: 'New Address',
    };

    it('should successfully update own school', async () => {
      const existingSchool = {
        id: 'school-123',
        adminId: teacherId,
        name: 'Original School',
        address: 'Original Address',
      };

      const updatedSchool = {
        ...existingSchool,
        ...updateSchoolDto,
      };

      schoolRepository.findOne.mockResolvedValue(existingSchool as School);
      schoolRepository.merge.mockImplementation((target, source) => Object.assign(target, source));
      schoolRepository.save.mockResolvedValue(updatedSchool as School);

      const result = await service.updateOwnSchool(teacherId, updateSchoolDto);

      expect(schoolRepository.findOne).toHaveBeenCalledWith({
        where: { adminId: teacherId },
        relations: ['admin', 'brand']
      });
      expect(schoolRepository.merge).toHaveBeenCalledWith(existingSchool, updateSchoolDto);
      expect(schoolRepository.save).toHaveBeenCalledWith(existingSchool);
      expect(result).toEqual(updatedSchool);
    });

    it('should throw NotFoundException if teacher has no school', async () => {
      schoolRepository.findOne.mockResolvedValue(null);

      await expect(
        service.updateOwnSchool(teacherId, updateSchoolDto)
      ).rejects.toThrow(NotFoundException);
      
      expect(schoolRepository.findOne).toHaveBeenCalledWith({
        where: { adminId: teacherId },
        relations: ['admin', 'brand']
      });
      expect(schoolRepository.save).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException if trying to change adminId', async () => {
      const invalidUpdateDto = {
        ...updateSchoolDto,
        adminId: 'different-teacher',
      };

      const existingSchool = {
        id: 'school-123',
        adminId: teacherId,
      };

      schoolRepository.findOne.mockResolvedValue(existingSchool as School);

      await expect(
        service.updateOwnSchool(teacherId, invalidUpdateDto)
      ).rejects.toThrow(BadRequestException);
      
      expect(schoolRepository.save).not.toHaveBeenCalled();
    });
  });

  describe('remove - INDEPENDENT_TEACHER restrictions', () => {
    const schoolId = 'school-123';

    it('should throw ForbiddenException when INDEPENDENT_TEACHER tries to delete school', async () => {
      const requestingUser = { role: EUserRole.INDEPENDENT_TEACHER };

      await expect(
        service.remove(schoolId, requestingUser)
      ).rejects.toThrow(ForbiddenException);
      
      expect(schoolRepository.remove).not.toHaveBeenCalled();
    });

    it('should allow ADMIN to delete school', async () => {
      const requestingUser = { role: EUserRole.ADMIN };
      const mockSchool = { id: schoolId, name: 'Test School' };

      jest.spyOn(service, 'findOne').mockResolvedValue(mockSchool as School);
      schoolRepository.remove.mockResolvedValue({} as any);

      await service.remove(schoolId, requestingUser);

      expect(service.findOne).toHaveBeenCalledWith(schoolId);
      expect(schoolRepository.remove).toHaveBeenCalledWith(mockSchool);
    });
  });
});
