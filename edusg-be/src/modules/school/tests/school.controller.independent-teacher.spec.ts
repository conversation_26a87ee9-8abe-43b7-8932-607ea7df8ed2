import { Test, TestingModule } from '@nestjs/testing';
import { ForbiddenException, NotFoundException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Reflector } from '@nestjs/core';
import { SchoolController } from '../school.controller';
import { SchoolService } from '../school.service';
import { DocumentsService } from '../../documents/documents.service';
import { MultimodalService } from '../../multimodal/multimodal.service';
import { NarrativeStructureService } from '../services/narrative-structure.service';
import { RbacService } from '../../auth/services/rbac.service';
import { AuthGuard } from '../../auth/guards/auth.guard';
import { RoleGuard } from '../../auth/guards/role.guard';
import { EUserRole } from '../../user/dto/create-user.dto';
import { CreateSchoolDto } from '../dto/create-school.dto';
import { UpdateSchoolDto } from '../dto/update-school.dto';

describe('SchoolController - Independent Teacher Features', () => {
  let controller: SchoolController;
  let schoolService: jest.Mocked<SchoolService>;
  let rbacService: jest.Mocked<RbacService>;

  const mockSchoolService = {
    create: jest.fn(),
    findOne: jest.fn(),
    updateOwnSchool: jest.fn(),
    remove: jest.fn(),
  };

  const mockDocumentsService = {
    storeExaminationFormat: jest.fn(),
  };

  const mockMultimodalService = {
    extractFullTextFromPdf: jest.fn(),
  };

  const mockNarrativeStructureService = {
    findBySchoolId: jest.fn(),
  };

  const mockRbacService = {
    isIndependentTeacher: jest.fn(),
    canAccessSchool: jest.fn(),
    isSchoolManager: jest.fn(),
    validateSchoolManagerAccess: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
  };

  const mockAuthGuard = {
    canActivate: jest.fn().mockReturnValue(true),
  };

  const mockRoleGuard = {
    canActivate: jest.fn().mockReturnValue(true),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SchoolController],
      providers: [
        { provide: SchoolService, useValue: mockSchoolService },
        { provide: DocumentsService, useValue: mockDocumentsService },
        { provide: MultimodalService, useValue: mockMultimodalService },
        { provide: NarrativeStructureService, useValue: mockNarrativeStructureService },
        { provide: RbacService, useValue: mockRbacService },
        { provide: JwtService, useValue: mockJwtService },
        { provide: AuthGuard, useValue: mockAuthGuard },
        { provide: RoleGuard, useValue: mockRoleGuard },
        Reflector,
      ],
    }).compile();

    controller = module.get<SchoolController>(SchoolController);
    schoolService = module.get(SchoolService);
    rbacService = module.get(RbacService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const createSchoolDto: CreateSchoolDto = {
      name: 'Independent Teacher School',
      address: '123 Education St',
      phoneNumber: '******-123-4567',
      registeredNumber: 'REG12345',
      email: '<EMAIL>',
    };

    it('should allow INDEPENDENT_TEACHER to create school', async () => {
      const req = {
        user: {
          sub: 'teacher-123',
          role: EUserRole.INDEPENDENT_TEACHER,
          email: '<EMAIL>',
        },
      };

      const mockSchool = {
        id: 'school-456',
        ...createSchoolDto,
        adminId: 'teacher-123',
      };

      schoolService.create.mockResolvedValue(mockSchool as any);

      const result = await controller.create(createSchoolDto, req);

      expect(schoolService.create).toHaveBeenCalledWith(
        createSchoolDto,
        'teacher-123',
        EUserRole.INDEPENDENT_TEACHER
      );
      expect(result).toEqual(mockSchool);
    });

    it('should allow ADMIN to create school', async () => {
      const req = {
        user: {
          sub: 'admin-123',
          role: EUserRole.ADMIN,
          email: '<EMAIL>',
        },
      };

      const mockSchool = {
        id: 'school-456',
        ...createSchoolDto,
      };

      schoolService.create.mockResolvedValue(mockSchool as any);

      const result = await controller.create(createSchoolDto, req);

      expect(schoolService.create).toHaveBeenCalledWith(
        createSchoolDto,
        'admin-123',
        EUserRole.ADMIN
      );
      expect(result).toEqual(mockSchool);
    });
  });

  describe('updateOwnSchool', () => {
    const updateSchoolDto: UpdateSchoolDto = {
      name: 'Updated School Name',
      address: 'New Address',
    };

    it('should allow INDEPENDENT_TEACHER to update their own school', async () => {
      const req = {
        user: {
          sub: 'teacher-123',
          role: EUserRole.INDEPENDENT_TEACHER,
          email: '<EMAIL>',
        },
      };

      const mockUpdatedSchool = {
        id: 'school-456',
        ...updateSchoolDto,
        adminId: 'teacher-123',
      };

      schoolService.updateOwnSchool.mockResolvedValue(mockUpdatedSchool as any);

      const result = await controller.updateOwnSchool(updateSchoolDto, req);

      expect(schoolService.updateOwnSchool).toHaveBeenCalledWith('teacher-123', updateSchoolDto);
      expect(result).toEqual(mockUpdatedSchool);
    });

    it('should throw NotFoundException if teacher has no school', async () => {
      const req = {
        user: {
          sub: 'teacher-123',
          role: EUserRole.INDEPENDENT_TEACHER,
          email: '<EMAIL>',
        },
      };

      schoolService.updateOwnSchool.mockRejectedValue(
        new NotFoundException('School not found or you are not the administrator of any school.')
      );

      await expect(controller.updateOwnSchool(updateSchoolDto, req)).rejects.toThrow(
        NotFoundException
      );

      expect(schoolService.updateOwnSchool).toHaveBeenCalledWith('teacher-123', updateSchoolDto);
    });
  });

  describe('remove', () => {
    const schoolId = 'school-123';

    it('should prevent INDEPENDENT_TEACHER from deleting schools', async () => {
      const req = {
        user: {
          sub: 'teacher-123',
          role: EUserRole.INDEPENDENT_TEACHER,
          email: '<EMAIL>',
        },
      };

      schoolService.remove.mockRejectedValue(
        new ForbiddenException('Independent teachers cannot delete schools.')
      );

      await expect(controller.remove(schoolId, req)).rejects.toThrow(ForbiddenException);

      expect(schoolService.remove).toHaveBeenCalledWith(schoolId, req.user);
    });

    it('should allow ADMIN to delete schools', async () => {
      const req = {
        user: {
          sub: 'admin-123',
          role: EUserRole.ADMIN,
          email: '<EMAIL>',
        },
      };

      schoolService.remove.mockResolvedValue();

      await controller.remove(schoolId, req);

      expect(schoolService.remove).toHaveBeenCalledWith(schoolId, req.user);
    });
  });

  describe('uploadExaminationFormat', () => {
    const uploadDto = { schoolId: 'school-123' };
    const mockFile = {
      buffer: Buffer.from('mock pdf content'),
      mimetype: 'application/pdf',
    } as Express.Multer.File;

    it('should allow INDEPENDENT_TEACHER to upload for their own school', async () => {
      const req = {
        user: {
          sub: 'teacher-123',
          role: EUserRole.INDEPENDENT_TEACHER,
          email: '<EMAIL>',
          schoolId: 'school-123',
        },
      };

      rbacService.isSchoolManager.mockReturnValue(false);
      rbacService.isIndependentTeacher.mockReturnValue(true);
      rbacService.canAccessSchool.mockReturnValue(true);
      schoolService.findOne.mockResolvedValue({} as any);
      mockMultimodalService.extractFullTextFromPdf.mockResolvedValue('extracted text');
      mockDocumentsService.storeExaminationFormat.mockResolvedValue('doc-123');

      const result = await controller.uploadExaminationFormat(mockFile, uploadDto, req);

      expect(rbacService.canAccessSchool).toHaveBeenCalledWith(req.user, 'school-123');
      expect(result.success).toBe(true);
      expect(result.documentId).toBe('doc-123');
    });

    it('should prevent INDEPENDENT_TEACHER from uploading for other schools', async () => {
      const req = {
        user: {
          sub: 'teacher-123',
          role: EUserRole.INDEPENDENT_TEACHER,
          email: '<EMAIL>',
          schoolId: 'school-123',
        },
      };

      const otherSchoolDto = { schoolId: 'other-school-456' };

      rbacService.isSchoolManager.mockReturnValue(false);
      rbacService.isIndependentTeacher.mockReturnValue(true);
      rbacService.canAccessSchool.mockReturnValue(false);

      await expect(
        controller.uploadExaminationFormat(mockFile, otherSchoolDto, req)
      ).rejects.toThrow(BadRequestException);

      expect(rbacService.canAccessSchool).toHaveBeenCalledWith(req.user, 'other-school-456');
    });

    it('should allow SCHOOL_MANAGER to upload for their school', async () => {
      const req = {
        user: {
          sub: 'manager-123',
          role: EUserRole.SCHOOL_MANAGER,
          email: '<EMAIL>',
          schoolId: 'school-123',
        },
      };

      rbacService.isSchoolManager.mockReturnValue(true);
      rbacService.validateSchoolManagerAccess.mockImplementation(() => {});
      schoolService.findOne.mockResolvedValue({} as any);
      mockMultimodalService.extractFullTextFromPdf.mockResolvedValue('extracted text');
      mockDocumentsService.storeExaminationFormat.mockResolvedValue('doc-123');

      const result = await controller.uploadExaminationFormat(mockFile, uploadDto, req);

      expect(rbacService.validateSchoolManagerAccess).toHaveBeenCalledWith(req.user, 'school-123');
      expect(result.success).toBe(true);
      expect(result.documentId).toBe('doc-123');
    });
  });
});
