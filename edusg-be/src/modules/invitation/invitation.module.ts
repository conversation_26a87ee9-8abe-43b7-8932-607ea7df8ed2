import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Invitation } from './entities/invitation.entity';
import { InvitationController } from './invitation.controller';
import { InvitationService } from './invitation.service';
import { AuthModule } from '../auth/auth.module';
import { School } from '../school/entities/school.entity';
import { User } from '../user/entities/user.entity';
import { Exam } from '../exam/entities/exam.entity';
import { ExamAssignment } from '../exam/entities/exam-assignment.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Invitation, School, User, Exam, ExamAssignment]),
    forwardRef(() => AuthModule),
  ],
  controllers: [InvitationController],
  providers: [InvitationService],
  exports: [InvitationService],
})
export class InvitationModule {}
