import { Controller, Post, Get, Body, Query, UseGuards, BadRequestException, NotFoundException, ForbiddenException, Param } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { InvitationService } from './invitation.service';
import { SendInvitationDto } from './dto/send-invitation.dto';
import { SendInvitationWithExamDto } from './dto/send-invitation-with-exam.dto';
import { ValidateInvitationDto } from './dto/validate-invitation.dto';
import { ActiveUser } from '../auth/decorators/active-user.decorator';
import { Roles } from '../auth/decorators/role.decorator';
import { Public } from '../auth/decorators/public.decorator';
import { AuthGuard } from '../auth/guards/auth.guard';
import { RoleGuard } from '../auth/guards/role.guard';
import { EUserRole } from '../user/dto/create-user.dto';
import { Invitation } from './entities/invitation.entity';

@ApiTags('Invitations')
@Controller('invitations')
@UseGuards(AuthGuard, RoleGuard)
export class InvitationController {
  constructor(private readonly invitationService: InvitationService) {}

  @Post('send')
  @Roles(
    EUserRole.ADMIN,
    EUserRole.SCHOOL_MANAGER,
    EUserRole.TEACHER,
    EUserRole.INDEPENDENT_TEACHER,
  )
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Send an invitation to join a school' })
  @ApiResponse({ 
    status: 201, 
    description: 'Invitation sent successfully',
    type: Invitation,
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid email or invitation already exists' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions' })
  async sendInvitation(
    @ActiveUser() user: any,
    @Body() dto: SendInvitationDto,
  ): Promise<Invitation> {
    const invitedBy = user.sub;

    const isTeacher = user.role === EUserRole.TEACHER ;

    if (isTeacher) {
      if (dto.role !== EUserRole.STUDENT) {
        throw new BadRequestException('You can only invite students');
      }

      if (user.schoolId && user.schoolId !== dto.schoolId) {
        throw new BadRequestException('You can only send invitations for your own school');
      }
    }
    
    // For school managers, ensure they can only send invitations for their own school
    if (user.role === EUserRole.SCHOOL_MANAGER && user.schoolId && user.schoolId !== dto.schoolId) {
      throw new BadRequestException('You can only send invitations for your own school');
    }

    return this.invitationService.createInvitation(
      dto.email,
      dto.schoolId,
      dto.role,
      invitedBy,
    );
  }

  @Get('validate')
  @Public()
  @ApiOperation({ summary: 'Validate an invitation token' })
  @ApiResponse({ 
    status: 200, 
    description: 'Invitation is valid',
    type: Invitation,
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Invalid or expired invitation token',
  })
  async validateInvitation(
    @Query() query: ValidateInvitationDto,
  ): Promise<Invitation> {
    const invitation = await this.invitationService.validateToken(query.token);
    
    if (!invitation) {
      throw new NotFoundException('Invalid or expired invitation token');
    }

    return invitation;
  }

  @Post('assign-exam')
  @Roles(
    EUserRole.ADMIN,
    EUserRole.SCHOOL_MANAGER,
    EUserRole.TEACHER,
    EUserRole.INDEPENDENT_TEACHER,
  )
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Invite a student and assign exams in a single operation' })
  @ApiResponse({ 
    status: 201, 
    description: 'Student invited and exams assigned successfully',
    schema: {
      type: 'object',
      properties: {
        invitation: { $ref: '#/components/schemas/Invitation' },
        assignments: {
          type: 'array',
          items: { $ref: '#/components/schemas/ExamAssignment' }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid email, exams, or invitation already exists' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or cannot assign specified exams' })
  @ApiResponse({ status: 404, description: 'One or more exams not found' })
  async inviteStudentWithExamAssignment(
    @ActiveUser() user: any,
    @Body() dto: SendInvitationWithExamDto,
  ) {
    const invitedBy = user.sub;

    // Enforce that only students can be invited through this endpoint
    if (dto.role && dto.role !== EUserRole.STUDENT) {
      throw new BadRequestException('This endpoint can only be used to invite students');
    }

    // Set role to STUDENT if not provided
    dto.role = EUserRole.STUDENT;

    // Permission checks for school managers and teachers
    const isTeacher = user.role === EUserRole.TEACHER || user.role === EUserRole.INDEPENDENT_TEACHER;
    const isSchoolManager = user.role === EUserRole.SCHOOL_MANAGER;

    if (isTeacher || isSchoolManager) {
      if (user.schoolId && user.schoolId !== dto.schoolId) {
        throw new BadRequestException('You can only send invitations for your own school');
      }
    }

    return this.invitationService.createInvitationWithExamAssignments(
      dto.email,
      dto.schoolId,
      dto.role,
      dto.examIds,
      invitedBy,
      user,
    );
  }

  @Get('school/:schoolId')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all invitations for a school' })
  @ApiResponse({ 
    status: 200, 
    description: 'Invitations retrieved successfully',
    type: [Invitation],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions' })
  async getSchoolInvitations(
    @ActiveUser() user: any,
    @Param('schoolId') schoolId: string,
  ): Promise<Invitation[]> {
    const canViewAnySchool = [EUserRole.ADMIN].includes(user.role);

    // For school managers, ensure they can only view invitations for their own school
    if (!canViewAnySchool && user.schoolId && user.schoolId !== schoolId) {
      throw new ForbiddenException('You can only view invitations for your own school');
    }

    return this.invitationService.getInvitationsBySchool(schoolId);
  }
}
