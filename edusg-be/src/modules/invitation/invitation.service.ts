import { Injectable, Logger, BadRequestException, InternalServerErrorException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In } from 'typeorm';
import { Invitation, InvitationStatus } from './entities/invitation.entity';
import { MailService } from 'src/mail/mail.service';
import { IsEmail } from 'class-validator';
import { validate } from 'class-validator';
import * as crypto from 'crypto';
import { School } from '../school/entities/school.entity';
import { User } from '../user/entities/user.entity';
import { UserStatus, EUserRole } from '../user/dto/create-user.dto';
import { Exam } from '../exam/entities/exam.entity';
import { ExamAssignment, AssignmentStatus } from '../exam/entities/exam-assignment.entity';

@Injectable()
export class InvitationService {
  private readonly logger = new Logger(InvitationService.name);

  constructor(
    @InjectRepository(Invitation)
    private readonly invitationRepository: Repository<Invitation>,
    @InjectRepository(School)
    private readonly schoolRepository: Repository<School>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Exam)
    private readonly examRepository: Repository<Exam>,
    @InjectRepository(ExamAssignment)
    private readonly examAssignmentRepository: Repository<ExamAssignment>,
    private readonly mailService: MailService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Create a new invitation
   * @param email - Email address of the invitee
   * @param schoolId - ID of the school
   * @param role - Role to be assigned (optional, defaults to 'TEACHER')
   * @param invitedBy - ID of the user who sent the invitation (optional)
   * @returns Created invitation
   */
  async createInvitation(
    email: string,
    schoolId: string,
    role: string = 'TEACHER',
    invitedBy?: string
  ): Promise<Invitation> {
    try {
      this.logger.log(`Creating invitation for email: ${email} and school: ${schoolId}`);

      // Validate email format
      await this.validateEmail(email);

      // Check if an ACTIVE user already exists with this email
      const existingActiveUser = await this.userRepository.findOne({
        where: {
          email,
          status: UserStatus.ACTIVE
        }
      });

      if (existingActiveUser) {
        throw new BadRequestException('A user with this email already exists and is active');
      }

      // Check if invitation already exists for this email and school
      const existingInvitation = await this.invitationRepository.findOne({
        where: {
          email,
          schoolId,
          status: InvitationStatus.PENDING
        }
      });

      if (existingInvitation) {
        throw new BadRequestException('Invitation already exists for this email and school');
      }

      // Check if an INVITED user already exists with this email
      let invitedUser = await this.userRepository.findOne({
        where: {
          email,
          status: UserStatus.INVITED
        }
      });

      // If no INVITED user exists, create one
      if (!invitedUser) {
        invitedUser = this.userRepository.create({
          email,
          schoolId,
          role: role as EUserRole,
          status: UserStatus.INVITED,
        });
        invitedUser = await this.userRepository.save(invitedUser);
        this.logger.log(`Created INVITED user for email: ${email}`);
      }

      // Generate secure token
      const token = this.generateSecureToken();

      // Set expiration date (24 hours from now)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);

      // Create invitation entity linked to the user
      const invitation = this.invitationRepository.create({
        email,
        schoolId,
        role,
        token,
        expiresAt,
        invitedBy,
        userId: invitedUser.id,
        status: InvitationStatus.PENDING,
      });

      // Save invitation to database
      const savedInvitation = await this.invitationRepository.save(invitation);

      // Fetch school and inviter information for better email
      let schoolName: string | undefined;
      let inviterName: string | undefined;
      
      try {
        const school = await this.schoolRepository.findOne({ where: { id: schoolId } });
        schoolName = school?.name;
        
        if (invitedBy) {
          const inviter = await this.userRepository.findOne({ where: { id: invitedBy } });
          inviterName = inviter?.name || undefined;
        }
      } catch (error) {
        // Log error but don't fail the invitation creation
        this.logger.warn('Failed to fetch school or inviter information for email', error);
      }

      // Send invitation email
      await this.sendInvitationEmail(email, token, role, schoolName, inviterName);

      this.logger.log(`Invitation created successfully for email: ${email}`);
      return savedInvitation;

    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Failed to create invitation for email: ${email}`, error.stack);
      throw new InternalServerErrorException('Failed to create invitation. Please try again later.');
    }
  }

  /**
   * Create a new invitation with exam assignments in a single transaction
   * @param email - Email address of the invitee
   * @param schoolId - ID of the school
   * @param role - Role to be assigned (defaults to 'STUDENT')
   * @param examIds - Array of exam IDs to assign
   * @param invitedBy - ID of the user who sent the invitation
   * @param user - The authenticated user object for permission checks
   * @returns Object containing the created invitation and exam assignments
   */
  async createInvitationWithExamAssignments(
    email: string,
    schoolId: string,
    role: string = 'STUDENT',
    examIds: string[],
    invitedBy: string,
    user: any,
  ): Promise<{ invitation: Invitation; assignments: ExamAssignment[] }> {
    // Start a database transaction for atomicity
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(`Creating invitation with exam assignments for email: ${email} and school: ${schoolId}`);

      // Validate email format
      await this.validateEmail(email);

      // Check if an ACTIVE user already exists with this email
      const existingActiveUser = await queryRunner.manager.findOne(User, {
        where: {
          email,
          status: UserStatus.ACTIVE
        }
      });

      if (existingActiveUser) {
        throw new BadRequestException('A user with this email already exists and is active');
      }

      // Check if invitation already exists for this email and school
      const existingInvitation = await queryRunner.manager.findOne(Invitation, {
        where: {
          email,
          schoolId,
          status: InvitationStatus.PENDING
        }
      });

      if (existingInvitation) {
        throw new BadRequestException('Invitation already exists for this email and school');
      }

      // Validate that all exams exist
      const exams = await queryRunner.manager.find(Exam, {
        where: { id: In(examIds) },
        relations: ['user']
      });

      if (exams.length !== examIds.length) {
        const foundExamIds = exams.map(exam => exam.id);
        const missingExamIds = examIds.filter(id => !foundExamIds.includes(id));
        throw new NotFoundException(`Exams not found: ${missingExamIds.join(', ')}`);
      }

      // Validate permissions for each exam
      await this.validateExamPermissions(exams, user);

      // Check if an INVITED user already exists with this email
      let invitedUser = await queryRunner.manager.findOne(User, {
        where: {
          email,
          status: UserStatus.INVITED
        }
      });

      // If no INVITED user exists, create one
      if (!invitedUser) {
        invitedUser = queryRunner.manager.create(User, {
          email,
          schoolId,
          role: role as EUserRole,
          status: UserStatus.INVITED,
        });
        invitedUser = await queryRunner.manager.save(User, invitedUser);
        this.logger.log(`Created INVITED user for email: ${email}`);
      }

      // Generate secure token
      const token = this.generateSecureToken();

      // Set expiration date (24 hours from now)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);

      // Create invitation entity linked to the user
      const invitation = queryRunner.manager.create(Invitation, {
        email,
        schoolId,
        role,
        token,
        expiresAt,
        invitedBy,
        userId: invitedUser.id,
        status: InvitationStatus.PENDING,
      });

      // Save invitation to database
      const savedInvitation = await queryRunner.manager.save(Invitation, invitation);

      // Create exam assignments
      const assignments: ExamAssignment[] = [];
      for (const examId of examIds) {
        const assignment = queryRunner.manager.create(ExamAssignment, {
          examId,
          studentId: invitedUser.id,
          status: AssignmentStatus.ASSIGNED,
          score: 0,
        });
        assignments.push(assignment);
      }

      // Bulk save assignments
      const savedAssignments = await queryRunner.manager.save(ExamAssignment, assignments);

      // Commit the transaction
      await queryRunner.commitTransaction();

      // Fetch school and inviter information for better email
      let schoolName: string | undefined;
      let inviterName: string | undefined;
      
      try {
        const school = await this.schoolRepository.findOne({ where: { id: schoolId } });
        schoolName = school?.name;
        
        if (invitedBy) {
          const inviter = await this.userRepository.findOne({ where: { id: invitedBy } });
          inviterName = inviter?.name || undefined;
        }
      } catch (error) {
        // Log error but don't fail the invitation creation
        this.logger.warn('Failed to fetch school or inviter information for email', error);
      }

      // Send invitation email (outside transaction)
      await this.sendInvitationEmail(email, token, role, schoolName, inviterName);

      this.logger.log(`Invitation with ${savedAssignments.length} exam assignments created successfully for email: ${email}`);

      return {
        invitation: savedInvitation,
        assignments: savedAssignments,
      };

    } catch (error) {
      // Rollback the transaction on error
      await queryRunner.rollbackTransaction();
      
      if (error instanceof BadRequestException || error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }
      
      this.logger.error(`Failed to create invitation with exam assignments for email: ${email}`, error.stack);
      throw new InternalServerErrorException('Failed to create invitation with exam assignments. Please try again later.');
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Validate permissions for exam assignment during invitation
   * @param exams - Array of exam entities to validate permissions for
   * @param user - The authenticated user object
   * @throws ForbiddenException if user doesn't have permission to assign any exam
   */
  private async validateExamPermissions(exams: Exam[], user: any): Promise<void> {
    for (const exam of exams) {
      // Teachers can only assign their own exams
      if ((user.role === EUserRole.TEACHER || user.role === EUserRole.INDEPENDENT_TEACHER) && exam.userId !== user.sub) {
        throw new ForbiddenException(`You can only assign your own exams. Exam ID: ${exam.id} is not owned by you.`);
      }

      // School managers can only assign exams from their school
      if (user.role === EUserRole.SCHOOL_MANAGER && user.schoolId && exam.schoolId !== user.schoolId) {
        throw new ForbiddenException(`You can only assign exams from your own school. Exam ID: ${exam.id} is not from your school.`);
      }

      // Independent teachers should only assign exams they created
      if (user.role === EUserRole.INDEPENDENT_TEACHER && exam.userId !== user.sub) {
        throw new ForbiddenException(`Independent teachers can only assign their own exams. Exam ID: ${exam.id} is not owned by you.`);
      }
    }
  }

  /**
   * Validate invitation token
   * @param token - Invitation token
   * @returns Invitation if valid, null otherwise
   */
  async validateToken(token: string): Promise<Invitation | null> {
    try {
      this.logger.log(`Validating invitation token: ${token.substring(0, 8)}...`);

      if (!token || token.trim() === '') {
        throw new BadRequestException('Token is required');
      }

      // Find invitation by token
      const invitation = await this.invitationRepository.findOne({
        where: { token: token.trim() },
        relations: ['school', 'user'],
      });

      if (!invitation) {
        this.logger.warn(`Invalid token provided: ${token.substring(0, 8)}...`);
        return null;
      }

      // Check if invitation has expired
      if (invitation.expiresAt < new Date()) {
        this.logger.warn(`Expired token used: ${token.substring(0, 8)}...`);
        
        // Update invitation status to expired
        await this.invitationRepository.update(invitation.id, {
          status: InvitationStatus.EXPIRED,
        });
        
        return null;
      }

      // Check if invitation is still pending
      if (invitation.status !== InvitationStatus.PENDING) {
        this.logger.warn(`Token already processed: ${token.substring(0, 8)}... Status: ${invitation.status}`);
        return null;
      }

      this.logger.log(`Token validation successful for email: ${invitation.email}`);
      return invitation;

    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Error validating token: ${token.substring(0, 8)}...`, error.stack);
      throw new InternalServerErrorException('Failed to validate invitation token. Please try again later.');
    }
  }

  /**
   * Mark invitation as accepted
   * @param token - Invitation token
   * @returns Updated invitation
   */
  async markInvitationAsAccepted(token: string): Promise<Invitation> {
    try {
      this.logger.log(`Marking invitation as accepted for token: ${token.substring(0, 8)}...`);

      // First validate the token
      const invitation = await this.validateToken(token);
      
      if (!invitation) {
        throw new BadRequestException('Invalid or expired invitation token');
      }

      // Update invitation status to accepted
      await this.invitationRepository.update(invitation.id, {
        status: InvitationStatus.ACCEPTED,
      });

      // Fetch updated invitation
      const updatedInvitation = await this.invitationRepository.findOne({
        where: { id: invitation.id },
        relations: ['school'],
      });

      this.logger.log(`Invitation marked as accepted for email: ${invitation.email}`);
      return updatedInvitation!;

    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Error marking invitation as accepted for token: ${token.substring(0, 8)}...`, error.stack);
      throw new InternalServerErrorException('Failed to accept invitation. Please try again later.');
    }
  }

  /**
   * Get invitation by token
   * @param token - Invitation token
   * @returns Invitation if found
   */
  async getInvitationByToken(token: string): Promise<Invitation | null> {
    try {
      return await this.invitationRepository.findOne({
        where: { token: token.trim() },
        relations: ['school'],
      });
    } catch (error) {
      this.logger.error(`Error fetching invitation by token: ${token.substring(0, 8)}...`, error.stack);
      return null;
    }
  }

  /**
   * Get all invitations for a school
   * @param schoolId - School ID
   * @returns Array of invitations
   */
  async getInvitationsBySchool(schoolId: string): Promise<Invitation[]> {
    try {
      return await this.invitationRepository.find({
        where: { schoolId },
        relations: ['school'],
        order: { createdAt: 'DESC' },
      });
    } catch (error) {
      this.logger.error(`Error fetching invitations for school: ${schoolId}`, error.stack);
      throw new InternalServerErrorException('Failed to fetch invitations. Please try again later.');
    }
  }

  /**
   * Clean up expired invitations
   * @returns Number of cleaned up invitations
   */
  async cleanupExpiredInvitations(): Promise<number> {
    try {
      const result = await this.invitationRepository
        .createQueryBuilder()
        .update()
        .set({ status: InvitationStatus.EXPIRED })
        .where('expiresAt < :now', { now: new Date() })
        .andWhere('status = :status', { status: InvitationStatus.PENDING })
        .execute();

      this.logger.log(`Cleaned up ${result.affected} expired invitations`);
      return result.affected || 0;
    } catch (error) {
      this.logger.error('Error cleaning up expired invitations', error.stack);
      throw new InternalServerErrorException('Failed to cleanup expired invitations');
    }
  }

  /**
   * Validate email format
   * @param email - Email to validate
   * @throws BadRequestException if email is invalid
   */
  private async validateEmail(email: string): Promise<void> {
    if (!email || email.trim() === '') {
      throw new BadRequestException('Email is required');
    }

    // Create a temporary object with IsEmail decorator for validation
    class EmailValidator {
      @IsEmail()
      email: string;
    }

    const emailValidator = new EmailValidator();
    emailValidator.email = email;

    const errors = await validate(emailValidator);
    
    if (errors.length > 0) {
      throw new BadRequestException('Invalid email format');
    }
  }

  /**
   * Generate secure token for invitation
   * @returns Secure token string
   */
  private generateSecureToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Send invitation email
   * @param email - Recipient email
   * @param token - Invitation token
   * @param role - Role to be assigned
   * @param schoolName - Name of the school (optional)
   * @param inviterName - Name of the person who sent the invitation (optional)
   */
  private async sendInvitationEmail(
    email: string, 
    token: string, 
    role: string, 
    schoolName?: string, 
    inviterName?: string
  ): Promise<void> {
    try {
      // Create invitation URL (adjust URL based on your frontend)
      const invitationUrl = `${process.env.FRONTEND_URL || 'https://app.edusg.co'}/invitation?token=${token}`;

      await this.mailService.sendInvitationEmail(
        email,
        invitationUrl,
        role,
        schoolName,
        inviterName
      );

      this.logger.log(`Invitation email sent successfully to: ${email}`);
    } catch (error) {
      this.logger.error(`Failed to send invitation email to: ${email}`, error.stack);
      throw new InternalServerErrorException('Failed to send invitation email');
    }
  }
}
