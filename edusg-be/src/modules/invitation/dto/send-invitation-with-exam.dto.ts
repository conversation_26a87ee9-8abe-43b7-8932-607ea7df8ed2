import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsNotEmpty, IsUUID, IsArray, ArrayMinSize, ArrayUnique, IsOptional } from 'class-validator';
import { EUserRole } from 'src/modules/user/dto/create-user.dto';

export class SendInvitationWithExamDto {
  @ApiProperty({
    description: 'Email address of the student to invite',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Role to be assigned to the invitee (should be STUDENT for this endpoint)',
    enum: EUserRole,
    example: EUserRole.STUDENT,
    default: EUserRole.STUDENT,
  })
  @IsEnum(EUserRole)
  @IsOptional()
  role?: EUserRole = EUserRole.STUDENT;

  @ApiProperty({
    description: 'ID of the school for the invitation',
    example: 'uuid-string',
  })
  @IsUUID()
  @IsNotEmpty()
  schoolId: string;

  @ApiProperty({
    description: 'Array of exam IDs to assign to the invited student',
    example: ['uuid-1', 'uuid-2', 'uuid-3'],
    type: [String],
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsUUID('4', { each: true })
  @IsNotEmpty()
  examIds: string[];
} 