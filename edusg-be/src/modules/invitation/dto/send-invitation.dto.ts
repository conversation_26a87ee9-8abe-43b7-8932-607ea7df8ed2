import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsNotEmpty, IsString, IsUUID } from 'class-validator';
import { EUserRole } from 'src/modules/user/dto/create-user.dto';

export class SendInvitationDto {
  @ApiProperty({
    description: 'Email address of the invitee',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Role to be assigned to the invitee',
    enum: EUserRole,
    example: EUserRole.TEACHER,
  })
  @IsEnum(EUserRole)
  @IsNotEmpty()
  role: EUserRole;

  @ApiProperty({
    description: 'ID of the school for the invitation',
    example: 'uuid-string',
  })
  @IsUUID()
  @IsNotEmpty()
  schoolId: string;
}
