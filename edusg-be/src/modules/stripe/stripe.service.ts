// stripe/stripe.service.ts
import { Injectable, InternalServerErrorException } from '@nestjs/common';
import Stripe from 'stripe';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class StripeService {
  private readonly stripe: Stripe;

  constructor(private configService: ConfigService) {
    const stripeKey = this.configService.get<string>('STRIPE_SECRET_KEY');
    if (!stripeKey) {
      throw new InternalServerErrorException(
        'STRIPE_SECRET_KEY is not configured',
      );
    }
    this.stripe = new Stripe(stripeKey, {
      apiVersion: '2025-05-28.basil',
    });
  }

  public getStripeInstance(): Stripe {
    return this.stripe;
  }

  async getPrices() {
    return this.stripe.prices.list({
      active: true,
    });
  }

  async createProduct(productData: {
    name: string;
    description?: string;
    images?: string[];
  }): Promise<Stripe.Product> {
    try {
      return await this.stripe.products.create({
        name: productData.name,
        description: productData.description,
        images: productData.images,
        active: true,
      });
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to create Stripe product: ${error.message}`,
      );
    }
  }

  async updateProduct(
    productId: string,
    productData: {
      name?: string;
      description?: string;
      images?: string[];
      active?: boolean;
    },
  ): Promise<Stripe.Product> {
    try {
      return await this.stripe.products.update(productId, productData);
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to update Stripe product: ${error.message}`,
      );
    }
  }

  async createCheckoutSession({
    priceId,
    userId,
    packageId,
    successUrl,
    cancelUrl,
  }: {
    priceId: string;
    userId: string;
    packageId: string;
    successUrl: string;
    cancelUrl: string;
  }) {
    try {
      const session = await this.stripe.checkout.sessions.create({
        mode: 'subscription',
        payment_method_types: ['card'],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        success_url: successUrl,
        cancel_url: cancelUrl,
        subscription_data: {
          metadata: {
            userId,
            internalPackageId: packageId,
          },
        },
      });

      return session.url;
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to create checkout session: ${error.message}`,
      );
    }
  }

  constructWebhookEvent(payload: Buffer, signature: string): Stripe.Event {
    const webhookSecret = this.configService.get<string>(
      'STRIPE_WEBHOOK_SECRET',
    );
    if (!webhookSecret) {
      throw new InternalServerErrorException(
        'STRIPE_WEBHOOK_SECRET is not configured',
      );
    }

    try {
      return this.stripe.webhooks.constructEvent(
        payload,
        signature,
        webhookSecret,
      );
    } catch (err) {
      throw new InternalServerErrorException(
        `Webhook signature error: ${err.message}`,
      );
    }
  }
}
