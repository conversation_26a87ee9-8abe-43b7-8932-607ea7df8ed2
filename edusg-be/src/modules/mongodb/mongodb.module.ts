import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';
import {
  WorksheetDocument,
  WorksheetDocumentSchema,
} from './schemas/worksheet-document.schema';
import {
  WorksheetPromptResult,
  WorksheetPromptResultSchema,
} from './schemas/worksheet-prompt-result.schema';
import {
  QueryCache,
  QueryCacheSchema,
} from './schemas/query-cache.schema';
import {
  EmbeddingCache,
  EmbeddingCacheSchema,
} from './schemas/embedding-cache.schema';
import {
  QuestionPool,
  QuestionPoolSchema,
} from './schemas/question-pool.schema';
import {
  WorksheetQuestionDocument,
  WorksheetQuestionDocumentSchema,
} from './schemas/worksheet-question-document.schema';

@Module({
  imports: [
    MongooseModule.forRootAsync({
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGODB_URI'),
        useNewUrlParser: true,
        useUnifiedTopology: true,
        // Connection pool configuration for optimal performance
        maxPoolSize: 20, // Maximum number of connections in the pool
        minPoolSize: 5,  // Minimum number of connections in the pool
        maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
        waitQueueTimeoutMS: 5000, // Maximum time to wait for a connection
        serverSelectionTimeoutMS: 5000, // How long to try selecting a server
        socketTimeoutMS: 45000, // How long a send or receive on a socket can take
        bufferCommands: false, // Disable mongoose buffering
      }),
      inject: [ConfigService],
    }),
    MongooseModule.forFeature([
      { name: WorksheetDocument.name, schema: WorksheetDocumentSchema },
      { name: WorksheetPromptResult.name, schema: WorksheetPromptResultSchema },
      { name: QueryCache.name, schema: QueryCacheSchema },
      { name: EmbeddingCache.name, schema: EmbeddingCacheSchema },
      { name: QuestionPool.name, schema: QuestionPoolSchema },
      { name: WorksheetQuestionDocument.name, schema: WorksheetQuestionDocumentSchema },
    ]),
  ],
  exports: [MongooseModule],
})
export class MongodbModule {}
