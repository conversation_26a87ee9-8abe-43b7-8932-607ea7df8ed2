import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { 
  IExerciseQuestion, 
  EQuestionType, 
  EQuestionDifficulty, 
  EQuestionStatus,
  IQuestionMedia,
  IQuestionMetadata,
  IQuestionAudit
} from '../../../shared/interfaces/exercise-question.interface';

/**
 * Enum for worksheet question status (specific to worksheet context)
 */
export enum EWorksheetQuestionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  LOCKED = 'locked', // Locked for editing by another user
  PENDING_REVIEW = 'pending_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

/**
 * Interface for user response to a question (for exam/assessment context)
 */
export interface IUserResponse {
  userId: string;
  response: string | string[];
  submittedAt: Date;
  isCorrect?: boolean;
  score?: number;
  timeSpentSeconds?: number;
  attempts?: number;
}

/**
 * Interface for question performance analytics
 */
export interface IQuestionAnalytics {
  totalAttempts: number;
  correctAttempts: number;
  averageScore: number;
  averageTimeSeconds: number;
  difficultyRating: number; // Based on user performance
  lastAnalyzedAt: Date;
}

/**
 * Schema for question media (embedded)
 */
@Schema({ _id: false })
export class QuestionMediaSchema {
  @Prop()
  imageUrl?: string;

  @Prop()
  imagePrompt?: string;

  @Prop()
  audioUrl?: string;

  @Prop()
  videoUrl?: string;

  @Prop([String])
  attachmentUrls?: string[];
}

/**
 * Schema for question metadata (embedded)
 */
@Schema({ _id: false })
export class QuestionMetadataSchema {
  @Prop([String])
  tags?: string[];

  @Prop([String])
  keywords?: string[];

  @Prop()
  estimatedTimeMinutes?: number;

  @Prop()
  cognitiveLevel?: string;

  @Prop([String])
  learningObjectives?: string[];

  @Prop([String])
  prerequisites?: string[];

  @Prop([String])
  hints?: string[];

  @Prop([String])
  references?: string[];

  @Prop()
  authorNotes?: string;

  @Prop()
  reviewNotes?: string;

  @Prop()
  lastReviewDate?: Date;

  @Prop()
  nextReviewDate?: Date;
}

/**
 * Schema for audit log entries (embedded)
 */
@Schema({ _id: false })
export class AuditLogEntrySchema {
  @Prop({ required: true })
  timestamp: Date;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  action: string;

  @Prop({ type: Object })
  changes: Record<string, any>;

  @Prop()
  reason?: string;
}

/**
 * Schema for question audit information (embedded)
 */
@Schema({ _id: false })
export class QuestionAuditSchema {
  @Prop({ required: true, default: Date.now })
  createdAt: Date;

  @Prop({ required: true, default: Date.now })
  updatedAt: Date;

  @Prop({ required: true })
  createdBy: string;

  @Prop()
  updatedBy?: string;

  @Prop({ required: true, default: 1 })
  version: number;

  @Prop([AuditLogEntrySchema])
  changeLog?: AuditLogEntrySchema[];
}

/**
 * Schema for user responses (embedded)
 */
@Schema({ _id: false })
export class UserResponseSchema {
  @Prop({ required: true })
  userId: string;

  @Prop({ type: [String] })
  response: string | string[];

  @Prop({ required: true, default: Date.now })
  submittedAt: Date;

  @Prop()
  isCorrect?: boolean;

  @Prop()
  score?: number;

  @Prop()
  timeSpentSeconds?: number;

  @Prop({ default: 1 })
  attempts?: number;
}

/**
 * Schema for question analytics (embedded)
 */
@Schema({ _id: false })
export class QuestionAnalyticsSchema {
  @Prop({ default: 0 })
  totalAttempts: number;

  @Prop({ default: 0 })
  correctAttempts: number;

  @Prop({ default: 0 })
  averageScore: number;

  @Prop({ default: 0 })
  averageTimeSeconds: number;

  @Prop({ default: 0 })
  difficultyRating: number;

  @Prop({ default: Date.now })
  lastAnalyzedAt: Date;
}

/**
 * Main Worksheet Question Document Schema
 * Represents a question instance within a specific worksheet context
 */
@Schema({ 
  timestamps: true, 
  collection: 'worksheet_questions',
  // Enable optimistic concurrency control
  optimisticConcurrency: true
})
export class WorksheetQuestionDocument extends Document {
  // Worksheet association
  @Prop({ required: true, index: true })
  worksheetId: string;

  // Question identification
  @Prop({ required: true })
  questionId: string; // Unique identifier for the question

  // Core question data (embedded for performance)
  @Prop({ required: true, enum: Object.values(EQuestionType) })
  type: EQuestionType;

  @Prop({ required: true, maxlength: 2000 })
  content: string;

  @Prop({ type: [String], required: true })
  options: string[];

  @Prop({ type: [String], required: true })
  answer: string[];

  @Prop({ required: true, maxlength: 1000 })
  explain: string;

  // Subject and academic information
  @Prop({ index: true })
  subject?: string;

  @Prop()
  parentSubject?: string;

  @Prop({ index: true })
  childSubject?: string;

  @Prop({ index: true })
  topic?: string;

  @Prop()
  subtopic?: string;

  @Prop({ index: true })
  grade?: string;

  @Prop({ enum: Object.values(EQuestionDifficulty) })
  difficulty?: EQuestionDifficulty;

  // Media references
  @Prop({ type: QuestionMediaSchema })
  media?: QuestionMediaSchema;

  // Legacy fields for backward compatibility
  @Prop()
  imagePrompt?: string;

  @Prop()
  imageUrl?: string;

  @Prop()
  image?: string;

  // Worksheet-specific fields
  @Prop({ required: true, index: true })
  position: number; // Order within the worksheet

  @Prop({ default: 1, min: 0 })
  points: number; // Points allocated for this question

  @Prop({ 
    enum: Object.values(EWorksheetQuestionStatus), 
    default: EWorksheetQuestionStatus.ACTIVE,
    index: true 
  })
  status: EWorksheetQuestionStatus;

  // School association for data isolation
  @Prop({ index: true })
  schoolId?: string;

  // User responses (for exam/assessment context)
  @Prop([UserResponseSchema])
  userResponses?: UserResponseSchema[];

  // Question metadata
  @Prop({ type: QuestionMetadataSchema })
  metadata?: QuestionMetadataSchema;

  // Analytics data
  @Prop({ type: QuestionAnalyticsSchema })
  analytics?: QuestionAnalyticsSchema;

  // Audit information
  @Prop({ type: QuestionAuditSchema, required: true })
  audit: QuestionAuditSchema;

  // Schema versioning
  @Prop({ default: 1 })
  schemaVersion: number;

  // TTL for cache expiration (optional)
  @Prop({ 
    default: () => {
      // Default TTL: 30 days from now
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date;
    }
  })
  expiresAt?: Date;

  // Lock information for concurrent editing
  @Prop()
  lockedBy?: string; // User ID who locked the question

  @Prop()
  lockedAt?: Date;

  @Prop({ default: 300 }) // 5 minutes default lock duration
  lockDurationSeconds?: number;

  // Additional flexible fields
  @Prop({ type: Object })
  additionalData?: Record<string, any>;
}

export const WorksheetQuestionDocumentSchema = SchemaFactory.createForClass(WorksheetQuestionDocument);

// Compound indexes for optimal query performance
WorksheetQuestionDocumentSchema.index({ worksheetId: 1, position: 1 }, { unique: true });
WorksheetQuestionDocumentSchema.index({ worksheetId: 1, status: 1 });
WorksheetQuestionDocumentSchema.index({ schoolId: 1, subject: 1, grade: 1 });
WorksheetQuestionDocumentSchema.index({ type: 1, difficulty: 1 });
WorksheetQuestionDocumentSchema.index({ 'audit.createdAt': -1 });
WorksheetQuestionDocumentSchema.index({ 'audit.updatedAt': -1 });

// Additional optimized indexes for performance
WorksheetQuestionDocumentSchema.index({ schoolId: 1, worksheetId: 1, status: 1 });
WorksheetQuestionDocumentSchema.index({ status: 1, worksheetId: 1, position: 1 });
WorksheetQuestionDocumentSchema.index({ worksheetId: 1, 'audit.lastModifiedBy': 1, 'audit.lastModifiedAt': -1 });
WorksheetQuestionDocumentSchema.index({ childSubject: 1, grade: 1, status: 1 });
WorksheetQuestionDocumentSchema.index({ 'analytics.totalAttempts': -1, status: 1 });
WorksheetQuestionDocumentSchema.index({ 'analytics.correctAttempts': -1, 'analytics.totalAttempts': -1 });
WorksheetQuestionDocumentSchema.index({ 'userResponses.userId': 1, worksheetId: 1, status: 1 });
WorksheetQuestionDocumentSchema.index({ 'media.hasImage': 1, status: 1 });
WorksheetQuestionDocumentSchema.index({ 'media.hasVideo': 1, status: 1 });

// TTL index for automatic expiration (if expiresAt is set)
WorksheetQuestionDocumentSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0, sparse: true });

// Text index for content search
WorksheetQuestionDocumentSchema.index({ 
  content: 'text', 
  explain: 'text', 
  'metadata.tags': 'text',
  'metadata.keywords': 'text'
});

// Pre-save middleware to update audit information
WorksheetQuestionDocumentSchema.pre('save', function(next) {
  if (this.isNew) {
    this.audit.createdAt = new Date();
  }
  this.audit.updatedAt = new Date();
  next();
});

// Method to check if question is locked
WorksheetQuestionDocumentSchema.methods.isLocked = function(): boolean {
  if (!this.lockedBy || !this.lockedAt) {
    return false;
  }
  
  const lockExpiry = new Date(this.lockedAt.getTime() + (this.lockDurationSeconds * 1000));
  return new Date() < lockExpiry;
};

// Method to lock question
WorksheetQuestionDocumentSchema.methods.lock = function(userId: string, durationSeconds: number = 300): void {
  this.lockedBy = userId;
  this.lockedAt = new Date();
  this.lockDurationSeconds = durationSeconds;
};

// Method to unlock question
WorksheetQuestionDocumentSchema.methods.unlock = function(): void {
  this.lockedBy = undefined;
  this.lockedAt = undefined;
};

// Export the schema and document type
export type WorksheetQuestionDocumentType = WorksheetQuestionDocument & Document;
