import { Injectable, NotFoundException, BadRequestException, Logger, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Worksheet,
  WorksheetGeneratingStatus,
} from './entities/worksheet.entity';
import { Repository, FindOptionsWhere, FindManyOptions } from 'typeorm';
import { CreateWorksheetDto, WorksheetOptionDto } from './dto/create-worksheet.dto';
import { ListWorksheetDto } from './dto/list-worksheets.dto';
import { User } from '../user/entities/user.entity';
import { EUserRole } from '../user/dto/create-user.dto';
import { OptionType } from '../options/entities/option-type.entity';
import { OptionValue } from '../options/entities/option-value.entity';
import { WorksheetOption } from './entities/worksheet-option.entity';
import { WorksheetQueueService } from './worksheet-queue.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WorksheetPromptResult } from '../mongodb/schemas/worksheet-prompt-result.schema';
import { WorksheetDocumentCacheService } from './services/worksheet-document-cache.service';
import { WorksheetDocument } from '../mongodb/schemas/worksheet-document.schema';
import { UsageTrackingService } from '../usage-tracking/services/usage-tracking.service';
import { SelectionStrategy } from './dto/worksheet-generation-options.dto';

// Interface for extended worksheet with additional data
export interface WorksheetWithExtras extends Worksheet {
  promptResult?: any;
  documentResult?: any;
}

@Injectable()
export class WorksheetService {
  private readonly logger = new Logger(WorksheetService.name);

  constructor(
      @InjectRepository(Worksheet)
      private worksheetRepository: Repository<Worksheet>,
      @InjectRepository(OptionType)
      private optionTypeRepository: Repository<OptionType>,
      @InjectRepository(OptionValue)
      private optionValueRepository: Repository<OptionValue>,
      @InjectRepository(WorksheetOption)
      private worksheetOptionRepository: Repository<WorksheetOption>,
      private worksheetQueueService: WorksheetQueueService,
      @InjectModel(WorksheetPromptResult.name)
      private worksheetPromptResultModel: Model<WorksheetPromptResult>,
      private worksheetDocumentCacheService: WorksheetDocumentCacheService,
      @InjectModel(WorksheetDocument.name) // Added WorksheetDocument model
      private worksheetDocumentModel: Model<WorksheetDocument>, // Added WorksheetDocument model
      private usageTrackingService: UsageTrackingService,
  ) {}

  private generateWorksheetTitle(
      dto: CreateWorksheetDto,
  ): string {
    return `${dto.grade} ${dto.topic} ${dto.level} Worksheet - ${dto.question_count} Questions`;
  }

  private generateWorksheetDescription(
      dto: CreateWorksheetDto,
  ): string {

    return `A comprehensive ${dto.level?.toLowerCase()} level worksheet for ${dto.grade} ${dto.topic} students.
    This worksheet contains ${dto.question_count} questions in ${dto.language} language,
    covering various question types: ${dto.question_type}.
    Designed to enhance understanding and practice of key concepts in ${dto.topic}.`;
  }

  private convertNewFormatToOptions(dto: CreateWorksheetDto): WorksheetOptionDto[] {
    const options: WorksheetOptionDto[] = [];

    // Add direct properties
    if (dto.grade) {
      options.push({ key: 'grade', value: dto.grade, text: dto.grade });
    }

    if (dto.topic) {
      options.push({ key: 'topic', value: dto.topic, text: dto.topic });
    }

    if (dto.level) {
      options.push({ key: 'level', value: dto.level, text: dto.level });
    }

    if (dto.language) {
      options.push({ key: 'language', value: dto.language, text: dto.language });
    }

    if (dto.question_count) {
      options.push({
        key: 'question_count',
        value: dto.question_count,
        text: `${dto.question_count}`
      });
    }

    // Note: Subject items are now handled separately and stored in worksheet.subjectData
    // They are no longer converted to options

    // Process question types
    if (dto.question_type && dto.question_type.length > 0) {
      for (const questionType of dto.question_type) {
        // Map frontend question type labels to backend format
        const mappedLabel = this.mapQuestionTypeLabel(questionType.label);
        options.push({
          key: 'question_type',
          value: mappedLabel,
          text: questionType.label,
          count: questionType.count
        });
      }
    }

    return options;
  }

  /**
   * Maps frontend question type labels to backend format
   * @param label The frontend label (e.g., "Fill Blank", "Creative Writing")
   * @returns The backend format (e.g., "fill_blank", "creative_writing")
   */
  private mapQuestionTypeLabel(label: string): string {
    const mappings: Record<string, string> = {
      'Fill Blank': 'fill_blank',
      'Creative Writing': 'creative_writing',
      'Multiple Choices': 'multiple_choice',
      'Single Choice': 'single_choice',
      'Multiple Choice': 'multiple_choice',
      'Open Ended': 'open_ended',
      'Matching': 'matching'
    };

    return mappings[label] || label.toLowerCase().replace(/\s+/g, '_');
  }

  /**
   * Extracts subject data from the DTO to be stored directly in the worksheet
   * @param dto The worksheet creation DTO
   * @returns The subject data to be stored
   */
  private extractSubjectData(dto: CreateWorksheetDto): any {
    if (dto.subject && dto.subject.length > 0) {
      return dto.subject;
    }
    return null;
  }

  async create(dto: CreateWorksheetDto, user?: User): Promise<Worksheet> {
    // Extract schoolId from authenticated user
    const schoolId = user?.schoolId;

    // Log schoolId for debugging
    if (schoolId) {
      this.logger.log(`WorksheetService: Creating worksheet with schoolId: ${schoolId} from authenticated user`);
    } else {
      this.logger.warn(`WorksheetService: Creating worksheet without schoolId - user has no associated school. Narrative structure retrieval will be limited`);
    }

    // Convert new format to options if needed
    let options = dto.options || [];

    // If we have any of the new format fields, convert them to options
    if (dto.grade || dto.topic || dto.level || dto.language ||
        dto.question_count || dto.question_type) {
      options = this.convertNewFormatToOptions(dto);
    }

    const title = dto.title || this.generateWorksheetTitle(dto);
    const description =
        dto.description || this.generateWorksheetDescription(dto);

    // Extract subject data to be stored directly in the worksheet
    const subjectData = this.extractSubjectData(dto);

    const worksheet = this.worksheetRepository.create({
      title,
      description,
      subjectData,
      // Set schoolId from user or DTO
      ...(schoolId && { schoolId }),
    });
    const saved = await this.worksheetRepository.save(worksheet);

    if (options && options.length > 0) {
      for (const opt of options) {
        const type = await this.optionTypeRepository.findOne({
          where: { key: opt.key },
        });

        if (!type) {
          throw new NotFoundException(
              `Option type with key ${opt.key} not found`,
          );
        }

        if (opt.key === 'question_count' && dto.isCustomQuestionCount) {
          const count = Number(opt.value); // opt.value is dto.question_count (string type)
          if (isNaN(count) || !Number.isInteger(count) || count <= 0) {
            throw new BadRequestException('Custom question count must be a positive integer');
          }
          if (count > 100) {
            throw new BadRequestException('Custom question count cannot exceed 100');
          }

          const worksheetOption = this.worksheetOptionRepository.create({
            worksheet: saved,
            optionType: type, // This is the 'question_count' OptionType
            // optionValue is intentionally omitted for custom count
            text: opt.text,   // This is the string representation, e.g., "15"
            count: count,     // This is the numeric value, e.g., 15
          });
          await this.worksheetOptionRepository.save(worksheetOption);
        } else {
          // Existing logic for predefined options (including non-custom question_count)
          // and other option types like grade, topic, question_type.
          const where: FindOptionsWhere<OptionValue> = {
            label: opt.text,
            optionTypeId: type.id,
          };

          const value = await this.optionValueRepository.findOne({
            where,
          });

          if (!value) {
            // This error is hit if an OptionValue is not found for any opt.text/opt.key combination
            // that wasn't handled by the custom question count logic above.
            // This applies to:
            // - Predefined question_count (e.g., user selected "10", but "10" is not in OptionValues for question_count)
            // - Grade, Topic, Level, Language (e.g., "P7" for grade, but "P7" is not an OptionValue for grade)
            // - Question Types (e.g., "Multiple Choices" for question_type, but not an OptionValue for question_type)
            throw new NotFoundException(
                `Option value ${opt.text} not found for type ${opt.key}`,
            );
          }

          const worksheetOption = this.worksheetOptionRepository.create({
            worksheet: saved,
            optionType: type,
            optionValue: value,
            text: opt.text,
            count: opt.count, // This is relevant for question_type options that have counts.
                              // For other predefined options (grade, topic, predefined q_count), opt.count is undefined,
                              // and worksheetOption.count will also be undefined/null, which is consistent.
          });
          await this.worksheetOptionRepository.save(worksheetOption);
        }
      }
    }

    const topicOption = options.find((opt) => opt.key === 'topic');
    const gradeOption = options.find((opt) => opt.key === 'grade');

    if (!topicOption || !gradeOption) {
      throw new NotFoundException('Topic and grade options are required');
    }

    let questionCountForJob: number;
    if (dto.isCustomQuestionCount && dto.question_count) {
      questionCountForJob = parseInt(dto.question_count, 10);
      if (isNaN(questionCountForJob) || questionCountForJob <= 0) {
        throw new BadRequestException('Custom question count must be a positive number string.');
      }
    } else if (dto.question_type && dto.question_type.length > 0) {
      questionCountForJob = dto.question_type.reduce((sum, qt) => sum + qt.count, 0);
      if (questionCountForJob <= 0 && dto.question_count) { // if sum is 0, but question_count is provided
        const parsedFallback = parseInt(dto.question_count, 10);
        if (!isNaN(parsedFallback) && parsedFallback > 0) {
          questionCountForJob = parsedFallback;
        } else {
          throw new BadRequestException('Total question count from question types is zero, and no valid fallback question_count provided.');
        }
      } else if (questionCountForJob <= 0) {
        throw new BadRequestException('Total question count from question types must be positive.');
      }
    } else if (dto.question_count) {
      questionCountForJob = parseInt(dto.question_count, 10);
      if (isNaN(questionCountForJob) || questionCountForJob <= 0) {
        throw new BadRequestException('Question count must be a positive number string.');
      }
    } else {
      throw new BadRequestException('Cannot determine question count. Provide question_count, or question_type distribution.');
    }

    const isCustomQuestionCountForJob = dto.isCustomQuestionCount ?? false;

    await this.worksheetQueueService.addWorksheetGenerationJob(
        saved.id,
        topicOption.value,
        gradeOption.value,
        questionCountForJob,
        isCustomQuestionCountForJob,
    );

    // Track usage for worksheet generation
    this.logger.debug(`Starting usage tracking for worksheet creation. User object type: ${typeof user}, User keys: ${Object.keys(user || {}).join(', ')}, User: ${JSON.stringify(user || {})}, questionCountForJob: ${questionCountForJob}`);
    
    // Try to determine the correct user identifier
    const userId = (user as any)?.sub || (user as any)?.id;
    
    if (userId) {
      try {
        this.logger.debug(`Calling incrementUsage for maxWorksheets with userId: ${userId}`);
        // Increment worksheet count by 1
        await this.usageTrackingService.incrementUsage(userId, 'maxWorksheets', 1);
        
        this.logger.debug(`Calling incrementUsage for maxQuestionsPerWorksheet with userId: ${userId}, amount: ${questionCountForJob}`);
        // Increment questions count by the number of questions in this worksheet
        await this.usageTrackingService.incrementUsage(userId, 'maxQuestionsPerWorksheet', questionCountForJob);
        
        this.logger.debug(`Usage tracking updated for user ${userId}: +1 worksheet, +${questionCountForJob} questions`);
      } catch (error) {
        // Log error but don't block worksheet creation
        this.logger.error(`Failed to update usage tracking for user ${userId}: ${error.message}`, error.stack);
      }
    } else {
      this.logger.warn(`No user ID found for worksheet creation - skipping usage tracking. User object: ${JSON.stringify(user)}`);
    }

    const result = await this.worksheetRepository.findOne({
      where: { id: saved.id },
      relations: [
        'selectedOptions',
        'selectedOptions.optionType',
        'selectedOptions.optionValue',
      ],
    });

    if (!result) {
      throw new NotFoundException('Worksheet not found after creation');
    }

    return result;
  }

  async updateStatus(
      id: string,
      status: WorksheetGeneratingStatus,
  ): Promise<Worksheet> {
    const worksheet = await this.worksheetRepository.findOne({
      where: { id },
    });

    if (!worksheet) {
      throw new NotFoundException('Worksheet not found');
    }

    worksheet.generatingStatus = status;
    return this.worksheetRepository.save(worksheet);
  }

  /**
   * Update worksheet with question synchronization data
   * @param id - Worksheet ID
   * @param questionIds - Array of MongoDB ObjectId strings
   * @param totalQuestions - Total number of questions
   * @param questionMetadata - Question metadata object
   */
  async updateQuestionData(
    id: string,
    questionIds: string[],
    totalQuestions: number,
    questionMetadata: {
      lastQuestionUpdate?: Date;
      questionVersion?: number;
      hasUnsavedChanges?: boolean;
      collaborators?: string[];
      lockStatus?: {
        isLocked: boolean;
        lockedBy?: string;
        lockedAt?: Date;
        lockReason?: string;
      };
    }
  ): Promise<Worksheet> {
    const worksheet = await this.worksheetRepository.findOne({
      where: { id },
    });

    if (!worksheet) {
      throw new NotFoundException('Worksheet not found');
    }

    worksheet.questionIds = questionIds;
    worksheet.totalQuestions = totalQuestions;
    worksheet.questionMetadata = questionMetadata;

    return this.worksheetRepository.save(worksheet);
  }

  async findAll(listWorksheetDto: ListWorksheetDto, user: User): Promise<{ items: Worksheet[]; meta: any }> {
    const { page, pageSize, schoolId } = listWorksheetDto;

    // Calculate skip value for pagination
    const skip = (page - 1) * pageSize;

    // Authorization and filtering logic
    const where: FindOptionsWhere<Worksheet> = {};

    if (schoolId) {
      // Explicit schoolId provided in request
      if (user.role === EUserRole.ADMIN) {
        // Admin users can filter by any schoolId
        where.schoolId = schoolId;
        this.logger.debug(`Admin user ${user.id} filtering worksheets by schoolId: ${schoolId}`);
      } else {
        // Non-admin users can only access their own school's worksheets
        if (!user.schoolId) {
          this.logger.warn(`User ${user.id} with role ${user.role} attempted to filter by schoolId but has no associated school`);
          throw new ForbiddenException('User is not associated with any school');
        }

        if (user.schoolId !== schoolId) {
          this.logger.warn(`User ${user.id} with role ${user.role} attempted to access worksheets from different school. User school: ${user.schoolId}, Requested school: ${schoolId}`);
          throw new ForbiddenException('Access denied: Cannot access worksheets from a different school');
        }

        // User is requesting their own school's worksheets
        where.schoolId = schoolId;
        this.logger.debug(`User ${user.id} with role ${user.role} filtering worksheets by their own schoolId: ${schoolId}`);
      }
    } else {
      // No explicit schoolId provided
      if (user.role === EUserRole.ADMIN) {
        // Admin users without schoolId see all worksheets
        this.logger.debug(`Admin user ${user.id} retrieving all worksheets (no school filter)`);
        // No where clause for schoolId - return all worksheets
      } else {
        // Non-admin users without explicit schoolId use their own school
        if (user.schoolId) {
          where.schoolId = user.schoolId;
          this.logger.debug(`User ${user.id} with role ${user.role} implicitly filtering by their schoolId: ${user.schoolId}`);
        } else {
          // Non-admin user with no school association
          this.logger.warn(`User ${user.id} with role ${user.role} has no associated school and cannot access worksheets`);
          // Return empty results for users not associated with any school
          return {
            items: [],
            meta: {
              page,
              pageSize,
              total: 0,
              totalPages: 0,
            },
          };
        }
      }
    }

    const findOptions: FindManyOptions<Worksheet> = {
      where,
      skip,
      take: pageSize,
      relations: [
        'selectedOptions',
        'selectedOptions.optionType',
        'selectedOptions.optionValue',
      ],
      order: {
        createdAt: 'DESC',
      },
    };

    // Get paginated results
    const [items, total] = await this.worksheetRepository.findAndCount(findOptions);

    return {
      items,
      meta: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      },
    };
  }

  async findOne(id: string, user?: User, throwError = true): Promise<WorksheetWithExtras | null> {
    const worksheet = await this.worksheetRepository.findOne({
      where: { id },
      relations: [
        'selectedOptions',
        'selectedOptions.optionType',
        'selectedOptions.optionValue',
      ],
    });

    if (!worksheet) {
      if (throwError) {
        throw new NotFoundException('Worksheet not found');
      }
      return null;
    }

    // Check if user has access to this worksheet (same school)
    // Admin users can access worksheets from any school
    if (user && user.role !== EUserRole.ADMIN && user.schoolId && worksheet.schoolId && user.schoolId !== worksheet.schoolId) {
      this.logger.warn(`User ${user.id} with role ${user.role} attempted to access worksheet ${id} from different school. User school: ${user.schoolId}, Worksheet school: ${worksheet.schoolId}`);
      if (throwError) {
        throw new NotFoundException('Worksheet not found'); // Don't reveal that it exists but user can't access it
      }
      return null;
    }

    const promptResult = await this.worksheetPromptResultModel.findOne({
      worksheetId: id,
    });

    const documentResult = await this.worksheetDocumentCacheService.getByWorksheetId(id);

    return {
      ...worksheet,
      promptResult: promptResult?.promptResult,
      documentResult: documentResult?.documentResult,
    };
  }

  async remove(id: string, user: User): Promise<void> {
    const worksheet = await this.findOne(id, user);
    if (!worksheet) {
      throw new NotFoundException(`Worksheet with ID "${id}" not found`);
    }

    // Authorization check (already done in controller, but good for service layer too)
    // Admin users can delete worksheets from any school
    if (user.role !== EUserRole.ADMIN && user.schoolId !== worksheet.schoolId) {
      this.logger.warn(
        `User ${user.id} with role ${user.role} attempted to delete worksheet ${id} from another school ${worksheet.schoolId} (user's school: ${user.schoolId})`,
      );
      throw new ForbiddenException('You do not have permission to delete this worksheet.');
    }

    // Soft delete the worksheet
    await this.worksheetRepository.softDelete(id);
    this.logger.log(`Worksheet with ID "${id}" soft deleted by user ${user.id}`);

    // Clean up associated data
    // 1. Delete WorksheetOptions
    await this.worksheetOptionRepository.delete({ worksheet: { id } });
    this.logger.log(`Deleted WorksheetOptions for worksheet ID "${id}"`);

    // 2. Delete associated cached documents from MongoDB
    // Assuming worksheetId in WorksheetDocument schema corresponds to the worksheet's id
    try {
      const deleteResult = await this.worksheetDocumentModel.deleteMany({ worksheetId: id });
      this.logger.log(`Deleted ${deleteResult.deletedCount} cached documents for worksheet ID "${id}" from MongoDB`);
    } catch (error) {
      this.logger.error(`Error deleting cached documents for worksheet ID "${id}": ${error.message}`, error.stack);
      // Decide if this error should prevent the operation or just be logged
    }

    // 3. (Optional) Delete from WorksheetPromptResult if applicable
    try {
        const promptResultDelete = await this.worksheetPromptResultModel.deleteMany({ worksheetId: id });
        this.logger.log(`Deleted ${promptResultDelete.deletedCount} prompt results for worksheet ID "${id}"`);
    } catch (error) {
        this.logger.error(`Error deleting prompt results for worksheet ID "${id}": ${error.message}`, error.stack);
    }

    // Add any other cleanup logic here (e.g., removing files from storage if applicable)
  }
}
