import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Public } from '../../auth/decorators/public.decorator';
import { Roles } from '../../auth/decorators/role.decorator';
import { AuthGuard } from '../../auth/guards/auth.guard';
import { RoleGuard } from '../../auth/guards/role.guard';
import { EUserRole } from '../../user/dto/create-user.dto';
import { WorksheetQuestionMetricsService } from '../services/worksheet-question-metrics.service';

/**
 * Controller for worksheet question performance metrics endpoints
 */
@ApiTags('Worksheet Question Metrics')
@Controller('worksheets/questions/metrics')
@UseGuards(AuthGuard, RoleGuard)
@ApiBearerAuth()
export class WorksheetQuestionMetricsController {
  constructor(
    private readonly metricsService: WorksheetQuestionMetricsService
  ) {}

  /**
   * Get Prometheus metrics for worksheet question operations
   * Public endpoint for Prometheus scraping
   */
  @Get()
  @Public()
  @ApiOperation({ 
    summary: 'Get Prometheus metrics for worksheet question operations',
    description: 'Returns metrics in Prometheus format for scraping by monitoring systems'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Metrics retrieved successfully',
    content: {
      'text/plain': {
        schema: {
          type: 'string',
          example: `# HELP worksheet_question_api_duration_seconds Duration of worksheet question API operations in seconds
# TYPE worksheet_question_api_duration_seconds histogram
worksheet_question_api_duration_seconds_bucket{le="0.001",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"} 0
worksheet_question_api_duration_seconds_bucket{le="0.005",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"} 0
worksheet_question_api_duration_seconds_bucket{le="0.01",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"} 5
worksheet_question_api_duration_seconds_bucket{le="0.05",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"} 15
worksheet_question_api_duration_seconds_bucket{le="0.1",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"} 25
worksheet_question_api_duration_seconds_bucket{le="0.5",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"} 30
worksheet_question_api_duration_seconds_bucket{le="1",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"} 30
worksheet_question_api_duration_seconds_bucket{le="2",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"} 30
worksheet_question_api_duration_seconds_bucket{le="5",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"} 30
worksheet_question_api_duration_seconds_bucket{le="10",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"} 30
worksheet_question_api_duration_seconds_bucket{le="+Inf",method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"} 30
worksheet_question_api_duration_seconds_sum{method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"} 0.75
worksheet_question_api_duration_seconds_count{method="POST",endpoint="/worksheets/:id/questions",status_code="201",user_role="teacher"} 30`
        }
      }
    }
  })
  async getPrometheusMetrics(): Promise<string> {
    return this.metricsService.getMetrics();
  }

  /**
   * Get performance summary for dashboard
   * Requires admin or school manager role
   */
  @Get('summary')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @ApiOperation({ 
    summary: 'Get performance summary for worksheet question operations',
    description: 'Returns a JSON summary with key performance indicators for dashboard display'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Performance summary retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalApiRequests: {
          type: 'number',
          description: 'Total number of API requests processed',
          example: 1250
        },
        totalApiErrors: {
          type: 'number',
          description: 'Total number of API errors encountered',
          example: 15
        },
        averageApiDuration: {
          type: 'number',
          description: 'Average API response time in seconds',
          example: 0.045
        },
        cacheHitRate: {
          type: 'number',
          description: 'Cache hit rate as a percentage',
          example: 75.5
        },
        activeOperations: {
          type: 'number',
          description: 'Number of currently active operations',
          example: 8
        },
        memoryUsage: {
          type: 'number',
          description: 'Current memory usage in bytes',
          example: 134217728
        },
        timestamp: {
          type: 'string',
          format: 'date-time',
          description: 'Timestamp when metrics were collected',
          example: '2024-01-15T10:30:00.000Z'
        }
      }
    }
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions' })
  async getPerformanceSummary() {
    return this.metricsService.getPerformanceSummary();
  }

  /**
   * Get detailed performance breakdown
   * Admin only endpoint for detailed analysis
   */
  @Get('detailed')
  @Roles(EUserRole.ADMIN)
  @ApiOperation({ 
    summary: 'Get detailed performance breakdown for worksheet question operations',
    description: 'Returns detailed performance metrics for administrative analysis and troubleshooting'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Detailed metrics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        apiMetrics: {
          type: 'object',
          properties: {
            requestsByEndpoint: {
              type: 'object',
              description: 'Request counts grouped by endpoint'
            },
            errorsByType: {
              type: 'object',
              description: 'Error counts grouped by error type'
            },
            responseTimePercentiles: {
              type: 'object',
              description: 'Response time percentiles (P50, P95, P99)'
            }
          }
        },
        cacheMetrics: {
          type: 'object',
          properties: {
            hitRateByType: {
              type: 'object',
              description: 'Cache hit rates by cache type'
            },
            operationDurations: {
              type: 'object',
              description: 'Cache operation durations'
            }
          }
        },
        databaseMetrics: {
          type: 'object',
          properties: {
            queryDurations: {
              type: 'object',
              description: 'Database query durations by operation'
            },
            connectionPoolStatus: {
              type: 'object',
              description: 'Database connection pool status'
            }
          }
        },
        collaborationMetrics: {
          type: 'object',
          properties: {
            eventsByType: {
              type: 'object',
              description: 'Collaboration events by type'
            },
            activeCollaboratorsByWorksheet: {
              type: 'object',
              description: 'Active collaborators per worksheet'
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  async getDetailedMetrics() {
    const registry = this.metricsService.getRegistry();
    const metrics = await registry.getMetricsAsJSON();
    
    // Process and organize metrics for detailed analysis
    const detailedMetrics = {
      apiMetrics: this.processApiMetrics(metrics),
      cacheMetrics: this.processCacheMetrics(metrics),
      databaseMetrics: this.processDatabaseMetrics(metrics),
      collaborationMetrics: this.processCollaborationMetrics(metrics),
      timestamp: new Date().toISOString()
    };

    return detailedMetrics;
  }

  /**
   * Process API metrics for detailed view
   */
  private processApiMetrics(metrics: any[]) {
    const apiRequestsMetric = metrics.find(m => m.name === 'worksheet_question_api_requests_total');
    const apiErrorsMetric = metrics.find(m => m.name === 'worksheet_question_api_errors_total');
    const apiDurationMetric = metrics.find(m => m.name === 'worksheet_question_api_duration_seconds');

    return {
      requestsByEndpoint: this.groupMetricsByLabel(apiRequestsMetric, 'endpoint'),
      errorsByType: this.groupMetricsByLabel(apiErrorsMetric, 'error_type'),
      responseTimePercentiles: this.calculatePercentiles(apiDurationMetric)
    };
  }

  /**
   * Process cache metrics for detailed view
   */
  private processCacheMetrics(metrics: any[]) {
    const cacheHitsMetric = metrics.find(m => m.name === 'worksheet_question_cache_hits_total');
    const cacheMissesMetric = metrics.find(m => m.name === 'worksheet_question_cache_misses_total');
    const cacheOperationMetric = metrics.find(m => m.name === 'worksheet_question_cache_operation_duration_seconds');

    return {
      hitRateByType: this.calculateCacheHitRates(cacheHitsMetric, cacheMissesMetric),
      operationDurations: this.groupMetricsByLabel(cacheOperationMetric, 'operation')
    };
  }

  /**
   * Process database metrics for detailed view
   */
  private processDatabaseMetrics(metrics: any[]) {
    const dbQueryMetric = metrics.find(m => m.name === 'worksheet_question_db_query_duration_seconds');
    const dbConnectionsMetric = metrics.find(m => m.name === 'worksheet_question_db_connections_active');

    return {
      queryDurations: this.groupMetricsByLabel(dbQueryMetric, 'operation'),
      connectionPoolStatus: dbConnectionsMetric ? dbConnectionsMetric.values : []
    };
  }

  /**
   * Process collaboration metrics for detailed view
   */
  private processCollaborationMetrics(metrics: any[]) {
    const collaborationEventsMetric = metrics.find(m => m.name === 'worksheet_question_collaboration_events_total');
    const activeCollaboratorsMetric = metrics.find(m => m.name === 'worksheet_question_active_collaborators');

    return {
      eventsByType: this.groupMetricsByLabel(collaborationEventsMetric, 'event_type'),
      activeCollaboratorsByWorksheet: this.groupMetricsByLabel(activeCollaboratorsMetric, 'worksheet_id')
    };
  }

  /**
   * Helper method to group metrics by label
   */
  private groupMetricsByLabel(metric: any, labelName: string): any {
    if (!metric || !metric.values) return {};

    return metric.values.reduce((acc: any, value: any) => {
      const labelValue = value.labels[labelName] || 'unknown';
      acc[labelValue] = (acc[labelValue] || 0) + value.value;
      return acc;
    }, {});
  }

  /**
   * Helper method to calculate percentiles from histogram
   */
  private calculatePercentiles(metric: any): any {
    if (!metric || !metric.values) return {};

    // This is a simplified percentile calculation
    // In a real implementation, you'd want more sophisticated percentile calculation
    const buckets = metric.values.filter((v: any) => v.labels.le !== '+Inf');
    const totalCount = metric.values.find((v: any) => v.labels.le === '+Inf')?.value || 0;

    if (totalCount === 0) return { p50: 0, p95: 0, p99: 0 };

    const p50Threshold = totalCount * 0.5;
    const p95Threshold = totalCount * 0.95;
    const p99Threshold = totalCount * 0.99;

    let p50 = 0, p95 = 0, p99 = 0;
    let cumulativeCount = 0;

    for (const bucket of buckets) {
      cumulativeCount += bucket.value;
      const bucketValue = parseFloat(bucket.labels.le);

      if (p50 === 0 && cumulativeCount >= p50Threshold) p50 = bucketValue;
      if (p95 === 0 && cumulativeCount >= p95Threshold) p95 = bucketValue;
      if (p99 === 0 && cumulativeCount >= p99Threshold) p99 = bucketValue;
    }

    return { p50, p95, p99 };
  }

  /**
   * Helper method to calculate cache hit rates by type
   */
  private calculateCacheHitRates(hitsMetric: any, missesMetric: any): any {
    const hitsByType = this.groupMetricsByLabel(hitsMetric, 'cache_type');
    const missesByType = this.groupMetricsByLabel(missesMetric, 'cache_type');

    const hitRates: any = {};
    const allTypes = new Set([...Object.keys(hitsByType), ...Object.keys(missesByType)]);

    for (const type of allTypes) {
      const hits = hitsByType[type] || 0;
      const misses = missesByType[type] || 0;
      const total = hits + misses;
      hitRates[type] = total > 0 ? (hits / total) * 100 : 0;
    }

    return hitRates;
  }
}
