import { Controller, Get, Post, Query, Param, Res, UseGuards, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Response } from 'express';
import { Roles } from '../../auth/decorators/role.decorator';
import { AuthGuard } from '../../auth/guards/auth.guard';
import { RoleGuard } from '../../auth/guards/role.guard';
import { EUserRole } from '../../user/dto/create-user.dto';
import { ActiveUser } from '../../auth/decorators/active-user.decorator';
import { User } from '../../user/entities/user.entity';
import { WorksheetQuestionMemoryOptimizationService, PaginationOptions } from '../services/worksheet-question-memory-optimization.service';

/**
 * Controller for memory optimization features in worksheet question management
 */
@ApiTags('Worksheet Memory Optimization')
@Controller('worksheets/memory')
@UseGuards(AuthGuard, RoleGuard)
@ApiBearerAuth()
export class WorksheetMemoryOptimizationController {
  constructor(
    private readonly memoryOptimizationService: WorksheetQuestionMemoryOptimizationService
  ) {}

  /**
   * Get paginated worksheet questions with memory optimization
   */
  @Get(':worksheetId/questions/paginated')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ 
    summary: 'Get paginated worksheet questions with memory optimization',
    description: 'Returns paginated worksheet questions with memory usage tracking and optimization'
  })
  @ApiParam({ name: 'worksheetId', description: 'ID of the worksheet' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20, max: 100)' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Field to sort by (default: position)' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Sort order (default: asc)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Paginated questions retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' },
          description: 'Array of questions for the current page'
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' },
            hasNext: { type: 'boolean' },
            hasPrev: { type: 'boolean' }
          }
        },
        memoryUsage: {
          type: 'object',
          properties: {
            heapUsed: { type: 'number', description: 'Heap memory used in bytes' },
            heapTotal: { type: 'number', description: 'Total heap memory in bytes' },
            external: { type: 'number', description: 'External memory in bytes' },
            rss: { type: 'number', description: 'Resident set size in bytes' }
          }
        }
      }
    }
  })
  async getPaginatedQuestions(
    @Param('worksheetId') worksheetId: string,
    @ActiveUser() user: User,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder: 'asc' | 'desc' = 'asc'
  ) {
    const options: PaginationOptions = {
      page: Number(page),
      limit: Number(limit),
      sortBy,
      sortOrder,
      filters: {}
    };

    // Add school-based filtering for non-admin users
    if (user.role !== EUserRole.ADMIN && user.schoolId) {
      options.filters = { schoolId: user.schoolId };
    }

    return this.memoryOptimizationService.getPaginatedQuestions(worksheetId, options);
  }

  /**
   * Stream worksheet questions for large exports
   */
  @Get(':worksheetId/questions/stream')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ 
    summary: 'Stream worksheet questions for large exports',
    description: 'Returns a streaming response for memory-efficient export of large worksheets'
  })
  @ApiParam({ name: 'worksheetId', description: 'ID of the worksheet' })
  @ApiQuery({ name: 'format', required: false, enum: ['json', 'csv'], description: 'Export format (default: json)' })
  @ApiQuery({ name: 'batchSize', required: false, type: Number, description: 'Batch size for streaming (default: 50)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Streaming export started successfully',
    content: {
      'application/json': {
        schema: { type: 'string', description: 'Streaming JSON data' }
      },
      'text/csv': {
        schema: { type: 'string', description: 'Streaming CSV data' }
      }
    }
  })
  async streamQuestions(
    @Param('worksheetId') worksheetId: string,
    @Query('format') format: 'json' | 'csv' = 'json',
    @Query('batchSize') batchSize: number = 50,
    @Res() res: Response
  ) {
    const streamOptions = {
      batchSize: Number(batchSize),
      objectMode: true
    };

    // Set appropriate headers for streaming
    res.setHeader('Content-Type', format === 'csv' ? 'text/csv' : 'application/json');
    res.setHeader('Transfer-Encoding', 'chunked');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Content-Disposition', `attachment; filename="worksheet-${worksheetId}-questions.${format}"`);

    const questionStream = this.memoryOptimizationService.createQuestionStream(worksheetId, streamOptions);

    if (format === 'json') {
      res.write('['); // Start JSON array
      let isFirst = true;

      questionStream.on('data', (question) => {
        if (!isFirst) {
          res.write(',');
        }
        res.write(JSON.stringify(question));
        isFirst = false;
      });

      questionStream.on('end', () => {
        res.write(']'); // End JSON array
        res.end();
      });
    } else {
      // CSV format
      let isFirst = true;

      questionStream.on('data', (question) => {
        if (isFirst) {
          // Write CSV headers
          const headers = Object.keys(question).join(',');
          res.write(headers + '\n');
          isFirst = false;
        }

        // Write CSV row
        const values = Object.values(question).map(value => 
          typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value
        ).join(',');
        res.write(values + '\n');
      });

      questionStream.on('end', () => {
        res.end();
      });
    }

    questionStream.on('error', (error) => {
      res.status(500).json({ error: 'Streaming failed', message: error.message });
    });
  }

  /**
   * Lazy load specific question details
   */
  @Get(':worksheetId/questions/:questionId/lazy')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ 
    summary: 'Lazy load specific question details',
    description: 'Returns only requested fields of a question to minimize memory usage'
  })
  @ApiParam({ name: 'worksheetId', description: 'ID of the worksheet' })
  @ApiParam({ name: 'questionId', description: 'ID of the question' })
  @ApiQuery({ 
    name: 'fields', 
    required: false, 
    type: String, 
    description: 'Comma-separated list of fields to load (e.g., content,options,answer)' 
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Question details loaded successfully',
    schema: {
      type: 'object',
      description: 'Partial question object with only requested fields'
    }
  })
  @ApiResponse({ status: 404, description: 'Question not found' })
  async lazyLoadQuestionDetails(
    @Param('worksheetId') worksheetId: string,
    @Param('questionId') questionId: string,
    @Query('fields') fields?: string
  ) {
    const fieldArray = fields ? fields.split(',').map(f => f.trim()) : [];
    
    const question = await this.memoryOptimizationService.lazyLoadQuestionDetails(
      worksheetId,
      questionId,
      fieldArray
    );

    if (!question) {
      throw new Error('Question not found');
    }

    return {
      question,
      loadedFields: fieldArray.length > 0 ? fieldArray : 'all',
      worksheetId,
      questionId,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get memory usage statistics
   */
  @Get('statistics')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @ApiOperation({ 
    summary: 'Get memory usage statistics',
    description: 'Returns current memory usage statistics and optimization metrics'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Memory statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        current: {
          type: 'object',
          properties: {
            heapUsed: { type: 'number', description: 'Current heap usage in MB' },
            heapTotal: { type: 'number', description: 'Total heap size in MB' },
            external: { type: 'number', description: 'External memory in MB' },
            rss: { type: 'number', description: 'Resident set size in MB' }
          }
        },
        thresholds: {
          type: 'object',
          properties: {
            warning: { type: 'number', description: 'Warning threshold in MB' },
            critical: { type: 'number', description: 'Critical threshold in MB' }
          }
        },
        alerts: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              operation: { type: 'string' },
              alertCount: { type: 'number' }
            }
          }
        },
        configuration: { type: 'object', description: 'Memory optimization configuration' }
      }
    }
  })
  async getMemoryStatistics() {
    return this.memoryOptimizationService.getMemoryStatistics();
  }

  /**
   * Trigger memory cleanup
   */
  @Post('cleanup')
  @Roles(EUserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Trigger memory cleanup',
    description: 'Manually triggers garbage collection and memory cleanup (Admin only)'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Memory cleanup completed successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        memoryBefore: { type: 'object' },
        memoryAfter: { type: 'object' },
        cleaned: { type: 'boolean' }
      }
    }
  })
  async triggerMemoryCleanup(@ActiveUser() user: User) {
    const memoryBefore = process.memoryUsage();
    
    await this.memoryOptimizationService.performMemoryCleanup();
    
    const memoryAfter = process.memoryUsage();
    
    const memoryBeforeMB = {
      heapUsed: Math.round(memoryBefore.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memoryBefore.heapTotal / 1024 / 1024),
      external: Math.round(memoryBefore.external / 1024 / 1024),
      rss: Math.round(memoryBefore.rss / 1024 / 1024)
    };

    const memoryAfterMB = {
      heapUsed: Math.round(memoryAfter.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memoryAfter.heapTotal / 1024 / 1024),
      external: Math.round(memoryAfter.external / 1024 / 1024),
      rss: Math.round(memoryAfter.rss / 1024 / 1024)
    };

    const memoryFreed = memoryBefore.heapUsed - memoryAfter.heapUsed;

    return {
      message: 'Memory cleanup completed',
      memoryBefore: memoryBeforeMB,
      memoryAfter: memoryAfterMB,
      memoryFreed: Math.round(memoryFreed / 1024 / 1024),
      cleaned: memoryFreed > 0,
      triggeredBy: user.id,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get memory optimization recommendations
   */
  @Get('recommendations')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @ApiOperation({ 
    summary: 'Get memory optimization recommendations',
    description: 'Returns recommendations for improving memory usage based on current patterns'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Recommendations retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        recommendations: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              category: { type: 'string' },
              priority: { type: 'string', enum: ['low', 'medium', 'high'] },
              description: { type: 'string' },
              action: { type: 'string' }
            }
          }
        },
        currentUsage: { type: 'object' },
        projectedSavings: { type: 'object' }
      }
    }
  })
  async getMemoryRecommendations() {
    const stats = this.memoryOptimizationService.getMemoryStatistics();
    const recommendations = this.generateRecommendations(stats);

    return {
      recommendations,
      currentUsage: stats.current,
      projectedSavings: this.calculateProjectedSavings(recommendations),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Generate memory optimization recommendations
   */
  private generateRecommendations(stats: any): any[] {
    const recommendations: any[] = [];

    // Check heap usage
    if (stats.current.heapUsed > stats.thresholds.warning) {
      recommendations.push({
        category: 'Memory Usage',
        priority: stats.current.heapUsed > stats.thresholds.critical ? 'high' : 'medium',
        description: `Heap memory usage is ${stats.current.heapUsed}MB, above the ${stats.thresholds.warning}MB threshold`,
        action: 'Consider implementing more aggressive caching strategies or increasing pagination limits'
      });
    }

    // Check for frequent alerts
    const highAlertOperations = stats.alerts.filter(alert => alert.alertCount > 5);
    if (highAlertOperations.length > 0) {
      recommendations.push({
        category: 'Alert Frequency',
        priority: 'medium',
        description: `High alert frequency detected for operations: ${highAlertOperations.map(a => a.operation).join(', ')}`,
        action: 'Review and optimize the flagged operations for better memory efficiency'
      });
    }

    // General recommendations
    recommendations.push({
      category: 'Best Practices',
      priority: 'low',
      description: 'Regular memory cleanup can help maintain optimal performance',
      action: 'Schedule periodic memory cleanup operations during low-traffic periods'
    });

    return recommendations;
  }

  /**
   * Calculate projected memory savings
   */
  private calculateProjectedSavings(recommendations: any[]): any {
    const highPriorityCount = recommendations.filter(r => r.priority === 'high').length;
    const mediumPriorityCount = recommendations.filter(r => r.priority === 'medium').length;

    return {
      estimatedSavingsMB: (highPriorityCount * 20) + (mediumPriorityCount * 10),
      implementationEffort: highPriorityCount > 0 ? 'high' : mediumPriorityCount > 0 ? 'medium' : 'low',
      timeframe: '1-2 weeks'
    };
  }
}
