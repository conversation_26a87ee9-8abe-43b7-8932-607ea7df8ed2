import { Controller, Get, Post, Body, Query, UseGuards, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiBody, ApiQuery } from '@nestjs/swagger';
import { Roles } from '../../auth/decorators/role.decorator';
import { AuthGuard } from '../../auth/guards/auth.guard';
import { RoleGuard } from '../../auth/guards/role.guard';
import { EUserRole } from '../../user/dto/create-user.dto';
import { ActiveUser } from '../../auth/decorators/active-user.decorator';
import { User } from '../../user/entities/user.entity';
import { WorksheetQuestionPerformanceTestingService, PerformanceTestConfig } from '../services/worksheet-question-performance-testing.service';

/**
 * Controller for performance testing and benchmarking of worksheet question operations
 * Admin-only endpoints for running performance tests and generating reports
 */
@ApiTags('Worksheet Performance Testing')
@Controller('worksheets/performance')
@UseGuards(AuthGuard, RoleGuard)
@ApiBearerAuth()
export class WorksheetPerformanceTestingController {
  constructor(
    private readonly performanceTestingService: WorksheetQuestionPerformanceTestingService
  ) {}

  /**
   * Run comprehensive performance test suite
   */
  @Post('test-suite')
  @Roles(EUserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Run comprehensive performance test suite',
    description: 'Executes a full suite of performance tests including response time, throughput, memory usage, and cache performance tests'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Performance test suite completed successfully',
    schema: {
      type: 'object',
      properties: {
        summary: {
          type: 'object',
          properties: {
            totalTests: { type: 'number' },
            passedTests: { type: 'number' },
            failedTests: { type: 'number' },
            averageResponseTime: { type: 'number' },
            averageThroughput: { type: 'number' },
            overallRecommendations: { type: 'array', items: { type: 'string' } }
          }
        },
        detailedResults: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              testName: { type: 'string' },
              status: { type: 'string', enum: ['PASS', 'FAIL'] },
              performance: { type: 'string', enum: ['EXCELLENT', 'GOOD', 'ACCEPTABLE', 'POOR'] },
              keyMetrics: {
                type: 'object',
                properties: {
                  responseTime: { type: 'string' },
                  throughput: { type: 'string' },
                  successRate: { type: 'string' },
                  memoryUsage: { type: 'string' }
                }
              },
              recommendations: { type: 'array', items: { type: 'string' } }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  async runPerformanceTestSuite(@ActiveUser() user: User) {
    const startTime = Date.now();
    
    const testResults = await this.performanceTestingService.runPerformanceTestSuite();
    const report = this.performanceTestingService.generatePerformanceReport(testResults);
    
    const totalDuration = Date.now() - startTime;

    return {
      ...report,
      executionInfo: {
        totalDuration: `${totalDuration}ms`,
        executedBy: user.id,
        executedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Run custom performance test
   */
  @Post('custom-test')
  @Roles(EUserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Run custom performance test',
    description: 'Executes a custom performance test with specified configuration'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        testName: { type: 'string', description: 'Name of the test' },
        description: { type: 'string', description: 'Description of the test' },
        iterations: { type: 'number', description: 'Number of iterations to run' },
        concurrency: { type: 'number', description: 'Number of concurrent operations' },
        warmupIterations: { type: 'number', description: 'Number of warmup iterations' },
        timeout: { type: 'number', description: 'Timeout in milliseconds' },
        dataSize: { type: 'string', enum: ['small', 'medium', 'large', 'xlarge'] },
        cacheEnabled: { type: 'boolean', description: 'Whether to enable caching' },
        backgroundProcessing: { type: 'boolean', description: 'Whether to test background processing' }
      },
      required: ['testName', 'iterations', 'concurrency']
    }
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Custom performance test completed successfully',
    schema: {
      type: 'object',
      properties: {
        testName: { type: 'string' },
        config: { type: 'object' },
        results: {
          type: 'object',
          properties: {
            totalDuration: { type: 'number' },
            averageResponseTime: { type: 'number' },
            minResponseTime: { type: 'number' },
            maxResponseTime: { type: 'number' },
            p50ResponseTime: { type: 'number' },
            p95ResponseTime: { type: 'number' },
            p99ResponseTime: { type: 'number' },
            throughput: { type: 'number' },
            successRate: { type: 'number' },
            errorRate: { type: 'number' },
            memoryUsage: { type: 'object' },
            cacheMetrics: { type: 'object' }
          }
        },
        errors: { type: 'array', items: { type: 'string' } },
        recommendations: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  async runCustomPerformanceTest(
    @Body() config: PerformanceTestConfig,
    @ActiveUser() user: User
  ) {
    const result = await this.performanceTestingService.runPerformanceTest(config);
    
    return {
      ...result,
      executionInfo: {
        executedBy: user.id,
        executedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Run load testing scenarios
   */
  @Post('load-test')
  @Roles(EUserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Run load testing scenarios',
    description: 'Executes predefined load testing scenarios to test system performance under various load conditions'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Load testing scenarios completed successfully',
    schema: {
      type: 'object',
      properties: {
        scenarios: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              scenarioName: { type: 'string' },
              status: { type: 'string' },
              metrics: {
                type: 'object',
                properties: {
                  averageResponseTime: { type: 'number' },
                  throughput: { type: 'number' },
                  errorRate: { type: 'number' }
                }
              }
            }
          }
        },
        summary: { type: 'object' }
      }
    }
  })
  async runLoadTestScenarios(@ActiveUser() user: User) {
    const startTime = Date.now();
    
    const scenarios = await this.performanceTestingService.runLoadTestScenarios();
    const totalDuration = Date.now() - startTime;

    const summary = {
      totalScenarios: scenarios.length,
      completedScenarios: scenarios.filter(s => s.status === 'COMPLETED').length,
      averageResponseTime: this.calculateAverage(scenarios.map(s => s.metrics?.averageResponseTime || 0)),
      averageThroughput: this.calculateAverage(scenarios.map(s => s.metrics?.throughput || 0)),
      averageErrorRate: this.calculateAverage(scenarios.map(s => s.metrics?.errorRate || 0))
    };

    return {
      scenarios,
      summary,
      executionInfo: {
        totalDuration: `${totalDuration}ms`,
        executedBy: user.id,
        executedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Get performance benchmarks
   */
  @Get('benchmarks')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @ApiOperation({ 
    summary: 'Get performance benchmarks',
    description: 'Returns current performance benchmarks and thresholds for worksheet question operations'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Performance benchmarks retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        responseTimeThresholds: {
          type: 'object',
          properties: {
            excellent: { type: 'number' },
            good: { type: 'number' },
            acceptable: { type: 'number' },
            poor: { type: 'number' }
          }
        },
        throughputThresholds: {
          type: 'object',
          properties: {
            minimum: { type: 'number' },
            good: { type: 'number' },
            excellent: { type: 'number' }
          }
        },
        memoryThresholds: {
          type: 'object',
          properties: {
            warning: { type: 'number' },
            critical: { type: 'number' }
          }
        },
        cacheThresholds: {
          type: 'object',
          properties: {
            minimum: { type: 'number' },
            good: { type: 'number' },
            excellent: { type: 'number' }
          }
        }
      }
    }
  })
  async getPerformanceBenchmarks() {
    return {
      responseTimeThresholds: {
        excellent: 100,
        good: 500,
        acceptable: 1000,
        poor: 2000
      },
      throughputThresholds: {
        minimum: 10,
        good: 50,
        excellent: 100
      },
      memoryThresholds: {
        warning: 100,
        critical: 200
      },
      cacheThresholds: {
        minimum: 70,
        good: 85,
        excellent: 95
      },
      units: {
        responseTime: 'milliseconds',
        throughput: 'requests per second',
        memory: 'megabytes',
        cache: 'percentage'
      },
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Get performance testing history
   */
  @Get('history')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @ApiOperation({ 
    summary: 'Get performance testing history',
    description: 'Returns historical performance test results for trend analysis'
  })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of results to return (default: 10)' })
  @ApiQuery({ name: 'testType', required: false, type: String, description: 'Filter by test type' })
  @ApiResponse({ 
    status: 200, 
    description: 'Performance testing history retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        history: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              testId: { type: 'string' },
              testName: { type: 'string' },
              executedAt: { type: 'string' },
              executedBy: { type: 'string' },
              results: { type: 'object' },
              status: { type: 'string' }
            }
          }
        },
        trends: {
          type: 'object',
          properties: {
            responseTimeTrend: { type: 'string', enum: ['improving', 'stable', 'degrading'] },
            throughputTrend: { type: 'string', enum: ['improving', 'stable', 'degrading'] },
            recommendations: { type: 'array', items: { type: 'string' } }
          }
        }
      }
    }
  })
  async getPerformanceTestingHistory(
    @Query('limit') limit: number = 10,
    @Query('testType') testType?: string
  ) {
    // This would typically fetch from a database
    // For now, return mock data
    const mockHistory = [
      {
        testId: 'test_001',
        testName: 'Basic Question Retrieval',
        executedAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        executedBy: 'admin_user',
        results: {
          averageResponseTime: 120,
          throughput: 45,
          successRate: 98.5
        },
        status: 'COMPLETED'
      },
      {
        testId: 'test_002',
        testName: 'Cache Performance',
        executedAt: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
        executedBy: 'admin_user',
        results: {
          averageResponseTime: 85,
          throughput: 65,
          successRate: 99.2
        },
        status: 'COMPLETED'
      }
    ];

    const trends = {
      responseTimeTrend: 'improving' as const,
      throughputTrend: 'stable' as const,
      recommendations: [
        'Response times have improved over the last week',
        'Consider running more frequent performance tests during peak hours'
      ]
    };

    return {
      history: mockHistory.slice(0, limit),
      trends,
      totalResults: mockHistory.length,
      filters: {
        limit,
        testType: testType || 'all'
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Generate performance report
   */
  @Get('report')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @ApiOperation({ 
    summary: 'Generate performance report',
    description: 'Generates a comprehensive performance report based on recent test results'
  })
  @ApiQuery({ name: 'period', required: false, enum: ['day', 'week', 'month'], description: 'Report period (default: week)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Performance report generated successfully',
    schema: {
      type: 'object',
      properties: {
        reportPeriod: { type: 'string' },
        summary: { type: 'object' },
        performanceMetrics: { type: 'object' },
        recommendations: { type: 'array', items: { type: 'string' } },
        nextSteps: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  async generatePerformanceReport(
    @Query('period') period: 'day' | 'week' | 'month' = 'week'
  ) {
    // This would generate a comprehensive report based on historical data
    return {
      reportPeriod: period,
      summary: {
        testsExecuted: 15,
        averagePerformance: 'GOOD',
        criticalIssues: 0,
        improvementOpportunities: 3
      },
      performanceMetrics: {
        responseTime: {
          current: 145,
          target: 100,
          trend: 'improving'
        },
        throughput: {
          current: 52,
          target: 50,
          trend: 'stable'
        },
        cacheHitRate: {
          current: 87,
          target: 90,
          trend: 'improving'
        }
      },
      recommendations: [
        'Continue current optimization efforts',
        'Consider implementing additional caching layers',
        'Monitor memory usage during peak hours'
      ],
      nextSteps: [
        'Schedule weekly performance tests',
        'Set up automated performance monitoring',
        'Review and update performance thresholds quarterly'
      ],
      generatedAt: new Date().toISOString()
    };
  }

  /**
   * Helper method to calculate average
   */
  private calculateAverage(numbers: number[]): number {
    return numbers.length > 0 ? numbers.reduce((sum, num) => sum + num, 0) / numbers.length : 0;
  }
}
