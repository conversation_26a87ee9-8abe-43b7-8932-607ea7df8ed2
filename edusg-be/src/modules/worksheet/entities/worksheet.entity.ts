import { <PERSON><PERSON><PERSON>, Column, OneToMany, ManyToOne, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, Index } from 'typeorm';
import { WorksheetOption } from './worksheet-option.entity';
import BaseEntity from '../../../core/entities/base-entity';
import { School } from '../../school/entities/school.entity';
import { IExerciseQuestion } from '../../../shared/interfaces/exercise-question.interface';

export enum WorksheetGeneratingStatus {
  PENDING = 'Pending',
  GENERATED = 'Generated',
  ERROR = 'Error',
}

@Entity('worksheets')
@Index(['schoolId', 'updatedAt'])
@Index(['schoolId', 'totalQuestions'])
@Index(['lastModifiedBy', 'updatedAt'])
export class Worksheet extends BaseEntity {
  @Column({ nullable: true })
  title: string;

  @Column({ nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: WorksheetGeneratingStatus,
    default: WorksheetGeneratingStatus.PENDING,
  })
  generatingStatus: WorksheetGeneratingStatus;

  @Column({ type: 'json', nullable: true })
  subjectData: any;

  @OneToMany(() => WorksheetOption, (wo) => wo.worksheet, { cascade: true })
  selectedOptions: WorksheetOption[];

  @ManyToOne(() => School, { nullable: true }) // Assuming a worksheet might not always be linked to a school
  @JoinColumn({ name: 'schoolId' })
  school: School;

  @Column({ nullable: true })
  schoolId: string;

  // Question management fields
  @Column({ type: 'jsonb', nullable: true })
  questions: IExerciseQuestion[];

  @Column({ default: 0 })
  totalQuestions: number;

  @Column({ nullable: true })
  lastModifiedBy: string; // User ID who last modified questions

  @Column({ nullable: true })
  createdBy: string; // User ID who created the worksheet

  @Column({ default: 100 })
  maxQuestions: number;

  // Array of MongoDB ObjectIds as strings (references to WorksheetQuestionDocument)
  @Column({ type: 'jsonb', nullable: true })
  questionIds: string[];

  // Question management metadata
  @Column({ type: 'jsonb', nullable: true })
  questionMetadata: {
    lastQuestionUpdate?: Date;
    questionVersion?: number;
    hasUnsavedChanges?: boolean;
    collaborators?: string[]; // User IDs of active collaborators
    lockStatus?: {
      isLocked: boolean;
      lockedBy?: string;
      lockedAt?: Date;
      lockReason?: string;
    };
  };
}
