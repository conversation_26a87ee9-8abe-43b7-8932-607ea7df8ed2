/**
 * WebSocket events for real-time worksheet question collaboration
 */
export enum CollaborationEvent {
  // Connection events
  JOIN_WORKSHEET = 'join_worksheet',
  LEAVE_WORKSHEET = 'leave_worksheet',
  CONNECTION_ESTABLISHED = 'connection_established',
  CONNECTION_ERROR = 'connection_error',

  // Presence events
  USER_JOINED = 'user_joined',
  USER_LEFT = 'user_left',
  USER_PRESENCE_UPDATE = 'user_presence_update',
  ACTIVE_USERS_LIST = 'active_users_list',
  PRESENCE_HEARTBEAT = 'presence_heartbeat',

  // Question locking events
  QUESTION_LOCK_ACQUIRED = 'question_lock_acquired',
  QUESTION_LOCK_RELEASED = 'question_lock_released',
  QUESTION_LOCK_FAILED = 'question_lock_failed',
  QUESTION_LOCK_TIMEOUT = 'question_lock_timeout',
  QUESTION_LOCK_CONFLICT = 'question_lock_conflict',
  QUESTION_LOCK_STATUS = 'question_lock_status',

  // Live editing events
  QUESTION_EDIT_STARTED = 'question_edit_started',
  QUESTION_EDIT_CANCELLED = 'question_edit_cancelled',
  QUESTION_CONTENT_CHANGED = 'question_content_changed',
  QUESTION_TYPING_INDICATOR = 'question_typing_indicator',
  QUESTION_EDIT_SAVED = 'question_edit_saved',

  // Conflict resolution events
  EDIT_CONFLICT_DETECTED = 'edit_conflict_detected',
  CONFLICT_RESOLUTION_REQUIRED = 'conflict_resolution_required',
  CONFLICT_RESOLVED = 'conflict_resolved',
  CONFLICT_MERGE_SUGGESTED = 'conflict_merge_suggested',

  // Question CRUD events (real-time broadcasts)
  QUESTION_ADDED_REALTIME = 'question_added_realtime',
  QUESTION_UPDATED_REALTIME = 'question_updated_realtime',
  QUESTION_REMOVED_REALTIME = 'question_removed_realtime',
  QUESTIONS_REORDERED_REALTIME = 'questions_reordered_realtime',

  // System events
  COLLABORATION_ERROR = 'collaboration_error',
  ROOM_STATUS_CHANGED = 'room_status_changed',
  CONNECTION_RESTORED = 'connection_restored',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  PERMISSION_DENIED = 'permission_denied',

  // Admin events
  USER_KICKED = 'user_kicked',
  ROOM_LOCKED = 'room_locked',
  ROOM_UNLOCKED = 'room_unlocked',
}

/**
 * Collaboration error codes
 */
export enum CollaborationErrorCode {
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  AUTHORIZATION_FAILED = 'AUTHORIZATION_FAILED',
  WORKSHEET_NOT_FOUND = 'WORKSHEET_NOT_FOUND',
  QUESTION_NOT_FOUND = 'QUESTION_NOT_FOUND',
  LOCK_ACQUISITION_FAILED = 'LOCK_ACQUISITION_FAILED',
  LOCK_NOT_OWNED = 'LOCK_NOT_OWNED',
  CONCURRENT_EDIT_CONFLICT = 'CONCURRENT_EDIT_CONFLICT',
  ROOM_CAPACITY_EXCEEDED = 'ROOM_CAPACITY_EXCEEDED',
  INVALID_OPERATION = 'INVALID_OPERATION',
  REDIS_CONNECTION_FAILED = 'REDIS_CONNECTION_FAILED',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SCHOOL_ISOLATION_VIOLATION = 'SCHOOL_ISOLATION_VIOLATION',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
}

/**
 * User action types for presence tracking
 */
export enum UserAction {
  VIEWING = 'viewing',
  EDITING_QUESTION = 'editing_question',
  ADDING_QUESTION = 'adding_question',
  REORDERING_QUESTIONS = 'reordering_questions',
  IDLE = 'idle',
  TYPING = 'typing',
}

/**
 * Lock types for question locking
 */
export enum LockType {
  OPTIMISTIC = 'optimistic',
  PESSIMISTIC = 'pessimistic',
}

/**
 * Conflict resolution strategies
 */
export enum ConflictResolutionStrategy {
  LAST_WRITER_WINS = 'last_writer_wins',
  MANUAL_MERGE = 'manual_merge',
  AUTO_MERGE = 'auto_merge',
  DISCARD_CHANGES = 'discard_changes',
}

/**
 * Room status types
 */
export enum RoomStatus {
  ACTIVE = 'active',
  LOCKED = 'locked',
  MAINTENANCE = 'maintenance',
  ARCHIVED = 'archived',
}
