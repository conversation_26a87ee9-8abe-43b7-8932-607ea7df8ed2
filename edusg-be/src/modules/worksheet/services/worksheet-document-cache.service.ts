import {forwardRef, Inject, Injectable, Logger} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WorksheetDocument } from '../../mongodb/schemas/worksheet-document.schema';
import { QueryCacheService } from '../../mongodb/services/query-cache.service';
import { PoolMonitoringService } from '../../monitoring/services/pool-monitoring.service';
import { CacheInteractionEvent } from '../../monitoring/interfaces/monitoring-events.interface';
import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';

@Injectable()
export class WorksheetDocumentCacheService {
  private readonly logger = new Logger(WorksheetDocumentCacheService.name);
  private readonly CACHE_TTL_DAYS = 7; // Default TTL in days

  constructor(
    @InjectModel(WorksheetDocument.name)
    private worksheetDocumentModel: Model<WorksheetDocument>,
    @Inject(forwardRef(() => QueryCacheService))
    private queryCacheService: QueryCacheService,
    private readonly poolMonitoringService: PoolMonitoringService,
  ) {}

  /**
   * Generates a cache key for worksheet documents
   * @param topic The worksheet topic
   * @param grade The grade level
   * @param additionalParams Additional parameters to include in the cache key
   * @returns A unique cache key
   */
  generateCacheKey(
    topic: string,
    grade: string,
    additionalParams: Record<string, any> = {},
  ): string {
    const params = {
      topic,
      grade,
      ...additionalParams,
    };

    const combinedString = JSON.stringify(params);
    return crypto.createHash('md5').update(combinedString).digest('hex');
  }

  /**
   * Gets a worksheet document from cache
   * @param topic The worksheet topic
   * @param grade The grade level
   * @param additionalParams Additional parameters to include in the cache key
   * @returns The cached worksheet document or null if not found
   */
  async getFromCache(
    topic: string,
    grade: string,
    additionalParams: Record<string, any> = {},
  ): Promise<WorksheetDocument | null> {
    const startTime = Date.now();
    const cacheKey = this.generateCacheKey(topic, grade, additionalParams);

    // First try to get from QueryCacheService (memory + MongoDB)
    const cachedResult = await this.queryCacheService.getFromCache(cacheKey);

    if (cachedResult) {
      this.logger.debug(`Found worksheet document in QueryCacheService with key: ${cacheKey}`);

      // Emit cache hit event
      await this.emitCacheEvent('get', 'worksheet_document', cacheKey, true, Date.now() - startTime);

      return cachedResult;
    }

    // If not in QueryCacheService, try direct lookup in WorksheetDocument collection
    try {
      // First, try exact match
      let document = await this.worksheetDocumentModel
        .findOne({
          topic,
          grade,
        })
        .sort({ createdAt: -1 });

      // If no exact match, try a more flexible search with partial topic match
      if (!document) {
        this.logger.debug(`No exact match found, trying partial topic match`);
        const topicWords = topic.split(' ').filter(word => word.length > 3);

        if (topicWords.length > 0) {
          // Create a regex pattern to match any of the significant words in the topic
          const topicPattern = topicWords.map(word => `(?=.*${word})`).join('');
          const topicRegex = new RegExp(topicPattern, 'i');

          document = await this.worksheetDocumentModel
            .findOne({
              topic: { $regex: topicRegex },
              grade,
            })
            .sort({ hitCount: -1 }) // Prioritize frequently used documents
            .sort({ createdAt: -1 });

          if (document) {
            this.logger.debug(`Found similar topic match: ${document.topic}`);
          }
        }
      }

      if (document) {
        // Update hit count and last accessed time
        await this.worksheetDocumentModel.updateOne(
          { _id: document._id },
          {
            $inc: { hitCount: 1 },
            updatedAt: new Date()
          }
        );

        // Also save to QueryCacheService for faster access next time
        await this.saveToQueryCache(cacheKey, document);

        return document;
      }
    } catch (error) {
      this.logger.error(`Error retrieving from worksheet document cache: ${error.message}`);
    }

    // Emit cache miss event
    await this.emitCacheEvent('get', 'worksheet_document', cacheKey, false, Date.now() - startTime);

    return null;
  }

  /**
   * Saves a worksheet document to cache
   * @param topic The worksheet topic
   * @param grade The grade level
   * @param documentResult The document result to cache
   * @param worksheetId The ID of the worksheet
   * @param fromCache Whether the document was retrieved from cache
   * @param additionalParams Additional parameters to include in the cache key
   * @returns The saved worksheet document
   */
  async saveToCache(
    topic: string,
    grade: string,
    documentResult: any,
    worksheetId: string,
    fromCache: boolean = false,
    additionalParams: Record<string, any> = {},
  ): Promise<WorksheetDocument> {
    const cacheKey = this.generateCacheKey(topic, grade, additionalParams);

    try {
      // Calculate expiration date
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + this.CACHE_TTL_DAYS);

      // Create or update the worksheet document
      const existingDocument = await this.worksheetDocumentModel.findOne({
        worksheetId,
        topic,
        grade,
      });

      let worksheetDocument: WorksheetDocument;

      if (existingDocument) {
        // Update existing document
        existingDocument.documentResult = documentResult;
        existingDocument.fromCache = fromCache;
        existingDocument.updatedAt = new Date();
        existingDocument.expiresAt = expiresAt;
        existingDocument.hitCount += 1;

        worksheetDocument = await existingDocument.save();
      } else {
        // Create new document
        const newDocument = new this.worksheetDocumentModel({
          worksheetId,
          topic,
          grade,
          documentResult,
          fromCache,
          hitCount: 1,
          expiresAt,
        });

        worksheetDocument = await newDocument.save();
      }

      // Also save to QueryCacheService
      await this.saveToQueryCache(cacheKey, worksheetDocument);

      this.logger.debug(`Saved worksheet document to cache with key: ${cacheKey}`);
      return worksheetDocument;
    } catch (error) {
      this.logger.error(`Error saving to worksheet document cache: ${error.message}`);
      throw error;
    }
  }

  /**
   * Saves a worksheet document to the QueryCacheService
   * @param cacheKey The cache key
   * @param document The worksheet document to cache
   * @private
   */
  private async saveToQueryCache(
    cacheKey: string,
    document: WorksheetDocument,
  ): Promise<void> {
    try {
      // Calculate TTL in minutes for QueryCacheService
      const now = new Date();
      const expiresAt = document.expiresAt || new Date(now.getTime() + this.CACHE_TTL_DAYS * 24 * 60 * 60 * 1000);
      const ttlMinutes = Math.floor((expiresAt.getTime() - now.getTime()) / (60 * 1000));

      // Save to QueryCacheService
      await this.queryCacheService.saveToCache(
        `worksheet_document_${document.worksheetId}`,
        { topic: document.topic, grade: document.grade },
        document,
        ttlMinutes
      );
    } catch (error) {
      this.logger.error(`Error saving to QueryCacheService: ${error.message}`);
    }
  }

  /**
   * Gets a worksheet document by worksheetId
   * @param worksheetId The ID of the worksheet
   * @returns The worksheet document or null if not found
   */
  async getByWorksheetId(worksheetId: string): Promise<WorksheetDocument | null> {
    try {
      // Try to get from QueryCacheService first
      const cacheKey = `worksheet_document_${worksheetId}`;
      const cachedResult = await this.queryCacheService.getFromCache(cacheKey);

      if (cachedResult) {
        this.logger.debug(`Found worksheet document in QueryCacheService for worksheetId: ${worksheetId}`);
        return cachedResult;
      }

      // If not in QueryCacheService, try direct lookup
      const document = await this.worksheetDocumentModel.findOne({ worksheetId });

      if (document) {
        // Update hit count and last accessed time
        await this.worksheetDocumentModel.updateOne(
          { _id: document._id },
          {
            $inc: { hitCount: 1 },
            updatedAt: new Date()
          }
        );

        // Also save to QueryCacheService for faster access next time
        await this.saveToQueryCache(cacheKey, document);

        return document;
      }
    } catch (error) {
      this.logger.error(`Error retrieving document by worksheetId: ${error.message}`);
    }

    return null;
  }

  /**
   * Invalidates a worksheet document in the cache
   * @param worksheetId The ID of the worksheet
   */
  async invalidateCache(worksheetId: string): Promise<void> {
    try {
      // Find the document to get its cache key
      const document = await this.worksheetDocumentModel.findOne({ worksheetId });

      if (document) {
        const cacheKey = this.generateCacheKey(document.topic, document.grade);

        // Remove from QueryCacheService
        await this.queryCacheService.invalidateCache(cacheKey);
        await this.queryCacheService.invalidateCache(`worksheet_document_${worksheetId}`);

        // Remove from WorksheetDocument collection
        await this.worksheetDocumentModel.deleteOne({ worksheetId });

        this.logger.debug(`Invalidated cache for worksheet: ${worksheetId}`);
      }
    } catch (error) {
      this.logger.error(`Error invalidating cache: ${error.message}`);
    }
  }

  /**
   * Clears expired entries from the worksheet document cache
   * @returns Number of expired entries removed
   */
  async clearExpiredEntries(): Promise<number> {
    try {
      const now = new Date();
      const result = await this.worksheetDocumentModel.deleteMany({
        expiresAt: { $lt: now }
      });

      const deletedCount = result.deletedCount || 0;
      this.logger.debug(`Cleared ${deletedCount} expired entries from worksheet document cache`);
      return deletedCount;
    } catch (error) {
      this.logger.error(`Error clearing expired entries: ${error.message}`);
      return 0;
    }
  }

  /**
   * Emit monitoring event for cache interactions
   */
  private async emitCacheEvent(
    operation: 'get' | 'set' | 'delete' | 'clear',
    cacheType: 'question_pool' | 'worksheet_document' | 'query_cache',
    cacheKey: string,
    hit: boolean,
    executionTimeMs: number,
    dataSize?: number,
    ttl?: number
  ): Promise<void> {
    try {
      const event: CacheInteractionEvent = {
        eventId: uuidv4(),
        type: 'cache_interaction',
        timestamp: new Date(),
        operation,
        cacheType,
        cacheKey,
        result: {
          hit,
          executionTimeMs,
          dataSize,
          ttl,
        },
      };

      await this.poolMonitoringService.emitEvent(event);
    } catch (error) {
      // Don't throw error to avoid disrupting main flow
      this.logger.error(`Error emitting cache event: ${error.message}`, error.stack);
    }
  }
}
