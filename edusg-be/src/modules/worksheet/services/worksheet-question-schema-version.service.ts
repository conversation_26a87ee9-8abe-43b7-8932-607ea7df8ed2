import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';

/**
 * Current schema version for worksheet questions
 */
export const CURRENT_SCHEMA_VERSION = 1;

/**
 * Interface for schema migration
 */
export interface ISchemaMigration {
  fromVersion: number;
  toVersion: number;
  description: string;
  migrate: (document: any) => any;
  rollback?: (document: any) => any;
}

/**
 * Service for managing schema versioning and migrations for worksheet questions
 */
@Injectable()
export class WorksheetQuestionSchemaVersionService {
  private readonly logger = new Logger(WorksheetQuestionSchemaVersionService.name);
  private migrations: Map<number, ISchemaMigration> = new Map();

  constructor(
    @InjectModel(WorksheetQuestionDocument.name)
    private worksheetQuestionModel: Model<WorksheetQuestionDocument>,
  ) {
    this.initializeMigrations();
  }

  /**
   * Initialize all available migrations
   */
  private initializeMigrations(): void {
    // Example migration from version 0 to 1 (initial version)
    this.registerMigration({
      fromVersion: 0,
      toVersion: 1,
      description: 'Initial schema version with audit logging and analytics',
      migrate: (document: any) => {
        // Add default audit information if missing
        if (!document.audit) {
          document.audit = {
            createdAt: document.createdAt || new Date(),
            updatedAt: document.updatedAt || new Date(),
            createdBy: document.createdBy || 'system',
            version: 1,
            changeLog: []
          };
        }

        // Add default analytics if missing
        if (!document.analytics) {
          document.analytics = {
            totalAttempts: 0,
            correctAttempts: 0,
            averageScore: 0,
            averageTimeSeconds: 0,
            difficultyRating: 0,
            lastAnalyzedAt: new Date()
          };
        }

        // Set schema version
        document.schemaVersion = 1;

        return document;
      },
      rollback: (document: any) => {
        // Remove audit and analytics fields
        delete document.audit;
        delete document.analytics;
        delete document.schemaVersion;
        return document;
      }
    });

    // Future migrations can be added here
    // Example: Migration from version 1 to 2
    /*
    this.registerMigration({
      fromVersion: 1,
      toVersion: 2,
      description: 'Add new field for enhanced analytics',
      migrate: (document: any) => {
        if (!document.enhancedAnalytics) {
          document.enhancedAnalytics = {
            performanceMetrics: {},
            learningOutcomes: []
          };
        }
        document.schemaVersion = 2;
        return document;
      }
    });
    */

    this.logger.log(`Initialized ${this.migrations.size} schema migrations`);
  }

  /**
   * Register a new migration
   */
  registerMigration(migration: ISchemaMigration): void {
    this.migrations.set(migration.fromVersion, migration);
    this.logger.log(`Registered migration from version ${migration.fromVersion} to ${migration.toVersion}`);
  }

  /**
   * Check if a document needs migration
   */
  needsMigration(document: any): boolean {
    const currentVersion = document.schemaVersion || 0;
    return currentVersion < CURRENT_SCHEMA_VERSION;
  }

  /**
   * Migrate a single document to the current schema version
   */
  migrateDocument(document: any): any {
    let currentVersion = document.schemaVersion || 0;
    let migratedDocument = { ...document };

    this.logger.debug(`Migrating document from version ${currentVersion} to ${CURRENT_SCHEMA_VERSION}`);

    while (currentVersion < CURRENT_SCHEMA_VERSION) {
      const migration = this.migrations.get(currentVersion);
      
      if (!migration) {
        throw new Error(`No migration found from version ${currentVersion} to ${currentVersion + 1}`);
      }

      try {
        migratedDocument = migration.migrate(migratedDocument);
        currentVersion = migration.toVersion;
        
        this.logger.debug(`Applied migration: ${migration.description}`);
      } catch (error) {
        this.logger.error(`Failed to apply migration from version ${migration.fromVersion} to ${migration.toVersion}`, error);
        throw error;
      }
    }

    return migratedDocument;
  }

  /**
   * Migrate all documents in the collection that need migration
   */
  async migrateAllDocuments(): Promise<{ migrated: number; errors: number }> {
    this.logger.log('Starting bulk migration of worksheet question documents...');

    let migrated = 0;
    let errors = 0;
    const batchSize = 100;

    try {
      // Find all documents that need migration
      const documentsToMigrate = await this.worksheetQuestionModel
        .find({
          $or: [
            { schemaVersion: { $lt: CURRENT_SCHEMA_VERSION } },
            { schemaVersion: { $exists: false } }
          ]
        })
        .limit(batchSize);

      this.logger.log(`Found ${documentsToMigrate.length} documents that need migration`);

      for (const document of documentsToMigrate) {
        try {
          const migratedData = this.migrateDocument(document.toObject());
          
          await this.worksheetQuestionModel.updateOne(
            { _id: document._id },
            { $set: migratedData }
          );

          migrated++;
          
          if (migrated % 10 === 0) {
            this.logger.log(`Migrated ${migrated} documents...`);
          }
        } catch (error) {
          this.logger.error(`Failed to migrate document ${document._id}`, error);
          errors++;
        }
      }

      this.logger.log(`Migration completed. Migrated: ${migrated}, Errors: ${errors}`);
      return { migrated, errors };
    } catch (error) {
      this.logger.error('Failed to perform bulk migration', error);
      throw error;
    }
  }

  /**
   * Get schema version statistics
   */
  async getSchemaVersionStats(): Promise<any> {
    try {
      const pipeline = [
        {
          $group: {
            _id: { $ifNull: ['$schemaVersion', 0] },
            count: { $sum: 1 }
          }
        },
        {
          $sort: { _id: 1 as 1 }
        }
      ];

      const results = await this.worksheetQuestionModel.aggregate(pipeline);
      
      const stats = {
        currentVersion: CURRENT_SCHEMA_VERSION,
        versionDistribution: {} as Record<number, number>,
        totalDocuments: 0,
        needsMigration: 0
      };

      for (const result of results) {
        const version = result._id;
        const count = result.count;
        
        stats.versionDistribution[version] = count;
        stats.totalDocuments += count;
        
        if (version < CURRENT_SCHEMA_VERSION) {
          stats.needsMigration += count;
        }
      }

      return stats;
    } catch (error) {
      this.logger.error('Failed to get schema version statistics', error);
      throw error;
    }
  }

  /**
   * Validate document schema version
   */
  validateSchemaVersion(document: any): boolean {
    const version = document.schemaVersion;
    
    if (typeof version !== 'number') {
      return false;
    }

    if (version < 0 || version > CURRENT_SCHEMA_VERSION) {
      return false;
    }

    return true;
  }

  /**
   * Get available migrations
   */
  getAvailableMigrations(): ISchemaMigration[] {
    return Array.from(this.migrations.values());
  }

  /**
   * Rollback a document to a previous schema version
   */
  rollbackDocument(document: any, targetVersion: number): any {
    let currentVersion = document.schemaVersion || CURRENT_SCHEMA_VERSION;
    let rolledBackDocument = { ...document };

    this.logger.debug(`Rolling back document from version ${currentVersion} to ${targetVersion}`);

    while (currentVersion > targetVersion) {
      const migration = this.migrations.get(currentVersion - 1);
      
      if (!migration || !migration.rollback) {
        throw new Error(`No rollback available from version ${currentVersion} to ${currentVersion - 1}`);
      }

      try {
        rolledBackDocument = migration.rollback(rolledBackDocument);
        currentVersion = migration.fromVersion;
        
        this.logger.debug(`Applied rollback: ${migration.description}`);
      } catch (error) {
        this.logger.error(`Failed to rollback from version ${migration.toVersion} to ${migration.fromVersion}`, error);
        throw error;
      }
    }

    return rolledBackDocument;
  }

  /**
   * Check if migration is safe to perform
   */
  async isMigrationSafe(): Promise<{ safe: boolean; reasons: string[] }> {
    const reasons: string[] = [];
    let safe = true;

    try {
      // Check if there are any locked documents
      const lockedCount = await this.worksheetQuestionModel.countDocuments({
        lockedBy: { $exists: true, $ne: null }
      });

      if (lockedCount > 0) {
        safe = false;
        reasons.push(`${lockedCount} documents are currently locked`);
      }

      // Check if there are any documents with unsaved changes
      const unsavedCount = await this.worksheetQuestionModel.countDocuments({
        'questionMetadata.hasUnsavedChanges': true
      });

      if (unsavedCount > 0) {
        safe = false;
        reasons.push(`${unsavedCount} documents have unsaved changes`);
      }

      // Check collection size
      const totalCount = await this.worksheetQuestionModel.countDocuments();
      if (totalCount > 10000) {
        reasons.push(`Large collection size (${totalCount} documents) - migration may take time`);
      }

      return { safe, reasons };
    } catch (error) {
      this.logger.error('Failed to check migration safety', error);
      return { safe: false, reasons: ['Failed to check migration safety'] };
    }
  }
}


