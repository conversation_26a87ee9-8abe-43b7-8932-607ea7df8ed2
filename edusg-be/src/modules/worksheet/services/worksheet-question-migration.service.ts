import { Injectable, Logger } from '@nestjs/common';
import { InjectConnection } from '@nestjs/typeorm';
import { InjectModel } from '@nestjs/mongoose';
import { Connection } from 'typeorm';
import { Model } from 'mongoose';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { WorksheetQuestionIndexingService } from './worksheet-question-indexing.service';
import { WorksheetQuestionSchemaVersionService } from './worksheet-question-schema-version.service';

/**
 * Interface for migration status
 */
export interface IMigrationStatus {
  name: string;
  executed: boolean;
  executedAt?: Date;
  success?: boolean;
  error?: string;
}

/**
 * Interface for migration result
 */
export interface IMigrationResult {
  success: boolean;
  message: string;
  postgresqlMigrations: IMigrationStatus[];
  mongodbMigrations: IMigrationStatus[];
  errors: string[];
}

/**
 * Service for managing database migrations for worksheet questions
 * Handles both PostgreSQL and MongoDB migrations
 */
@Injectable()
export class WorksheetQuestionMigrationService {
  private readonly logger = new Logger(WorksheetQuestionMigrationService.name);

  constructor(
    @InjectConnection()
    private connection: Connection,
    @InjectModel(WorksheetQuestionDocument.name)
    private worksheetQuestionModel: Model<WorksheetQuestionDocument>,
    private indexingService: WorksheetQuestionIndexingService,
    private schemaVersionService: WorksheetQuestionSchemaVersionService,
  ) {}

  /**
   * Run all pending migrations
   */
  async runMigrations(): Promise<IMigrationResult> {
    this.logger.log('Starting worksheet question migrations...');

    const result: IMigrationResult = {
      success: true,
      message: '',
      postgresqlMigrations: [],
      mongodbMigrations: [],
      errors: []
    };

    try {
      // Check if migrations are safe to run
      const safetyCheck = await this.checkMigrationSafety();
      if (!safetyCheck.safe) {
        result.success = false;
        result.message = 'Migrations are not safe to run';
        result.errors = safetyCheck.reasons;
        return result;
      }

      // Run PostgreSQL migrations
      await this.runPostgreSQLMigrations(result);

      // Run MongoDB migrations
      await this.runMongoDBMigrations(result);

      // Verify migrations
      await this.verifyMigrations(result);

      if (result.success) {
        result.message = 'All migrations completed successfully';
        this.logger.log('✅ All worksheet question migrations completed successfully');
      } else {
        result.message = 'Some migrations failed';
        this.logger.error('❌ Some worksheet question migrations failed');
      }

      return result;
    } catch (error) {
      this.logger.error('Failed to run migrations', error);
      result.success = false;
      result.message = `Migration failed: ${error.message}`;
      result.errors.push(error.message);
      return result;
    }
  }

  /**
   * Check if migrations are safe to run
   */
  private async checkMigrationSafety(): Promise<{ safe: boolean; reasons: string[] }> {
    const reasons: string[] = [];
    let safe = true;

    try {
      // Check PostgreSQL connection
      if (!this.connection.isConnected) {
        safe = false;
        reasons.push('PostgreSQL connection is not established');
      }

      // Check MongoDB connection
      try {
        // Use a simple query to test MongoDB connection
        await this.worksheetQuestionModel.findOne().limit(1).exec();
      } catch (error) {
        safe = false;
        reasons.push('MongoDB connection is not established');
      }

      // Check for active transactions
      const queryRunner = this.connection.createQueryRunner();
      try {
        const activeTransactions = await queryRunner.query(
          "SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active' AND query LIKE '%worksheet%'"
        );
        if (activeTransactions[0]?.count > 0) {
          reasons.push('Active transactions detected on worksheet-related tables');
        }
      } finally {
        await queryRunner.release();
      }

      // Check schema version safety
      const schemaVersionSafety = await this.schemaVersionService.isMigrationSafe();
      if (!schemaVersionSafety.safe) {
        safe = false;
        reasons.push(...schemaVersionSafety.reasons);
      }

      return { safe, reasons };
    } catch (error) {
      this.logger.error('Failed to check migration safety', error);
      return { safe: false, reasons: ['Failed to check migration safety'] };
    }
  }

  /**
   * Run PostgreSQL migrations
   */
  private async runPostgreSQLMigrations(result: IMigrationResult): Promise<void> {
    try {
      this.logger.log('Running PostgreSQL migrations...');

      // Check if the migration has already been run
      const queryRunner = this.connection.createQueryRunner();
      
      try {
        // Check if the columns already exist
        const columnsExist = await queryRunner.query(`
          SELECT column_name 
          FROM information_schema.columns 
          WHERE table_name = 'worksheets' 
          AND column_name IN ('questions', 'totalQuestions', 'lastModifiedBy', 'maxQuestions', 'questionIds', 'questionMetadata')
        `);

        if (columnsExist.length === 6) {
          result.postgresqlMigrations.push({
            name: 'AddQuestionManagementToWorksheets',
            executed: true,
            executedAt: new Date(),
            success: true
          });
          this.logger.log('PostgreSQL migration already applied');
          return;
        }

        // Run the migration manually (since we can't easily run TypeORM migrations programmatically)
        await this.runPostgreSQLMigrationQueries(queryRunner);

        result.postgresqlMigrations.push({
          name: 'AddQuestionManagementToWorksheets',
          executed: true,
          executedAt: new Date(),
          success: true
        });

        this.logger.log('✅ PostgreSQL migrations completed');
      } finally {
        await queryRunner.release();
      }
    } catch (error) {
      this.logger.error('PostgreSQL migration failed', error);
      result.success = false;
      result.errors.push(`PostgreSQL migration failed: ${error.message}`);
      result.postgresqlMigrations.push({
        name: 'AddQuestionManagementToWorksheets',
        executed: false,
        success: false,
        error: error.message
      });
    }
  }

  /**
   * Run the actual PostgreSQL migration queries
   */
  private async runPostgreSQLMigrationQueries(queryRunner: any): Promise<void> {
    // Add columns
    await queryRunner.query(`
      ALTER TABLE "worksheets" 
      ADD COLUMN IF NOT EXISTS "questions" jsonb,
      ADD COLUMN IF NOT EXISTS "totalQuestions" integer DEFAULT 0,
      ADD COLUMN IF NOT EXISTS "lastModifiedBy" varchar,
      ADD COLUMN IF NOT EXISTS "maxQuestions" integer DEFAULT 100,
      ADD COLUMN IF NOT EXISTS "questionIds" jsonb,
      ADD COLUMN IF NOT EXISTS "questionMetadata" jsonb
    `);

    // Create indexes
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_WORKSHEET_SCHOOL_UPDATED" 
      ON "worksheets" ("schoolId", "updatedAt")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_WORKSHEET_SCHOOL_TOTAL_QUESTIONS" 
      ON "worksheets" ("schoolId", "totalQuestions")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_WORKSHEET_LAST_MODIFIED" 
      ON "worksheets" ("lastModifiedBy", "updatedAt")
    `);

    // Update existing records
    await queryRunner.query(`
      UPDATE "worksheets" 
      SET 
        "totalQuestions" = COALESCE("totalQuestions", 0),
        "maxQuestions" = COALESCE("maxQuestions", 100),
        "questions" = COALESCE("questions", '[]'::jsonb),
        "questionIds" = COALESCE("questionIds", '[]'::jsonb),
        "questionMetadata" = COALESCE("questionMetadata", '{
          "questionVersion": 1,
          "hasUnsavedChanges": false,
          "collaborators": [],
          "lockStatus": {"isLocked": false}
        }'::jsonb)
      WHERE "questions" IS NULL OR "questionIds" IS NULL OR "questionMetadata" IS NULL
    `);
  }

  /**
   * Run MongoDB migrations
   */
  private async runMongoDBMigrations(result: IMigrationResult): Promise<void> {
    try {
      this.logger.log('Running MongoDB migrations...');

      // Check if collection exists by trying to get collection info
      const collections = await this.worksheetQuestionModel.db.listCollections();
      const worksheetQuestionsCollection = collections.find(col => col.name === 'worksheet_questions');

      if (worksheetQuestionsCollection) {
        result.mongodbMigrations.push({
          name: 'CreateWorksheetQuestionsCollection',
          executed: true,
          executedAt: new Date(),
          success: true
        });
        this.logger.log('MongoDB collection already exists');
      } else {
        // Create collection (it will be created automatically when first document is inserted)
        // But we need to ensure indexes are created
        await this.indexingService.initializeIndexes();

        result.mongodbMigrations.push({
          name: 'CreateWorksheetQuestionsCollection',
          executed: true,
          executedAt: new Date(),
          success: true
        });
      }

      // Run schema version migrations
      const migrationResult = await this.schemaVersionService.migrateAllDocuments();
      
      if (migrationResult.errors > 0) {
        result.errors.push(`Schema version migration had ${migrationResult.errors} errors`);
      }

      result.mongodbMigrations.push({
        name: 'SchemaVersionMigration',
        executed: true,
        executedAt: new Date(),
        success: migrationResult.errors === 0
      });

      this.logger.log('✅ MongoDB migrations completed');
    } catch (error) {
      this.logger.error('MongoDB migration failed', error);
      result.success = false;
      result.errors.push(`MongoDB migration failed: ${error.message}`);
      result.mongodbMigrations.push({
        name: 'CreateWorksheetQuestionsCollection',
        executed: false,
        success: false,
        error: error.message
      });
    }
  }

  /**
   * Verify that migrations were successful
   */
  private async verifyMigrations(result: IMigrationResult): Promise<void> {
    try {
      this.logger.log('Verifying migrations...');

      // Verify PostgreSQL changes
      const queryRunner = this.connection.createQueryRunner();
      try {
        const columns = await queryRunner.query(`
          SELECT column_name, data_type, is_nullable, column_default
          FROM information_schema.columns 
          WHERE table_name = 'worksheets' 
          AND column_name IN ('questions', 'totalQuestions', 'lastModifiedBy', 'maxQuestions', 'questionIds', 'questionMetadata')
          ORDER BY column_name
        `);

        if (columns.length !== 6) {
          result.errors.push('PostgreSQL migration verification failed: Not all columns were created');
          result.success = false;
        }
      } finally {
        await queryRunner.release();
      }

      // Verify MongoDB changes
      const indexInfo = await this.indexingService.getIndexInfo();
      if (indexInfo.length < 10) { // We expect at least 10 indexes including _id
        result.errors.push('MongoDB migration verification failed: Not all indexes were created');
        result.success = false;
      }

      // Verify schema version
      const schemaStats = await this.schemaVersionService.getSchemaVersionStats();
      if (schemaStats.needsMigration > 0) {
        result.errors.push(`Schema version verification failed: ${schemaStats.needsMigration} documents still need migration`);
      }

      this.logger.log('✅ Migration verification completed');
    } catch (error) {
      this.logger.error('Migration verification failed', error);
      result.errors.push(`Migration verification failed: ${error.message}`);
      result.success = false;
    }
  }

  /**
   * Get migration status
   */
  async getMigrationStatus(): Promise<any> {
    try {
      const status = {
        postgresql: {
          connected: this.connection.isConnected,
          migrationsApplied: false,
          lastMigration: null
        },
        mongodb: {
          connected: false,
          collectionExists: false,
          indexCount: 0,
          schemaVersionStats: null
        }
      };

      // Check PostgreSQL status
      if (status.postgresql.connected) {
        const queryRunner = this.connection.createQueryRunner();
        try {
          const columns = await queryRunner.query(`
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'worksheets' 
            AND column_name IN ('questions', 'totalQuestions', 'lastModifiedBy', 'maxQuestions', 'questionIds', 'questionMetadata')
          `);
          status.postgresql.migrationsApplied = columns.length === 6;
        } finally {
          await queryRunner.release();
        }
      }

      // Check MongoDB status
      try {
        // Use a simple query to test MongoDB connection
        await this.worksheetQuestionModel.findOne().limit(1).exec();
        status.mongodb.connected = true;

        const collections = await this.worksheetQuestionModel.db.listCollections();
        const worksheetQuestionsCollection = collections.find(col => col.name === 'worksheet_questions');
        status.mongodb.collectionExists = !!worksheetQuestionsCollection;

        if (status.mongodb.collectionExists) {
          const indexInfo = await this.indexingService.getIndexInfo();
          status.mongodb.indexCount = indexInfo.length;

          status.mongodb.schemaVersionStats = await this.schemaVersionService.getSchemaVersionStats();
        }
      } catch (error) {
        this.logger.warn('Failed to check MongoDB status', error);
      }

      return status;
    } catch (error) {
      this.logger.error('Failed to get migration status', error);
      throw error;
    }
  }
}
