import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';

/**
 * Service for managing database indexes for worksheet questions
 * This service ensures optimal query performance for common operations
 */
@Injectable()
export class WorksheetQuestionIndexingService {
  private readonly logger = new Logger(WorksheetQuestionIndexingService.name);

  constructor(
    @InjectModel(WorksheetQuestionDocument.name)
    private worksheetQuestionModel: Model<WorksheetQuestionDocument>,
  ) {}

  /**
   * Initialize all required indexes for worksheet questions
   * This method should be called during application startup
   */
  async initializeIndexes(): Promise<void> {
    try {
      this.logger.log('Initializing worksheet question indexes...');

      // Core indexes for worksheet question operations
      await this.createCoreIndexes();
      
      // Performance indexes for common queries
      await this.createPerformanceIndexes();
      
      // Search indexes for content search
      await this.createSearchIndexes();
      
      // Audit and analytics indexes
      await this.createAuditIndexes();

      this.logger.log('All worksheet question indexes initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize worksheet question indexes', error);
      throw error;
    }
  }

  /**
   * Create core indexes required for basic operations
   */
  private async createCoreIndexes(): Promise<void> {
    const collection = this.worksheetQuestionModel.collection;

    // Unique compound index for worksheet + position
    await collection.createIndex(
      { worksheetId: 1, position: 1 },
      { 
        unique: true, 
        name: 'worksheet_position_unique',
        background: true
      }
    );

    // Index for worksheet + status queries
    await collection.createIndex(
      { worksheetId: 1, status: 1 },
      { 
        name: 'worksheet_status',
        background: true
      }
    );

    // Index for school-based data isolation
    await collection.createIndex(
      { schoolId: 1, worksheetId: 1 },
      { 
        name: 'school_worksheet',
        background: true,
        sparse: true // schoolId can be null for some questions
      }
    );

    this.logger.log('Core indexes created successfully');
  }

  /**
   * Create performance indexes for common query patterns
   */
  private async createPerformanceIndexes(): Promise<void> {
    const collection = this.worksheetQuestionModel.collection;

    // Compound index for subject-based queries
    await collection.createIndex(
      { schoolId: 1, subject: 1, grade: 1 },
      { 
        name: 'school_subject_grade',
        background: true,
        sparse: true
      }
    );

    // Index for question type and difficulty filtering
    await collection.createIndex(
      { type: 1, difficulty: 1 },
      { 
        name: 'type_difficulty',
        background: true,
        sparse: true
      }
    );

    // Index for topic-based queries
    await collection.createIndex(
      { topic: 1, childSubject: 1 },
      { 
        name: 'topic_child_subject',
        background: true,
        sparse: true
      }
    );

    // Index for question ID lookups
    await collection.createIndex(
      { questionId: 1 },
      { 
        name: 'question_id',
        background: true
      }
    );

    // Index for position-based sorting and range queries
    await collection.createIndex(
      { worksheetId: 1, position: 1, status: 1 },
      { 
        name: 'worksheet_position_status',
        background: true
      }
    );

    this.logger.log('Performance indexes created successfully');
  }

  /**
   * Create search indexes for content and metadata search
   */
  private async createSearchIndexes(): Promise<void> {
    const collection = this.worksheetQuestionModel.collection;

    // Text search index for content, explanation, and metadata
    await collection.createIndex(
      {
        content: 'text',
        explain: 'text',
        'metadata.tags': 'text',
        'metadata.keywords': 'text'
      },
      {
        name: 'content_search',
        background: true,
        weights: {
          content: 10,
          explain: 5,
          'metadata.tags': 3,
          'metadata.keywords': 2
        }
      }
    );

    // Index for tag-based filtering
    await collection.createIndex(
      { 'metadata.tags': 1 },
      { 
        name: 'metadata_tags',
        background: true,
        sparse: true
      }
    );

    this.logger.log('Search indexes created successfully');
  }

  /**
   * Create audit and analytics indexes
   */
  private async createAuditIndexes(): Promise<void> {
    const collection = this.worksheetQuestionModel.collection;

    // Index for audit queries (created/updated timestamps)
    await collection.createIndex(
      { 'audit.createdAt': -1 },
      { 
        name: 'audit_created_desc',
        background: true
      }
    );

    await collection.createIndex(
      { 'audit.updatedAt': -1 },
      { 
        name: 'audit_updated_desc',
        background: true
      }
    );

    // Index for user-based audit queries
    await collection.createIndex(
      { 'audit.createdBy': 1, 'audit.createdAt': -1 },
      { 
        name: 'audit_created_by',
        background: true
      }
    );

    await collection.createIndex(
      { 'audit.updatedBy': 1, 'audit.updatedAt': -1 },
      { 
        name: 'audit_updated_by',
        background: true,
        sparse: true
      }
    );

    // Index for analytics queries
    await collection.createIndex(
      { 'analytics.totalAttempts': -1 },
      { 
        name: 'analytics_attempts',
        background: true,
        sparse: true
      }
    );

    // Index for lock management
    await collection.createIndex(
      { lockedBy: 1, lockedAt: 1 },
      { 
        name: 'lock_management',
        background: true,
        sparse: true
      }
    );

    // TTL index for automatic expiration
    await collection.createIndex(
      { expiresAt: 1 },
      { 
        name: 'ttl_expiration',
        expireAfterSeconds: 0,
        background: true,
        sparse: true
      }
    );

    this.logger.log('Audit indexes created successfully');
  }

  /**
   * Get information about all indexes on the worksheet questions collection
   */
  async getIndexInfo(): Promise<any[]> {
    try {
      const collection = this.worksheetQuestionModel.collection;
      const indexes = await collection.listIndexes().toArray();
      
      this.logger.log(`Found ${indexes.length} indexes on worksheet questions collection`);
      return indexes;
    } catch (error) {
      this.logger.error('Failed to get index information', error);
      throw error;
    }
  }

  /**
   * Analyze query performance for common operations
   */
  async analyzeQueryPerformance(): Promise<any> {
    try {
      const collection = this.worksheetQuestionModel.collection;

      // Sample queries to analyze
      const queries = [
        // Get all questions for a worksheet
        { worksheetId: 'sample-worksheet-id' },

        // Get questions by subject and grade
        { schoolId: 'sample-school-id', subject: 'Mathematics', grade: '6' },

        // Search questions by content
        { $text: { $search: 'algebra' } },

        // Get questions by type and difficulty
        { type: 'multiple_choice', difficulty: 'medium' },

        // Get recent questions
        { 'audit.createdAt': { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } }
      ];

      const analysisResults: Array<{
        query: any;
        executionStats: any;
        indexUsed: string;
      }> = [];

      for (const query of queries) {
        try {
          const explanation = await collection.find(query).explain('executionStats');
          analysisResults.push({
            query,
            executionStats: explanation.executionStats,
            indexUsed: explanation.executionStats.executionStages?.indexName || 'COLLSCAN'
          });
        } catch (error) {
          this.logger.warn(`Failed to analyze query: ${JSON.stringify(query)}`, error);
        }
      }

      return analysisResults;
    } catch (error) {
      this.logger.error('Failed to analyze query performance', error);
      throw error;
    }
  }

  /**
   * Drop all indexes (use with caution - for maintenance purposes only)
   */
  async dropAllIndexes(): Promise<void> {
    try {
      const collection = this.worksheetQuestionModel.collection;
      await collection.dropIndexes();
      this.logger.warn('All indexes dropped from worksheet questions collection');
    } catch (error) {
      this.logger.error('Failed to drop indexes', error);
      throw error;
    }
  }

  /**
   * Rebuild all indexes
   */
  async rebuildIndexes(): Promise<void> {
    try {
      this.logger.log('Rebuilding all worksheet question indexes...');
      
      // Drop existing indexes (except _id)
      const collection = this.worksheetQuestionModel.collection;
      const indexes = await collection.listIndexes().toArray();
      
      for (const index of indexes) {
        if (index.name !== '_id_') {
          try {
            await collection.dropIndex(index.name);
          } catch (error) {
            this.logger.warn(`Failed to drop index ${index.name}`, error);
          }
        }
      }

      // Recreate all indexes
      await this.initializeIndexes();
      
      this.logger.log('All indexes rebuilt successfully');
    } catch (error) {
      this.logger.error('Failed to rebuild indexes', error);
      throw error;
    }
  }
}
