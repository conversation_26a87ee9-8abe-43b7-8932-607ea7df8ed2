import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EUserRole } from '../../user/dto/create-user.dto';
import { UserContext } from '../../../shared/interfaces/user-context.interface';

/**
 * Service for managing worksheet question throttling configuration and monitoring
 */
@Injectable()
export class WorksheetQuestionThrottlerService {
  private readonly logger = new Logger(WorksheetQuestionThrottlerService.name);

  /**
   * Default role-based rate limits (requests per hour)
   */
  private readonly DEFAULT_ROLE_LIMITS = {
    [EUserRole.ADMIN]: 1000,
    [EUserRole.SCHOOL_MANAGER]: 500,
    [EUserRole.TEACHER]: 200,
    [EUserRole.INDEPENDENT_TEACHER]: 100,
  };

  /**
   * Configurable rate limits from environment
   */
  private roleLimits: Record<string, number>;

  constructor(private readonly configService: ConfigService) {
    this.initializeRateLimits();
  }

  /**
   * Initialize rate limits from configuration
   */
  private initializeRateLimits(): void {
    this.roleLimits = {
      [EUserRole.ADMIN]: this.configService.get<number>('WORKSHEET_RATE_LIMIT_ADMIN', this.DEFAULT_ROLE_LIMITS[EUserRole.ADMIN]),
      [EUserRole.SCHOOL_MANAGER]: this.configService.get<number>('WORKSHEET_RATE_LIMIT_SCHOOL_MANAGER', this.DEFAULT_ROLE_LIMITS[EUserRole.SCHOOL_MANAGER]),
      [EUserRole.TEACHER]: this.configService.get<number>('WORKSHEET_RATE_LIMIT_TEACHER', this.DEFAULT_ROLE_LIMITS[EUserRole.TEACHER]),
      [EUserRole.INDEPENDENT_TEACHER]: this.configService.get<number>('WORKSHEET_RATE_LIMIT_INDEPENDENT_TEACHER', this.DEFAULT_ROLE_LIMITS[EUserRole.INDEPENDENT_TEACHER]),
    };

    this.logger.log('Worksheet question rate limits initialized:', this.roleLimits);
  }

  /**
   * Get rate limit for a specific user role
   */
  getRateLimitForRole(role: EUserRole): number {
    return this.roleLimits[role] || 50; // Default fallback
  }

  /**
   * Get rate limit for a user
   */
  getRateLimitForUser(user: UserContext): number {
    if (!user?.role) {
      return 50; // Default for unauthenticated users
    }
    return this.getRateLimitForRole(user.role);
  }

  /**
   * Check if a user is approaching their rate limit
   * @param user The user context
   * @param currentUsage Current number of requests in the time window
   * @returns true if user is approaching limit (>80% usage)
   */
  isApproachingLimit(user: UserContext, currentUsage: number): boolean {
    const limit = this.getRateLimitForUser(user);
    return currentUsage >= (limit * 0.8);
  }

  /**
   * Get remaining requests for a user
   */
  getRemainingRequests(user: UserContext, currentUsage: number): number {
    const limit = this.getRateLimitForUser(user);
    return Math.max(0, limit - currentUsage);
  }

  /**
   * Log rate limit violation
   */
  logRateLimitViolation(user: UserContext, endpoint: string, currentUsage: number): void {
    const limit = this.getRateLimitForUser(user);
    this.logger.warn(`Rate limit exceeded for user ${user.sub} (${user.role}) on ${endpoint}. Usage: ${currentUsage}/${limit}`);
  }

  /**
   * Log rate limit warning (approaching limit)
   */
  logRateLimitWarning(user: UserContext, endpoint: string, currentUsage: number): void {
    const limit = this.getRateLimitForUser(user);
    this.logger.warn(`User ${user.sub} (${user.role}) approaching rate limit on ${endpoint}. Usage: ${currentUsage}/${limit}`);
  }

  /**
   * Get all configured rate limits
   */
  getAllRateLimits(): Record<string, number> {
    return { ...this.roleLimits };
  }

  /**
   * Update rate limit for a specific role (runtime configuration)
   */
  updateRateLimitForRole(role: EUserRole, newLimit: number): void {
    this.roleLimits[role] = newLimit;
    this.logger.log(`Updated rate limit for role ${role} to ${newLimit}`);
  }

  /**
   * Reset rate limits to defaults
   */
  resetToDefaults(): void {
    this.roleLimits = { ...this.DEFAULT_ROLE_LIMITS };
    this.logger.log('Rate limits reset to defaults');
  }
}
