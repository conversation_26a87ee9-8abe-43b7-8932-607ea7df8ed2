import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue, Job } from 'bullmq';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { WorksheetQuestionMetricsService } from './worksheet-question-metrics.service';
import { WorksheetQuestionCollaborationGateway } from '../gateways/worksheet-question-collaboration.gateway';
import { IExerciseQuestion } from '../../../shared/interfaces/exercise-question.interface';
import { EUserRole } from '../../user/dto/create-user.dto';

export interface BulkOperationJob {
  type: 'bulk_add' | 'bulk_update' | 'bulk_delete' | 'bulk_reorder' | 'worksheet_export' | 'worksheet_import';
  worksheetId: string;
  userId: string;
  userRole: EUserRole;
  schoolId?: string;
  data: any;
  options?: {
    batchSize?: number;
    priority?: number;
    delay?: number;
    attempts?: number;
  };
}

export interface JobProgress {
  jobId: string;
  type: string;
  status: 'queued' | 'active' | 'completed' | 'failed' | 'delayed';
  progress: number;
  total: number;
  currentItem?: string;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  result?: any;
}

/**
 * Background processing service for computationally expensive worksheet question operations
 * Uses Bull queues for async processing with progress tracking and retry mechanisms
 */
@Injectable()
export class WorksheetQuestionBackgroundService {
  private readonly logger = new Logger(WorksheetQuestionBackgroundService.name);

  constructor(
    @InjectQueue('worksheet-questions') private worksheetQueue: Queue,
    @InjectModel(WorksheetQuestionDocument.name)
    private worksheetQuestionModel: Model<WorksheetQuestionDocument>,
    private readonly metricsService: WorksheetQuestionMetricsService,
    private readonly collaborationGateway: WorksheetQuestionCollaborationGateway
  ) {}

  /**
   * Queue bulk add operation for background processing
   */
  async queueBulkAdd(
    worksheetId: string,
    questions: IExerciseQuestion[],
    userId: string,
    userRole: EUserRole,
    schoolId?: string
  ): Promise<string> {
    const jobData: BulkOperationJob = {
      type: 'bulk_add',
      worksheetId,
      userId,
      userRole,
      schoolId,
      data: { questions },
      options: {
        batchSize: 10,
        priority: this.getPriorityByRole(userRole),
        attempts: 3
      }
    };

    const job = await this.worksheetQueue.add('bulk-operation', jobData, {
      priority: jobData.options?.priority || 1,
      attempts: jobData.options?.attempts || 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 50,
      removeOnFail: 20
    });

    this.logger.log(`Queued bulk add operation for worksheet ${worksheetId} with ${questions.length} questions (Job ID: ${job.id})`);
    
    // Track the operation
    this.metricsService.updateActiveOperations('bulk_add', 1);

    return job.id!;
  }

  /**
   * Queue bulk update operation for background processing
   */
  async queueBulkUpdate(
    worksheetId: string,
    updates: Array<{ questionId: string; updates: Partial<IExerciseQuestion> }>,
    userId: string,
    userRole: EUserRole,
    schoolId?: string
  ): Promise<string> {
    const jobData: BulkOperationJob = {
      type: 'bulk_update',
      worksheetId,
      userId,
      userRole,
      schoolId,
      data: { updates },
      options: {
        batchSize: 5,
        priority: this.getPriorityByRole(userRole),
        attempts: 3
      }
    };

    const job = await this.worksheetQueue.add('bulk-operation', jobData, {
      priority: jobData.options?.priority || 1,
      attempts: jobData.options?.attempts || 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      }
    });

    this.logger.log(`Queued bulk update operation for worksheet ${worksheetId} with ${updates.length} updates (Job ID: ${job.id})`);
    
    this.metricsService.updateActiveOperations('bulk_update', 1);

    return job.id!;
  }

  /**
   * Queue bulk delete operation for background processing
   */
  async queueBulkDelete(
    worksheetId: string,
    questionIds: string[],
    userId: string,
    userRole: EUserRole,
    schoolId?: string
  ): Promise<string> {
    const jobData: BulkOperationJob = {
      type: 'bulk_delete',
      worksheetId,
      userId,
      userRole,
      schoolId,
      data: { questionIds },
      options: {
        batchSize: 10,
        priority: this.getPriorityByRole(userRole),
        attempts: 2
      }
    };

    const job = await this.worksheetQueue.add('bulk-operation', jobData, {
      priority: jobData.options?.priority || 1,
      attempts: jobData.options?.attempts || 2,
      backoff: {
        type: 'exponential',
        delay: 1000,
      }
    });

    this.logger.log(`Queued bulk delete operation for worksheet ${worksheetId} with ${questionIds.length} questions (Job ID: ${job.id})`);
    
    this.metricsService.updateActiveOperations('bulk_delete', 1);

    return job.id!;
  }

  /**
   * Queue bulk reorder operation for background processing
   */
  async queueBulkReorder(
    worksheetId: string,
    reorderMap: Array<{ questionId: string; newPosition: number }>,
    userId: string,
    userRole: EUserRole,
    schoolId?: string
  ): Promise<string> {
    const jobData: BulkOperationJob = {
      type: 'bulk_reorder',
      worksheetId,
      userId,
      userRole,
      schoolId,
      data: { reorderMap },
      options: {
        batchSize: 20,
        priority: this.getPriorityByRole(userRole),
        attempts: 2
      }
    };

    const job = await this.worksheetQueue.add('bulk-operation', jobData, {
      priority: jobData.options?.priority || 1,
      attempts: jobData.options?.attempts || 2,
      backoff: {
        type: 'exponential',
        delay: 1000,
      }
    });

    this.logger.log(`Queued bulk reorder operation for worksheet ${worksheetId} with ${reorderMap.length} items (Job ID: ${job.id})`);
    
    this.metricsService.updateActiveOperations('bulk_reorder', 1);

    return job.id!;
  }

  /**
   * Queue worksheet export operation for background processing
   */
  async queueWorksheetExport(
    worksheetId: string,
    exportFormat: 'pdf' | 'docx' | 'json' | 'csv',
    options: any,
    userId: string,
    userRole: EUserRole,
    schoolId?: string
  ): Promise<string> {
    const jobData: BulkOperationJob = {
      type: 'worksheet_export',
      worksheetId,
      userId,
      userRole,
      schoolId,
      data: { exportFormat, options },
      options: {
        priority: this.getPriorityByRole(userRole),
        attempts: 2
      }
    };

    const job = await this.worksheetQueue.add('bulk-operation', jobData, {
      priority: jobData.options?.priority || 1,
      attempts: jobData.options?.attempts || 2,
      backoff: {
        type: 'exponential',
        delay: 3000,
      }
    });

    this.logger.log(`Queued worksheet export for ${worksheetId} in ${exportFormat} format (Job ID: ${job.id})`);
    
    this.metricsService.updateActiveOperations('worksheet_export', 1);

    return job.id!;
  }

  /**
   * Get job status and progress
   */
  async getJobProgress(jobId: string): Promise<JobProgress | null> {
    try {
      const job = await this.worksheetQueue.getJob(jobId);
      if (!job) {
        return null;
      }

      const state = await job.getState();
      const progress = job.progress as any;

      return {
        jobId: job.id!,
        type: job.data.type,
        status: state as any,
        progress: progress?.completed || 0,
        total: progress?.total || 0,
        currentItem: progress?.currentItem,
        startedAt: job.processedOn ? new Date(job.processedOn) : undefined,
        completedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
        error: job.failedReason,
        result: job.returnvalue
      };
    } catch (error) {
      this.logger.error(`Error getting job progress for ${jobId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get all active jobs for a user
   */
  async getUserActiveJobs(userId: string): Promise<JobProgress[]> {
    try {
      const [waiting, active, delayed] = await Promise.all([
        this.worksheetQueue.getWaiting(),
        this.worksheetQueue.getActive(),
        this.worksheetQueue.getDelayed()
      ]);

      const allJobs = [...waiting, ...active, ...delayed];
      const userJobs = allJobs.filter(job => job.data.userId === userId);

      const jobProgresses = await Promise.all(
        userJobs.map(async (job) => {
          const state = await job.getState();
          const progress = job.progress as any;

          return {
            jobId: job.id!,
            type: job.data.type,
            status: state as any,
            progress: progress?.completed || 0,
            total: progress?.total || 0,
            currentItem: progress?.currentItem,
            startedAt: job.processedOn ? new Date(job.processedOn) : undefined,
            completedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
            error: job.failedReason,
            result: job.returnvalue
          };
        })
      );

      return jobProgresses;
    } catch (error) {
      this.logger.error(`Error getting user active jobs for ${userId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Cancel a job
   */
  async cancelJob(jobId: string, userId: string): Promise<boolean> {
    try {
      const job = await this.worksheetQueue.getJob(jobId);
      if (!job) {
        return false;
      }

      // Verify user owns the job
      if (job.data.userId !== userId) {
        throw new Error('Unauthorized to cancel this job');
      }

      await job.remove();
      this.logger.log(`Job ${jobId} cancelled by user ${userId}`);
      
      // Update metrics
      this.metricsService.updateActiveOperations(job.data.type, -1);

      return true;
    } catch (error) {
      this.logger.error(`Error cancelling job ${jobId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStatistics(): Promise<any> {
    try {
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        this.worksheetQueue.getWaiting(),
        this.worksheetQueue.getActive(),
        this.worksheetQueue.getCompleted(),
        this.worksheetQueue.getFailed(),
        this.worksheetQueue.getDelayed()
      ]);

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        total: waiting.length + active.length + completed.length + failed.length + delayed.length,
        queueName: this.worksheetQueue.name,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`Error getting queue statistics: ${error.message}`);
      return { error: 'Failed to get queue statistics' };
    }
  }

  /**
   * Clean up completed and failed jobs
   */
  async cleanupJobs(maxAge: number = 24 * 60 * 60 * 1000): Promise<void> {
    try {
      const cutoff = Date.now() - maxAge;
      
      await Promise.all([
        this.worksheetQueue.clean(cutoff, 100, 'completed'),
        this.worksheetQueue.clean(cutoff, 50, 'failed')
      ]);

      this.logger.log(`Cleaned up old jobs older than ${maxAge}ms`);
    } catch (error) {
      this.logger.error(`Error cleaning up jobs: ${error.message}`);
    }
  }

  /**
   * Get priority based on user role
   */
  private getPriorityByRole(role: EUserRole): number {
    switch (role) {
      case EUserRole.ADMIN:
        return 10; // Highest priority
      case EUserRole.SCHOOL_MANAGER:
        return 8;
      case EUserRole.TEACHER:
        return 5;
      case EUserRole.INDEPENDENT_TEACHER:
        return 3;
      default:
        return 1; // Lowest priority
    }
  }

  /**
   * Notify users about job completion via WebSocket
   */
  async notifyJobCompletion(
    jobId: string,
    userId: string,
    worksheetId: string,
    type: string,
    success: boolean,
    result?: any,
    error?: string
  ): Promise<void> {
    try {
      const notification = {
        jobId,
        type,
        worksheetId,
        success,
        result,
        error,
        completedAt: new Date().toISOString()
      };

      await this.collaborationGateway.notifyJobCompletion(userId, notification);
      
      this.logger.debug(`Notified user ${userId} about job ${jobId} completion`);
    } catch (error) {
      this.logger.error(`Error notifying job completion: ${error.message}`);
    }
  }
}
