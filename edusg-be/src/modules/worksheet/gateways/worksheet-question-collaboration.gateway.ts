import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
  WsException
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Injectable, Logger, UseGuards } from '@nestjs/common';
import { WorksheetCollaborationService } from '../services/worksheet-collaboration.service';
import { WorksheetQuestionLockingService } from '../services/worksheet-question-locking.service';
import { 
  CollaborationEvent, 
  UserAction, 
  CollaborationErrorCode 
} from '../enums/collaboration-events.enum';
import {
  JoinWorksheetRoomDto,
  UpdatePresenceDto,
  AcquireQuestionLockDto,
  ReleaseQuestionLockDto,
  TypingIndicatorDto,
  RealtimeQuestionUpdateDto
} from '../dto/collaboration.dto';
import { 
  IUserPresence, 
  ICollaborationError,
  IRoomJoinResponse 
} from '../interfaces/collaboration.interface';
import { UserContext } from '../services/worksheet-question.service';

@WebSocketGateway({
  namespace: '/worksheet-collaboration',
  cors: {
    origin: '*',
    credentials: true
  }
})
@Injectable()
export class WorksheetQuestionCollaborationGateway 
  implements OnGatewayConnection, OnGatewayDisconnect {
  
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(WorksheetQuestionCollaborationGateway.name);
  
  // Track socket to user mapping
  private socketUserMap = new Map<string, { user: UserContext; worksheetId: string }>();
  
  // Rate limiting
  private rateLimitMap = new Map<string, { count: number; resetTime: number }>();
  private readonly RATE_LIMIT_WINDOW = 60000; // 1 minute
  private readonly RATE_LIMIT_MAX = 100; // 100 events per minute

  constructor(
    private readonly collaborationService: WorksheetCollaborationService,
    private readonly lockingService: WorksheetQuestionLockingService
  ) {}

  /**
   * Handle new WebSocket connections
   */
  async handleConnection(client: Socket) {
    this.logger.log(`New connection attempt: ${client.id}`);
    
    try {
      // Connection will be fully established after successful room join
      client.emit(CollaborationEvent.CONNECTION_ESTABLISHED, {
        socketId: client.id,
        timestamp: new Date().toISOString(),
        message: 'Connection established. Please join a worksheet room.'
      });

    } catch (error) {
      this.logger.error(`Connection failed for ${client.id}`, error);
      client.emit(CollaborationEvent.CONNECTION_ERROR, {
        errorCode: CollaborationErrorCode.AUTHENTICATION_FAILED,
        message: 'Connection failed',
        timestamp: new Date().toISOString()
      });
      client.disconnect();
    }
  }

  /**
   * Handle WebSocket disconnections
   */
  async handleDisconnect(client: Socket) {
    const socketInfo = this.socketUserMap.get(client.id);
    
    if (socketInfo) {
      const { user, worksheetId } = socketInfo;
      
      try {
        // Leave collaboration room
        const leftUser = await this.collaborationService.leaveRoom(worksheetId, client.id);
        
        if (leftUser) {
          // Broadcast user left event
          this.broadcastToRoom(worksheetId, CollaborationEvent.USER_LEFT, {
            user: leftUser,
            timestamp: new Date().toISOString()
          }, client.id);

          // Release any locks held by this user
          await this.lockingService.releaseUserLocks(user.sub);
        }

        // Clean up tracking
        this.socketUserMap.delete(client.id);
        this.rateLimitMap.delete(client.id);

        this.logger.log(`User ${user.sub} disconnected from worksheet ${worksheetId}`);

      } catch (error) {
        this.logger.error(`Error handling disconnect for ${client.id}`, error);
      }
    }
  }

  /**
   * Join a worksheet collaboration room
   */
  @SubscribeMessage(CollaborationEvent.JOIN_WORKSHEET)
  async handleJoinWorksheet(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: JoinWorksheetRoomDto
  ): Promise<IRoomJoinResponse> {
    try {
      // Validate access and get user context
      const user = await this.collaborationService.validateWorksheetAccess(
        data.worksheetId,
        data.token
      );

      // Join the collaboration room
      const room = await this.collaborationService.joinRoom(
        data.worksheetId,
        user,
        client.id
      );

      // Join Socket.IO room
      const roomName = this.getRoomName(data.worksheetId);
      await client.join(roomName);

      // Track socket-user mapping
      this.socketUserMap.set(client.id, {
        user,
        worksheetId: data.worksheetId
      });

      // Get current room users
      const activeUsers = await this.collaborationService.getRoomUsers(data.worksheetId);
      
      // Get current question locks
      const questionLocks = await this.lockingService.getWorksheetLocks(data.worksheetId);

      // Broadcast user joined event to others in room
      this.broadcastToRoom(data.worksheetId, CollaborationEvent.USER_JOINED, {
        user: activeUsers.find(u => u.userId === user.sub),
        timestamp: new Date().toISOString()
      }, client.id);

      // Send current active users list to the joining user
      client.emit(CollaborationEvent.ACTIVE_USERS_LIST, {
        users: activeUsers,
        timestamp: new Date().toISOString()
      });

      this.logger.log(`User ${user.sub} joined worksheet ${data.worksheetId} collaboration`);

      return {
        success: true,
        room: {
          worksheetId: data.worksheetId,
          activeUsers,
          lockedQuestions: questionLocks.map(lock => lock.questionId),
          roomSettings: room.settings
        }
      };

    } catch (error) {
      this.logger.error(`Failed to join worksheet room`, error);
      
      const errorResponse: ICollaborationError = {
        errorCode: CollaborationErrorCode.AUTHORIZATION_FAILED,
        message: error.message || 'Failed to join worksheet room',
        timestamp: new Date(),
        recoverable: false
      };

      client.emit(CollaborationEvent.COLLABORATION_ERROR, errorResponse);

      return {
        success: false,
        error: errorResponse
      };
    }
  }

  /**
   * Update user presence
   */
  @SubscribeMessage(CollaborationEvent.USER_PRESENCE_UPDATE)
  async handlePresenceUpdate(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: UpdatePresenceDto
  ) {
    if (!this.checkRateLimit(client.id)) {
      return;
    }

    const socketInfo = this.socketUserMap.get(client.id);
    if (!socketInfo) {
      throw new WsException('Not connected to any worksheet room');
    }

    try {
      const updatedPresence = await this.collaborationService.updateUserPresence(
        socketInfo.worksheetId,
        client.id,
        data.action,
        data.questionId,
        data.metadata
      );

      if (updatedPresence) {
        // Broadcast presence update to room
        this.broadcastToRoom(socketInfo.worksheetId, CollaborationEvent.USER_PRESENCE_UPDATE, {
          user: updatedPresence,
          timestamp: new Date().toISOString()
        }, client.id);
      }

    } catch (error) {
      this.logger.error(`Failed to update presence for ${client.id}`, error);
      this.emitError(client, CollaborationErrorCode.INVALID_OPERATION, 'Failed to update presence');
    }
  }

  /**
   * Acquire question lock
   */
  @SubscribeMessage(CollaborationEvent.QUESTION_LOCK_ACQUIRED)
  async handleAcquireLock(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: AcquireQuestionLockDto
  ) {
    if (!this.checkRateLimit(client.id)) {
      return;
    }

    const socketInfo = this.socketUserMap.get(client.id);
    if (!socketInfo) {
      throw new WsException('Not connected to any worksheet room');
    }

    try {
      const lockResponse = await this.lockingService.acquireLock(
        socketInfo.worksheetId,
        data.questionId,
        socketInfo.user,
        {
          questionId: data.questionId,
          lockType: data.lockType || 'pessimistic' as any,
          duration: data.duration,
          force: data.force
        }
      );

      if (lockResponse.success && lockResponse.lock) {
        // Broadcast lock acquired to room
        this.broadcastToRoom(socketInfo.worksheetId, CollaborationEvent.QUESTION_LOCK_ACQUIRED, {
          lock: lockResponse.lock,
          timestamp: new Date().toISOString()
        });

        // Update user presence to editing
        await this.collaborationService.updateUserPresence(
          socketInfo.worksheetId,
          client.id,
          UserAction.EDITING_QUESTION,
          data.questionId
        );

        client.emit(CollaborationEvent.QUESTION_LOCK_ACQUIRED, {
          success: true,
          lock: lockResponse.lock,
          timestamp: new Date().toISOString()
        });

      } else {
        client.emit(CollaborationEvent.QUESTION_LOCK_FAILED, {
          success: false,
          questionId: data.questionId,
          error: lockResponse.error,
          conflictingLock: lockResponse.conflictingLock,
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      this.logger.error(`Failed to acquire lock for question ${data.questionId}`, error);
      this.emitError(client, CollaborationErrorCode.LOCK_ACQUISITION_FAILED, 'Failed to acquire question lock');
    }
  }

  /**
   * Release question lock
   */
  @SubscribeMessage(CollaborationEvent.QUESTION_LOCK_RELEASED)
  async handleReleaseLock(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: ReleaseQuestionLockDto
  ) {
    const socketInfo = this.socketUserMap.get(client.id);
    if (!socketInfo) {
      throw new WsException('Not connected to any worksheet room');
    }

    try {
      const released = await this.lockingService.releaseLock(
        socketInfo.worksheetId,
        data.questionId,
        socketInfo.user.sub
      );

      if (released) {
        // Broadcast lock released to room
        this.broadcastToRoom(socketInfo.worksheetId, CollaborationEvent.QUESTION_LOCK_RELEASED, {
          questionId: data.questionId,
          releasedBy: socketInfo.user.sub,
          reason: data.reason,
          timestamp: new Date().toISOString()
        });

        // Update user presence back to viewing
        await this.collaborationService.updateUserPresence(
          socketInfo.worksheetId,
          client.id,
          UserAction.VIEWING
        );
      }

      client.emit(CollaborationEvent.QUESTION_LOCK_RELEASED, {
        success: released,
        questionId: data.questionId,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error(`Failed to release lock for question ${data.questionId}`, error);
      this.emitError(client, CollaborationErrorCode.INVALID_OPERATION, 'Failed to release question lock');
    }
  }

  /**
   * Handle typing indicators
   */
  @SubscribeMessage(CollaborationEvent.QUESTION_TYPING_INDICATOR)
  async handleTypingIndicator(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: TypingIndicatorDto
  ) {
    if (!this.checkRateLimit(client.id, 10)) { // More restrictive for typing
      return;
    }

    const socketInfo = this.socketUserMap.get(client.id);
    if (!socketInfo) {
      return;
    }

    // Broadcast typing indicator to room (excluding sender)
    this.broadcastToRoom(socketInfo.worksheetId, CollaborationEvent.QUESTION_TYPING_INDICATOR, {
      questionId: data.questionId,
      userId: socketInfo.user.sub,
      userName: socketInfo.user.email,
      isTyping: data.isTyping,
      cursorPosition: data.cursorPosition,
      timestamp: new Date().toISOString()
    }, client.id);
  }

  /**
   * Handle real-time question content updates
   */
  @SubscribeMessage(CollaborationEvent.QUESTION_CONTENT_CHANGED)
  async handleQuestionContentChange(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: RealtimeQuestionUpdateDto
  ) {
    if (!this.checkRateLimit(client.id, 20)) {
      return;
    }

    const socketInfo = this.socketUserMap.get(client.id);
    if (!socketInfo) {
      throw new WsException('Not connected to any worksheet room');
    }

    try {
      // Check if user has lock on the question
      const canEdit = await this.lockingService.canEditQuestion(
        socketInfo.worksheetId,
        data.questionId,
        socketInfo.user.sub
      );

      if (!canEdit) {
        this.emitError(client, CollaborationErrorCode.LOCK_NOT_OWNED,
          'Cannot edit question without acquiring lock first');
        return;
      }

      // Broadcast content change to room (excluding sender)
      this.broadcastToRoom(socketInfo.worksheetId, CollaborationEvent.QUESTION_CONTENT_CHANGED, {
        questionId: data.questionId,
        field: data.field,
        value: data.value,
        userId: socketInfo.user.sub,
        version: data.version,
        isPartial: data.isPartial,
        timestamp: new Date().toISOString()
      }, client.id);

    } catch (error) {
      this.logger.error(`Failed to handle content change for question ${data.questionId}`, error);
      this.emitError(client, CollaborationErrorCode.INVALID_OPERATION, 'Failed to process content change');
    }
  }

  /**
   * Handle presence heartbeat
   */
  @SubscribeMessage(CollaborationEvent.PRESENCE_HEARTBEAT)
  async handlePresenceHeartbeat(@ConnectedSocket() client: Socket) {
    const socketInfo = this.socketUserMap.get(client.id);
    if (!socketInfo) {
      return;
    }

    try {
      await this.collaborationService.updateUserPresence(
        socketInfo.worksheetId,
        client.id,
        UserAction.VIEWING // Keep current action, just update timestamp
      );
    } catch (error) {
      this.logger.error(`Failed to update heartbeat for ${client.id}`, error);
    }
  }

  /**
   * Broadcast message to all users in a worksheet room
   */
  private broadcastToRoom(
    worksheetId: string,
    event: CollaborationEvent,
    data: any,
    excludeSocketId?: string
  ) {
    const roomName = this.getRoomName(worksheetId);
    
    if (excludeSocketId) {
      this.server.to(roomName).except(excludeSocketId).emit(event, data);
    } else {
      this.server.to(roomName).emit(event, data);
    }
  }

  /**
   * Emit error to specific client
   */
  private emitError(
    client: Socket,
    errorCode: CollaborationErrorCode,
    message: string,
    details?: any
  ) {
    const error: ICollaborationError = {
      errorCode,
      message,
      details,
      timestamp: new Date(),
      recoverable: true
    };

    client.emit(CollaborationEvent.COLLABORATION_ERROR, error);
  }

  /**
   * Check rate limiting for socket
   */
  private checkRateLimit(socketId: string, customLimit?: number): boolean {
    const now = Date.now();
    const limit = customLimit || this.RATE_LIMIT_MAX;
    
    let rateLimitInfo = this.rateLimitMap.get(socketId);
    
    if (!rateLimitInfo || now > rateLimitInfo.resetTime) {
      rateLimitInfo = {
        count: 1,
        resetTime: now + this.RATE_LIMIT_WINDOW
      };
      this.rateLimitMap.set(socketId, rateLimitInfo);
      return true;
    }

    if (rateLimitInfo.count >= limit) {
      return false;
    }

    rateLimitInfo.count++;
    return true;
  }

  /**
   * Get Socket.IO room name for worksheet
   */
  private getRoomName(worksheetId: string): string {
    return `worksheet-collaboration-${worksheetId}`;
  }

  /**
   * Notify user about job completion
   */
  async notifyJobCompletion(userId: string, notification: any): Promise<void> {
    try {
      // Find socket for the user
      const userSocket = Array.from(this.socketUserMap.entries())
        .find(([_, info]) => info.user.sub === userId)?.[0];

      if (userSocket) {
        this.server.to(userSocket).emit('job_completion', notification);
        this.logger.debug(`Notified user ${userId} about job completion`);
      } else {
        this.logger.warn(`User ${userId} not connected for job completion notification`);
      }
    } catch (error) {
      this.logger.error(`Error notifying job completion to user ${userId}`, error);
    }
  }

  /**
   * Broadcast question update to room (called by service)
   */
  async broadcastQuestionUpdate(
    worksheetId: string,
    event: CollaborationEvent,
    data: any,
    excludeUserId?: string
  ) {
    try {
      const roomName = this.getRoomName(worksheetId);
      
      if (excludeUserId) {
        // Find socket ID for user to exclude
        const excludeSocketId = Array.from(this.socketUserMap.entries())
          .find(([_, info]) => info.user.sub === excludeUserId && info.worksheetId === worksheetId)?.[0];
        
        if (excludeSocketId) {
          this.server.to(roomName).except(excludeSocketId).emit(event, {
            ...data,
            timestamp: new Date().toISOString()
          });
        } else {
          this.server.to(roomName).emit(event, {
            ...data,
            timestamp: new Date().toISOString()
          });
        }
      } else {
        this.server.to(roomName).emit(event, {
          ...data,
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      this.logger.error(`Failed to broadcast ${event} for worksheet ${worksheetId}`, error);
    }
  }
}
