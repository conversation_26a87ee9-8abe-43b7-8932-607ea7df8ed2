import { TypeOrmModule } from '@nestjs/typeorm';
import { WorksheetController } from './worksheet.controller';
import { Module, forwardRef } from '@nestjs/common';
import { WorksheetService } from './worksheet.service';
import { OptionType } from '../options/entities/option-type.entity';
import { OptionValue } from '../options/entities/option-value.entity';
import { BullModule } from '@nestjs/bullmq';
import { WorksheetQueueService } from './worksheet-queue.service';
import { WorksheetGenerateConsumer } from './queue.consumer';
import { DocumentsModule } from '../documents/documents.module';
import { PromptModule } from '../prompt/prompt.module';
import { MongodbModule } from '../mongodb/mongodb.module';
import { SocketModule } from '../socket/socket.module';
import { Worksheet } from './entities/worksheet.entity';
import { WorksheetOption } from './entities/worksheet-option.entity';
import { WorksheetPromptResult, WorksheetPromptResultSchema } from '../mongodb/schemas/worksheet-prompt-result.schema';
import { WorksheetDocument, WorksheetDocumentSchema } from '../mongodb/schemas/worksheet-document.schema'; // Import WorksheetDocument and its schema
import { WorksheetCleanupService } from './worksheet-cleanup.service';
import { GenImageModule } from '../gen-image/gen-image.module';
import { WorksheetDocumentCacheService } from './services/worksheet-document-cache.service';
import { WorksheetQuestionService } from './services/worksheet-question.service';
import { WorksheetQuestionAuditService } from './services/worksheet-question-audit.service';
import { WorksheetQuestionLockingService } from './services/worksheet-question-locking.service';
import { WorksheetCollaborationService } from './services/worksheet-collaboration.service';
import { WorksheetQuestionCollaborationGateway } from './gateways/worksheet-question-collaboration.gateway';
import { WorksheetQuestionDocument, WorksheetQuestionDocumentSchema } from '../mongodb/schemas/worksheet-question-document.schema';
import { WorksheetContainer, WorksheetContainerSchema } from '../mongodb/schemas/worksheet-container.schema';
import { RedisModule } from '../redis/redis.module';
import {QueryCacheModule} from "../mongodb/query-cache.module";
import {QuestionPoolModule} from "../question-pool/question-pool.module";
import { ValidationModule } from '../validation/validation.module';
import { MonitoringModule } from '../monitoring/monitoring.module';
import { MongooseModule } from '@nestjs/mongoose';
import { AiModule } from '../ai/ai.module';
import { AuthModule } from '../auth/auth.module';
import { PermissionModule } from '../permission/permission.module';
import { WorksheetQuestionThrottlerGuard } from './guards/worksheet-question-throttler.guard';
import { WorksheetQuestionThrottlerService } from './services/worksheet-question-throttler.service';
import { WorksheetQuestionMetricsService } from './services/worksheet-question-metrics.service';
import { WorksheetQuestionMetricsController } from './controllers/worksheet-question-metrics.controller';
import { WorksheetQuestionMetricsInterceptor } from './interceptors/worksheet-question-metrics.interceptor';
import { WorksheetQuestionDbOptimizationService } from './services/worksheet-question-db-optimization.service';
import { WorksheetQuestionEnhancedCacheService } from './services/worksheet-question-enhanced-cache.service';
import { WorksheetRedisCacheService } from './services/worksheet-redis-cache.service';
import { WorksheetCacheManagementController } from './controllers/worksheet-cache-management.controller';
import { WorksheetQuestionBackgroundService } from './services/worksheet-question-background.service';
import { WorksheetQuestionBulkProcessor } from './processors/worksheet-question-bulk.processor';
import { WorksheetBackgroundJobsController } from './controllers/worksheet-background-jobs.controller';
import { WorksheetQuestionMemoryOptimizationService } from './services/worksheet-question-memory-optimization.service';
import { WorksheetMemoryOptimizationController } from './controllers/worksheet-memory-optimization.controller';
import { WorksheetQuestionPerformanceTestingService } from './services/worksheet-question-performance-testing.service';
import { WorksheetQuestionCapacityPlanningService } from './services/worksheet-question-capacity-planning.service';
import { WorksheetPerformanceTestingController } from './controllers/worksheet-performance-testing.controller';
import { WorksheetQuestionCapacityPlanningController } from './controllers/worksheet-question-capacity-planning.controller';
import { UsageTrackingModule } from '../usage-tracking/usage-tracking.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      OptionType,
      OptionValue,
      Worksheet,
      WorksheetOption,
    ]),
    MongooseModule.forFeature([
      { name: WorksheetPromptResult.name, schema: WorksheetPromptResultSchema },
      { name: WorksheetDocument.name, schema: WorksheetDocumentSchema }, // Add WorksheetDocument to Mongoose features
      { name: WorksheetQuestionDocument.name, schema: WorksheetQuestionDocumentSchema },
      { name: WorksheetContainer.name, schema: WorksheetContainerSchema },
    ]),
    BullModule.registerQueue({
      name: 'worksheet_generate',
    }),
    BullModule.registerQueue({
      name: 'worksheet-questions',
    }),
    DocumentsModule,
    forwardRef(() => PromptModule),
    forwardRef(() => QueryCacheModule),
    MongodbModule,
    SocketModule,
    GenImageModule,
    AiModule,
    forwardRef(() => QuestionPoolModule),
    forwardRef(() => MonitoringModule),
    ValidationModule,
    forwardRef(() => AuthModule), // Import AuthModule to get JwtService for AuthGuard
    PermissionModule, // Import PermissionModule for permission-based access control
    UsageTrackingModule, // Import UsageTrackingModule for daily question limits
    RedisModule, // Add Redis for collaboration features
  ],
  controllers: [WorksheetController, WorksheetQuestionMetricsController, WorksheetCacheManagementController, WorksheetBackgroundJobsController, WorksheetMemoryOptimizationController, WorksheetQuestionCapacityPlanningController],
  providers: [
    WorksheetService,
    WorksheetQueueService,
    WorksheetGenerateConsumer,
    WorksheetCleanupService,
    WorksheetDocumentCacheService,
    WorksheetQuestionService,
    WorksheetQuestionAuditService,
    WorksheetQuestionLockingService,
    WorksheetCollaborationService,
    WorksheetQuestionCollaborationGateway,
    WorksheetQuestionThrottlerGuard,
    WorksheetQuestionThrottlerService,
    WorksheetQuestionMetricsService,
    WorksheetQuestionMetricsInterceptor,
    WorksheetQuestionDbOptimizationService,
    WorksheetQuestionEnhancedCacheService,
    WorksheetRedisCacheService,
    WorksheetQuestionBackgroundService,
    WorksheetQuestionBulkProcessor,
    WorksheetQuestionMemoryOptimizationService,
    WorksheetQuestionPerformanceTestingService,
    WorksheetQuestionCapacityPlanningService,
  ],
  exports: [
    WorksheetService,
    WorksheetDocumentCacheService,
    WorksheetQuestionService,
    WorksheetQuestionLockingService,
    WorksheetCollaborationService,
    WorksheetQuestionCollaborationGateway
  ],
})
export class WorksheetModule {}
