/**
 * Interfaces related to worksheet generation queue processing
 */

import { SelectionStrategy } from '../dto/worksheet-generation-options.dto';

/**
 * Data structure for worksheet generation job
 */
export interface WorksheetGenerateJobData {
  worksheetId: string;
  topic: string;
  grade: string;
  questionCount: number;
  isCustomQuestionCount?: boolean;
  questionSourceStrategy?: SelectionStrategy;
}

/**
 * Question item structure from the question pool
 */
export interface QuestionItem {
  type: string;
  content: string;
  options?: any[];
  answer?: any;
  explain?: string;
  imagePrompt?: string;
  image?: string;
  subject?: string;
  parentSubject?: string;
  childSubject?: string;
  grade?: string;
  [key: string]: any;
}
