import { EUserRole } from '../../user/dto/create-user.dto';
import { 
  UserAction, 
  LockType, 
  ConflictResolutionStrategy, 
  RoomStatus,
  CollaborationErrorCode 
} from '../enums/collaboration-events.enum';

/**
 * User presence information for collaboration
 */
export interface IUserPresence {
  userId: string;
  email: string;
  name?: string;
  role: EUserRole;
  schoolId?: string;
  socketId: string;
  joinedAt: Date;
  lastSeen: Date;
  currentAction: UserAction;
  currentQuestionId?: string;
  isActive: boolean;
  cursor?: {
    questionId: string;
    position: number;
  };
}

/**
 * Question lock information
 */
export interface IQuestionLock {
  questionId: string;
  worksheetId: string;
  lockedBy: string;
  lockedByName?: string;
  lockType: LockType;
  acquiredAt: Date;
  expiresAt: Date;
  lastRenewed?: Date;
  sessionId: string;
  isActive: boolean;
}

/**
 * Edit session information
 */
export interface IEditSession {
  sessionId: string;
  questionId: string;
  worksheetId: string;
  userId: string;
  startedAt: Date;
  lastActivity: Date;
  changes: IQuestionChange[];
  isActive: boolean;
}

/**
 * Question change tracking
 */
export interface IQuestionChange {
  field: string;
  oldValue: any;
  newValue: any;
  timestamp: Date;
  userId: string;
  changeId: string;
}

/**
 * Conflict information
 */
export interface IEditConflict {
  conflictId: string;
  questionId: string;
  worksheetId: string;
  users: string[];
  changes: IQuestionChange[];
  detectedAt: Date;
  resolvedAt?: Date;
  resolutionStrategy?: ConflictResolutionStrategy;
  resolvedBy?: string;
  isResolved: boolean;
}

/**
 * Collaboration room information
 */
export interface ICollaborationRoom {
  worksheetId: string;
  activeUsers: Map<string, IUserPresence>;
  questionLocks: Map<string, IQuestionLock>;
  editSessions: Map<string, IEditSession>;
  conflicts: Map<string, IEditConflict>;
  roomStatus: RoomStatus;
  createdAt: Date;
  lastActivity: Date;
  maxUsers: number;
  settings: IRoomSettings;
}

/**
 * Room settings and configuration
 */
export interface IRoomSettings {
  lockTimeout: number; // in milliseconds
  presenceTimeout: number; // in milliseconds
  maxConcurrentEditors: number;
  enableTypingIndicators: boolean;
  enableAutoSave: boolean;
  autoSaveInterval: number; // in milliseconds
  conflictResolutionStrategy: ConflictResolutionStrategy;
  allowGuestUsers: boolean;
}

/**
 * WebSocket message payload base interface
 */
export interface ICollaborationMessage {
  event: string;
  worksheetId: string;
  userId: string;
  timestamp: Date;
  data?: any;
}

/**
 * Typing indicator information
 */
export interface ITypingIndicator {
  questionId: string;
  userId: string;
  userName?: string;
  isTyping: boolean;
  lastTyped: Date;
  cursorPosition?: number;
}

/**
 * Collaboration statistics
 */
export interface ICollaborationStats {
  worksheetId: string;
  totalUsers: number;
  activeUsers: number;
  lockedQuestions: number;
  activeEditSessions: number;
  unresolvedConflicts: number;
  lastActivity: Date;
}

/**
 * Error information for collaboration
 */
export interface ICollaborationError {
  errorCode: CollaborationErrorCode;
  message: string;
  details?: any;
  timestamp: Date;
  userId?: string;
  worksheetId?: string;
  questionId?: string;
  recoverable: boolean;
  suggestedAction?: string;
}

/**
 * Lock acquisition request
 */
export interface ILockRequest {
  questionId: string;
  lockType: LockType;
  duration?: number; // in milliseconds
  force?: boolean; // force acquire lock (admin only)
}

/**
 * Lock acquisition response
 */
export interface ILockResponse {
  success: boolean;
  lock?: IQuestionLock;
  error?: ICollaborationError;
  conflictingLock?: IQuestionLock;
}

/**
 * Presence update payload
 */
export interface IPresenceUpdate {
  action: UserAction;
  questionId?: string;
  metadata?: any;
}

/**
 * Real-time question update payload
 */
export interface IRealtimeQuestionUpdate {
  questionId: string;
  field: string;
  value: any;
  userId: string;
  timestamp: Date;
  version: number;
  isPartial: boolean;
}

/**
 * Conflict resolution request
 */
export interface IConflictResolutionRequest {
  conflictId: string;
  strategy: ConflictResolutionStrategy;
  selectedChanges?: string[]; // change IDs to keep
  mergedContent?: any; // manually merged content
}

/**
 * Room join request
 */
export interface IRoomJoinRequest {
  worksheetId: string;
  token: string;
  clientInfo?: {
    userAgent: string;
    platform: string;
    version: string;
  };
}

/**
 * Room join response
 */
export interface IRoomJoinResponse {
  success: boolean;
  room?: {
    worksheetId: string;
    activeUsers: IUserPresence[];
    lockedQuestions: string[];
    roomSettings: IRoomSettings;
  };
  error?: ICollaborationError;
}
