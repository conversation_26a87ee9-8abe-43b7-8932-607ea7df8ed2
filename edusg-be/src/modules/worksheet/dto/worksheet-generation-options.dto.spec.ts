import { validate } from 'class-validator';
import { WorksheetGenerationOptionsDto, SelectionStrategy } from './worksheet-generation-options.dto';

describe('WorksheetGenerationOptionsDto', () => {
  let dto: WorksheetGenerationOptionsDto;

  beforeEach(() => {
    dto = new WorksheetGenerationOptionsDto();
  });

  describe('Valid Input Validation', () => {
    it('should pass validation with valid selectionStrategy', async () => {
      dto.selectionStrategy = SelectionStrategy.HYBRID;
      dto.useQuestionPoolOverride = true;
      dto.minPoolQuestionsRequired = 10;

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation with all valid selection strategies', async () => {
      const strategies = [
        SelectionStrategy.POOL_ONLY,
        SelectionStrategy.AI_ONLY,
        SelectionStrategy.HYBRID,
        SelectionStrategy.MIXED,
      ];

      for (const strategy of strategies) {
        dto.selectionStrategy = strategy;
        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });

    it('should pass validation with valid boolean for useQuestionPoolOverride', async () => {
      dto.useQuestionPoolOverride = false;
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);

      dto.useQuestionPoolOverride = true;
      const errors2 = await validate(dto);
      expect(errors2).toHaveLength(0);
    });

    it('should pass validation with valid minPoolQuestionsRequired', async () => {
      dto.minPoolQuestionsRequired = 0;
      let errors = await validate(dto);
      expect(errors).toHaveLength(0);

      dto.minPoolQuestionsRequired = 50;
      errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should pass validation when all fields are undefined (optional)', async () => {
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });

  describe('Invalid Input Validation', () => {
    it('should fail validation with invalid selectionStrategy', async () => {
      (dto as any).selectionStrategy = 'invalid-strategy';

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('selectionStrategy');
      expect(errors[0].constraints?.isEnum).toContain('pool-only, ai-only, hybrid, mixed');
    });

    it('should fail validation with non-boolean useQuestionPoolOverride', async () => {
      (dto as any).useQuestionPoolOverride = 'not-a-boolean';

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('useQuestionPoolOverride');
      expect(errors[0].constraints?.isBoolean).toBeDefined();
    });

    it('should fail validation with non-integer minPoolQuestionsRequired', async () => {
      (dto as any).minPoolQuestionsRequired = 'not-a-number';

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('minPoolQuestionsRequired');
      expect(errors[0].constraints?.isInt).toBeDefined();
    });

    it('should fail validation with negative minPoolQuestionsRequired', async () => {
      dto.minPoolQuestionsRequired = -1;

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('minPoolQuestionsRequired');
      expect(errors[0].constraints?.min).toBeDefined();
    });

    it('should fail validation with decimal minPoolQuestionsRequired', async () => {
      (dto as any).minPoolQuestionsRequired = 10.5;

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('minPoolQuestionsRequired');
      expect(errors[0].constraints?.isInt).toBeDefined();
    });
  });

  describe('Multiple Validation Errors', () => {
    it('should return multiple errors for multiple invalid fields', async () => {
      (dto as any).selectionStrategy = 'invalid';
      (dto as any).useQuestionPoolOverride = 'not-boolean';
      dto.minPoolQuestionsRequired = -5;

      const errors = await validate(dto);
      expect(errors).toHaveLength(3);

      const properties = errors.map(error => error.property);
      expect(properties).toContain('selectionStrategy');
      expect(properties).toContain('useQuestionPoolOverride');
      expect(properties).toContain('minPoolQuestionsRequired');
    });
  });

  describe('Edge Cases', () => {
    it('should handle zero value for minPoolQuestionsRequired', async () => {
      dto.minPoolQuestionsRequired = 0;

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should handle large values for minPoolQuestionsRequired', async () => {
      dto.minPoolQuestionsRequired = 999999;

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });
});
