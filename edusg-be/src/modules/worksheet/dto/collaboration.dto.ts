import { 
  IsS<PERSON>, 
  IsO<PERSON>al, 
  IsEnum, 
  IsBoolean, 
  IsNumber, 
  IsDate, 
  IsArray,
  IsNotEmpty,
  Min,
  Max,
  ValidateNested,
  IsObject
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  UserAction, 
  LockType, 
  ConflictResolutionStrategy,
  CollaborationErrorCode 
} from '../enums/collaboration-events.enum';

/**
 * DTO for joining a worksheet collaboration room
 */
export class JoinWorksheetRoomDto {
  @ApiProperty({ description: 'Worksheet ID to join' })
  @IsString()
  @IsNotEmpty()
  worksheetId: string;

  @ApiProperty({ description: 'JWT authentication token' })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiPropertyOptional({ description: 'Client information' })
  @IsOptional()
  @IsObject()
  clientInfo?: {
    userAgent: string;
    platform: string;
    version: string;
  };
}

/**
 * DTO for updating user presence
 */
export class UpdatePresenceDto {
  @ApiProperty({ 
    description: 'Current user action',
    enum: UserAction
  })
  @IsEnum(UserAction)
  action: UserAction;

  @ApiPropertyOptional({ description: 'Question ID being worked on' })
  @IsOptional()
  @IsString()
  questionId?: string;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  @IsOptional()
  @IsObject()
  metadata?: any;
}

/**
 * DTO for acquiring a question lock
 */
export class AcquireQuestionLockDto {
  @ApiProperty({ description: 'Question ID to lock' })
  @IsString()
  @IsNotEmpty()
  questionId: string;

  @ApiPropertyOptional({ 
    description: 'Lock type',
    enum: LockType,
    default: LockType.PESSIMISTIC
  })
  @IsOptional()
  @IsEnum(LockType)
  lockType?: LockType;

  @ApiPropertyOptional({ 
    description: 'Lock duration in milliseconds',
    minimum: 60000, // 1 minute
    maximum: 1800000, // 30 minutes
    default: 300000 // 5 minutes
  })
  @IsOptional()
  @IsNumber()
  @Min(60000)
  @Max(1800000)
  duration?: number;

  @ApiPropertyOptional({ 
    description: 'Force acquire lock (admin only)',
    default: false
  })
  @IsOptional()
  @IsBoolean()
  force?: boolean;
}

/**
 * DTO for releasing a question lock
 */
export class ReleaseQuestionLockDto {
  @ApiProperty({ description: 'Question ID to unlock' })
  @IsString()
  @IsNotEmpty()
  questionId: string;

  @ApiPropertyOptional({ description: 'Reason for releasing lock' })
  @IsOptional()
  @IsString()
  reason?: string;
}

/**
 * DTO for real-time question updates
 */
export class RealtimeQuestionUpdateDto {
  @ApiProperty({ description: 'Question ID being updated' })
  @IsString()
  @IsNotEmpty()
  questionId: string;

  @ApiProperty({ description: 'Field being updated' })
  @IsString()
  @IsNotEmpty()
  field: string;

  @ApiProperty({ description: 'New value for the field' })
  value: any;

  @ApiPropertyOptional({ 
    description: 'Version number for conflict detection',
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  version?: number;

  @ApiPropertyOptional({ 
    description: 'Whether this is a partial update',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  isPartial?: boolean;
}

/**
 * DTO for typing indicators
 */
export class TypingIndicatorDto {
  @ApiProperty({ description: 'Question ID where typing is occurring' })
  @IsString()
  @IsNotEmpty()
  questionId: string;

  @ApiProperty({ description: 'Whether user is currently typing' })
  @IsBoolean()
  isTyping: boolean;

  @ApiPropertyOptional({ 
    description: 'Cursor position in the content',
    minimum: 0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  cursorPosition?: number;
}

/**
 * DTO for conflict resolution
 */
export class ResolveConflictDto {
  @ApiProperty({ description: 'Conflict ID to resolve' })
  @IsString()
  @IsNotEmpty()
  conflictId: string;

  @ApiProperty({ 
    description: 'Resolution strategy',
    enum: ConflictResolutionStrategy
  })
  @IsEnum(ConflictResolutionStrategy)
  strategy: ConflictResolutionStrategy;

  @ApiPropertyOptional({ 
    description: 'Selected change IDs to keep (for manual merge)',
    type: [String]
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  selectedChanges?: string[];

  @ApiPropertyOptional({ description: 'Manually merged content' })
  @IsOptional()
  @IsObject()
  mergedContent?: any;

  @ApiPropertyOptional({ description: 'Reason for resolution choice' })
  @IsOptional()
  @IsString()
  reason?: string;
}

/**
 * DTO for collaboration error reporting
 */
export class CollaborationErrorDto {
  @ApiProperty({ 
    description: 'Error code',
    enum: CollaborationErrorCode
  })
  @IsEnum(CollaborationErrorCode)
  errorCode: CollaborationErrorCode;

  @ApiProperty({ description: 'Error message' })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiPropertyOptional({ description: 'Additional error details' })
  @IsOptional()
  @IsObject()
  details?: any;

  @ApiPropertyOptional({ description: 'Whether error is recoverable' })
  @IsOptional()
  @IsBoolean()
  recoverable?: boolean;

  @ApiPropertyOptional({ description: 'Suggested action to resolve error' })
  @IsOptional()
  @IsString()
  suggestedAction?: string;
}

/**
 * DTO for room settings update
 */
export class UpdateRoomSettingsDto {
  @ApiPropertyOptional({ 
    description: 'Lock timeout in milliseconds',
    minimum: 60000,
    maximum: 1800000
  })
  @IsOptional()
  @IsNumber()
  @Min(60000)
  @Max(1800000)
  lockTimeout?: number;

  @ApiPropertyOptional({ 
    description: 'Maximum concurrent editors',
    minimum: 1,
    maximum: 20
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(20)
  maxConcurrentEditors?: number;

  @ApiPropertyOptional({ description: 'Enable typing indicators' })
  @IsOptional()
  @IsBoolean()
  enableTypingIndicators?: boolean;

  @ApiPropertyOptional({ description: 'Enable auto-save' })
  @IsOptional()
  @IsBoolean()
  enableAutoSave?: boolean;

  @ApiPropertyOptional({ 
    description: 'Auto-save interval in milliseconds',
    minimum: 5000,
    maximum: 60000
  })
  @IsOptional()
  @IsNumber()
  @Min(5000)
  @Max(60000)
  autoSaveInterval?: number;

  @ApiPropertyOptional({ 
    description: 'Default conflict resolution strategy',
    enum: ConflictResolutionStrategy
  })
  @IsOptional()
  @IsEnum(ConflictResolutionStrategy)
  conflictResolutionStrategy?: ConflictResolutionStrategy;
}

/**
 * DTO for kicking a user from room (admin only)
 */
export class KickUserDto {
  @ApiProperty({ description: 'User ID to kick' })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiPropertyOptional({ description: 'Reason for kicking user' })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiPropertyOptional({ 
    description: 'Ban duration in minutes (0 = no ban)',
    minimum: 0,
    maximum: 1440 // 24 hours
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1440)
  banDurationMinutes?: number;
}
