import { 
  <PERSON>S<PERSON>, 
  <PERSON>A<PERSON>y, 
  IsOptional, 
  IsNumber, 
  IsBoolean,
  ArrayMinSize, 
  ArrayMaxSize, 
  ValidateNested,
  IsNotEmpty,
  <PERSON>,
  <PERSON>
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AddQuestionToWorksheetDto, UpdateWorksheetQuestionDto } from './worksheet-question.dto';

/**
 * DTO for bulk adding questions to a worksheet
 */
export class BulkAddQuestionsDto {
  @ApiProperty({ 
    description: 'Array of questions to add to the worksheet',
    type: [AddQuestionToWorksheetDto],
    minItems: 1,
    maxItems: 50
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(50)
  @ValidateNested({ each: true })
  @Type(() => AddQuestionToWorksheetDto)
  questions: AddQuestionToWorksheetDto[];

  @ApiPropertyOptional({ 
    description: 'Position to insert questions (if not provided, will be added at the end)',
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  insertPosition?: number;

  @ApiPropertyOptional({ 
    description: 'Whether to validate questions before adding',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  validateQuestions?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Reason for bulk addition (for audit purposes)' 
  })
  @IsOptional()
  @IsString()
  @Max(500)
  reason?: string;
}

/**
 * DTO for bulk removing questions from a worksheet
 */
export class BulkRemoveQuestionsDto {
  @ApiProperty({ 
    description: 'Question IDs to remove from the worksheet',
    type: [String],
    minItems: 1,
    maxItems: 50
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(50)
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  questionIds: string[];

  @ApiPropertyOptional({ 
    description: 'Reason for bulk removal (for audit purposes)' 
  })
  @IsOptional()
  @IsString()
  @Max(500)
  reason?: string;

  @ApiPropertyOptional({ 
    description: 'Whether to force removal even if it would leave the worksheet with no questions',
    default: false
  })
  @IsOptional()
  @IsBoolean()
  forceRemoval?: boolean = false;
}

/**
 * DTO for bulk updating questions in a worksheet
 */
export class BulkUpdateQuestionsDto {
  @ApiProperty({ 
    description: 'Array of question updates',
    minItems: 1,
    maxItems: 50
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(50)
  @ValidateNested({ each: true })
  @Type(() => BulkQuestionUpdateItem)
  updates: BulkQuestionUpdateItem[];

  @ApiPropertyOptional({ 
    description: 'Reason for bulk update (for audit purposes)' 
  })
  @IsOptional()
  @IsString()
  @Max(500)
  reason?: string;

  @ApiPropertyOptional({ 
    description: 'Whether to validate questions after updating',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  validateQuestions?: boolean = true;
}

/**
 * Individual question update item for bulk operations
 */
export class BulkQuestionUpdateItem {
  @ApiProperty({ 
    description: 'Question ID to update' 
  })
  @IsString()
  @IsNotEmpty()
  questionId: string;

  @ApiProperty({ 
    description: 'Question updates to apply',
    type: UpdateWorksheetQuestionDto
  })
  @ValidateNested()
  @Type(() => UpdateWorksheetQuestionDto)
  updates: UpdateWorksheetQuestionDto;
}

/**
 * Response DTO for bulk operations
 */
export class BulkOperationResponseDto {
  @ApiProperty({ description: 'Whether the operation was successful' })
  success: boolean;

  @ApiProperty({ description: 'Number of items processed successfully' })
  successCount: number;

  @ApiProperty({ description: 'Number of items that failed' })
  failureCount: number;

  @ApiProperty({ description: 'Total number of items processed' })
  totalCount: number;

  @ApiProperty({ description: 'Array of successful operations', required: false })
  successes?: any[];

  @ApiProperty({ description: 'Array of failed operations with error details', required: false })
  failures?: Array<{
    item?: any;
    questionId?: string;
    error: string;
    index: number;
  }>;

  @ApiProperty({ description: 'Operation timestamp' })
  timestamp: string;

  @ApiProperty({ description: 'Processing time in milliseconds' })
  processingTimeMs: number;
}
