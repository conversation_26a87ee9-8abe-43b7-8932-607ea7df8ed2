import { <PERSON><PERSON>rray, IsBoolean, IsNumber, IsOptional, IsString, ValidateNested, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { SelectionStrategy } from './worksheet-generation-options.dto';

export class WorksheetOptionDto {
  @ApiProperty({
    description: 'Unique key for the worksheet option',
    example: 'difficulty',
  })
  @IsString()
  @IsOptional()
  key: string;

  @ApiProperty({
    description: 'Value of the worksheet option',
    example: 'easy',
  })
  @IsString()
  @IsOptional()
  value: string;

  @ApiProperty({
    description: 'Display text for the worksheet option (optional)',
    example: 'Easy difficulty',
    required: false,
  })
  @IsString()
  @IsOptional()
  text?: string;

  @ApiProperty({
    description: 'Number of questions for this option (used with exerciseType)',
    example: 5,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  count?: number;
}

export class SubjectItemDto {
  @ApiProperty({
    description: 'Label of the subject',
    example: 'Speed',
  })
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Items within the subject',
    example: ['Speed Conversion', 'Time, Distance and Speed Relationship'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  items: string[];
}

export class QuestionTypeDto {
  @ApiProperty({
    description: 'Label of the question type',
    example: 'Fill Blank',
  })
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Count of questions for this type',
    example: 3,
  })
  @IsNumber()
  count: number;
}

export class CreateWorksheetDto {
  @ApiProperty({
    description: 'Title of the worksheet',
    example: 'Math Worksheet - Fractions',
    required: false,
  })
  @IsString()
  @IsOptional()
  title: string;

  @ApiProperty({
    description: 'Description of the worksheet',
    example: 'A worksheet for practicing fractions',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Grade level',
    example: 'P6',
    required: false,
  })
  @IsString()
  @IsOptional()
  grade?: string;

  @ApiProperty({
    description: 'Topic of the worksheet',
    example: 'Mathematics',
    required: false,
  })
  @IsString()
  @IsOptional()
  topic?: string;

  @ApiProperty({
    description: 'Subject items',
    type: [SubjectItemDto],
    required: false,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SubjectItemDto)
  @IsOptional()
  subject?: SubjectItemDto[];

  @ApiProperty({
    description: 'Difficulty level',
    example: 'Medium',
    required: false,
  })
  @IsString()
  @IsOptional()
  level?: string;

  @ApiProperty({
    description: 'Language of the worksheet',
    example: 'English',
    required: false,
  })
  @IsString()
  @IsOptional()
  language?: string;

  @ApiProperty({
    description: 'Number of questions',
    example: '10',
    required: false,
  })
  @IsString()
  @IsOptional()
  question_count?: string;

  @ApiProperty({
    description: 'Flag indicating if question_count is a custom value',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isCustomQuestionCount?: boolean;

  @ApiProperty({
    description: 'Question types',
    type: [QuestionTypeDto],
    required: false,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QuestionTypeDto)
  @IsOptional()
  question_type?: QuestionTypeDto[];

  @ApiProperty({
    description: 'Legacy options for the worksheet (for backward compatibility)',
    type: [WorksheetOptionDto],
    required: false,
  })
  @IsArray()
  @IsOptional()
  options?: WorksheetOptionDto[];

  @ApiProperty({
    description: 'Strategy for question sourcing',
    enum: SelectionStrategy,
    example: SelectionStrategy.HYBRID,
    required: false,
  })
  @IsOptional()
  @IsEnum(SelectionStrategy, {
    message: 'questionSourceStrategy must be one of: pool-only, ai-only, hybrid, mixed',
  })
  questionSourceStrategy?: SelectionStrategy;
}
