import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsBoolean, IsInt, Min } from 'class-validator';

export enum SelectionStrategy {
  POOL_ONLY = 'pool-only',
  AI_ONLY = 'ai-only',
  HYBRID = 'hybrid',
  MIXED = 'mixed',
}

export class WorksheetGenerationOptionsDto {
  @ApiProperty({
    description: 'Strategy for question sourcing',
    enum: SelectionStrategy,
    example: SelectionStrategy.HYBRID,
    required: false,
  })
  @IsOptional()
  @IsEnum(SelectionStrategy, {
    message: 'selectionStrategy must be one of: pool-only, ai-only, hybrid, mixed',
  })
  selectionStrategy?: SelectionStrategy;

  @ApiProperty({
    description: 'Override to force use of question pool regardless of global settings',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({
    message: 'useQuestionPoolOverride must be a boolean value',
  })
  useQuestionPoolOverride?: boolean;

  @ApiProperty({
    description: 'Minimum number of questions required in pool for pool-based strategies',
    example: 10,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsInt({
    message: 'minPoolQuestionsRequired must be an integer',
  })
  @Min(0, {
    message: 'minPoolQuestionsRequired must be a non-negative integer',
  })
  minPoolQuestionsRequired?: number;
}

/**
 * Interface for internal use in services
 * Extends the DTO with resolved configuration values
 */
export interface WorksheetGenerationOptions extends WorksheetGenerationOptionsDto {
  // These will be populated by the service layer based on configuration
  resolvedStrategy?: SelectionStrategy;
  poolEnabled?: boolean;
  strategyAllowed?: boolean;
}
