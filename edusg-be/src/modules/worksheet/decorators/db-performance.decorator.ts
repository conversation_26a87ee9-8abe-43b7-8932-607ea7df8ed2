import { Logger } from '@nestjs/common';

/**
 * Decorator to monitor database query performance
 */
export function MonitorDbPerformance(operation: string, collection: string = 'worksheet_questions') {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const logger = new Logger(`${target.constructor.name}.${propertyName}`);

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      const metricsService = this.metricsService || this.worksheetQuestionMetricsService;

      try {
        // Execute the original method
        const result = await originalMethod.apply(this, args);
        
        // Record successful operation
        const duration = (Date.now() - startTime) / 1000;
        
        if (metricsService) {
          metricsService.recordDbQuery(duration, operation, collection, 'success');
        }

        // Log slow queries (>100ms)
        if (duration > 0.1) {
          logger.warn(`Slow query detected: ${operation} took ${duration.toFixed(3)}s`);
        } else {
          logger.debug(`Query completed: ${operation} took ${duration.toFixed(3)}s`);
        }

        return result;
      } catch (error) {
        // Record failed operation
        const duration = (Date.now() - startTime) / 1000;
        
        if (metricsService) {
          metricsService.recordDbQuery(duration, operation, collection, 'error');
        }

        logger.error(`Query failed: ${operation} took ${duration.toFixed(3)}s - ${error.message}`);
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Decorator to monitor aggregation pipeline performance
 */
export function MonitorAggregationPerformance(pipelineName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const logger = new Logger(`${target.constructor.name}.${propertyName}`);

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      const metricsService = this.metricsService || this.worksheetQuestionMetricsService;

      try {
        // Log pipeline stages for debugging
        const pipeline = args[0];
        if (Array.isArray(pipeline)) {
          logger.debug(`Executing aggregation pipeline '${pipelineName}' with ${pipeline.length} stages`);
        }

        const result = await originalMethod.apply(this, args);
        
        const duration = (Date.now() - startTime) / 1000;
        
        if (metricsService) {
          metricsService.recordDbQuery(duration, `aggregation_${pipelineName}`, 'worksheet_questions', 'success');
        }

        // Log slow aggregations (>200ms)
        if (duration > 0.2) {
          logger.warn(`Slow aggregation detected: ${pipelineName} took ${duration.toFixed(3)}s`);
        } else {
          logger.debug(`Aggregation completed: ${pipelineName} took ${duration.toFixed(3)}s`);
        }

        return result;
      } catch (error) {
        const duration = (Date.now() - startTime) / 1000;
        
        if (metricsService) {
          metricsService.recordDbQuery(duration, `aggregation_${pipelineName}`, 'worksheet_questions', 'error');
        }

        logger.error(`Aggregation failed: ${pipelineName} took ${duration.toFixed(3)}s - ${error.message}`);
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Decorator to monitor connection pool usage
 */
export function MonitorConnectionPool() {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const logger = new Logger(`${target.constructor.name}.${propertyName}`);

    descriptor.value = async function (...args: any[]) {
      const metricsService = this.metricsService || this.worksheetQuestionMetricsService;

      try {
        // Get connection info before operation
        const connection = this.worksheetQuestionModel?.db || this.connection;
        if (connection && metricsService) {
          const readyState = connection.readyState;
          metricsService.updateDbConnections(readyState === 1 ? 1 : 0);
        }

        const result = await originalMethod.apply(this, args);
        return result;
      } catch (error) {
        logger.error(`Connection pool error in ${propertyName}: ${error.message}`);
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Decorator to optimize query execution with hints and options
 */
export function OptimizeQuery(options: {
  hint?: any;
  maxTimeMS?: number;
  readPreference?: string;
  batchSize?: number;
}) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const logger = new Logger(`${target.constructor.name}.${propertyName}`);

    descriptor.value = async function (...args: any[]) {
      try {
        // Apply query optimizations
        const query = args[0];
        
        if (query && typeof query.hint === 'function' && options.hint) {
          query.hint(options.hint);
          logger.debug(`Applied index hint: ${JSON.stringify(options.hint)}`);
        }

        if (query && typeof query.maxTimeMS === 'function' && options.maxTimeMS) {
          query.maxTimeMS(options.maxTimeMS);
          logger.debug(`Applied maxTimeMS: ${options.maxTimeMS}`);
        }

        if (query && typeof query.read === 'function' && options.readPreference) {
          query.read(options.readPreference);
          logger.debug(`Applied read preference: ${options.readPreference}`);
        }

        if (query && typeof query.batchSize === 'function' && options.batchSize) {
          query.batchSize(options.batchSize);
          logger.debug(`Applied batch size: ${options.batchSize}`);
        }

        return await originalMethod.apply(this, args);
      } catch (error) {
        logger.error(`Query optimization error in ${propertyName}: ${error.message}`);
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Decorator to validate and optimize aggregation pipelines
 */
export function ValidateAggregationPipeline() {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const logger = new Logger(`${target.constructor.name}.${propertyName}`);

    descriptor.value = async function (...args: any[]) {
      try {
        const pipeline = args[0];
        
        if (Array.isArray(pipeline)) {
          // Validate and optimize pipeline
          const optimizedPipeline = optimizePipeline(pipeline);
          args[0] = optimizedPipeline;
          
          if (optimizedPipeline.length !== pipeline.length) {
            logger.debug(`Pipeline optimized: ${pipeline.length} -> ${optimizedPipeline.length} stages`);
          }
        }

        return await originalMethod.apply(this, args);
      } catch (error) {
        logger.error(`Pipeline validation error in ${propertyName}: ${error.message}`);
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Helper function to optimize aggregation pipelines
 */
function optimizePipeline(pipeline: any[]): any[] {
  const optimized = [...pipeline];
  
  // Move $match stages to the beginning
  const matchStages: any[] = [];
  const otherStages: any[] = [];
  
  optimized.forEach(stage => {
    if (stage.$match) {
      matchStages.push(stage);
    } else {
      otherStages.push(stage);
    }
  });
  
  // Combine multiple $match stages
  if (matchStages.length > 1) {
    const combinedMatch = matchStages.reduce((combined, stage) => {
      return { $match: { ...combined.$match, ...stage.$match } };
    }, { $match: {} });
    
    return [combinedMatch, ...otherStages];
  }
  
  // Move $limit stages closer to the beginning
  const limitIndex = otherStages.findIndex(stage => stage.$limit);
  if (limitIndex > 0) {
    const limitStage = otherStages.splice(limitIndex, 1)[0];
    // Insert after $match but before expensive operations like $lookup
    const insertIndex = otherStages.findIndex(stage => 
      stage.$lookup || stage.$unwind || stage.$group
    );
    if (insertIndex >= 0) {
      otherStages.splice(insertIndex, 0, limitStage);
    } else {
      otherStages.unshift(limitStage);
    }
  }
  
  return [...matchStages, ...otherStages];
}

/**
 * Decorator to add query explain functionality for debugging
 */
export function ExplainQuery(enabled: boolean = false) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const logger = new Logger(`${target.constructor.name}.${propertyName}`);

    descriptor.value = async function (...args: any[]) {
      if (!enabled || process.env.NODE_ENV === 'production') {
        return await originalMethod.apply(this, args);
      }

      try {
        const query = args[0];
        
        // Execute explain if query supports it
        if (query && typeof query.explain === 'function') {
          const explanation = await query.explain('executionStats');
          logger.debug(`Query explanation for ${propertyName}:`, {
            executionTimeMillis: explanation.executionStats?.executionTimeMillis,
            totalDocsExamined: explanation.executionStats?.totalDocsExamined,
            totalDocsReturned: explanation.executionStats?.totalDocsReturned,
            indexesUsed: explanation.executionStats?.executionStages?.indexName
          });
        }

        return await originalMethod.apply(this, args);
      } catch (error) {
        logger.error(`Query explain error in ${propertyName}: ${error.message}`);
        return await originalMethod.apply(this, args);
      }
    };

    return descriptor;
  };
}
