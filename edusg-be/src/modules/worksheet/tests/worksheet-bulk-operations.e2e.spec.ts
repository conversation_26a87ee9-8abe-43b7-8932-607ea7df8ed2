import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import * as request from 'supertest';
import { AppModule } from '../../../app.module';
import { WorksheetService } from '../worksheet.service';
import { WorksheetQuestionService } from '../services/worksheet-question.service';
import { EUserRole } from '../../user/dto/create-user.dto';
import { EQuestionType, EQuestionDifficulty } from '../../../shared/interfaces/exercise-question.interface';
import { BulkAddQuestionsDto, BulkRemoveQuestionsDto, BulkUpdateQuestionsDto } from '../dto/bulk-operations.dto';

describe('WorksheetBulkOperations - E2E Tests', () => {
  let app: INestApplication;
  let worksheetService: WorksheetService;
  let worksheetQuestionService: WorksheetQuestionService;
  let authToken: string;
  let testWorksheetId: string;

  const mockUser = {
    id: 'e2e-user-123',
    email: '<EMAIL>',
    role: EUserRole.TEACHER,
    schoolId: 'e2e-school-123'
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    worksheetService = moduleFixture.get<WorksheetService>(WorksheetService);
    worksheetQuestionService = moduleFixture.get<WorksheetQuestionService>(WorksheetQuestionService);

    // Create a test worksheet for E2E tests
    const testWorksheet = await worksheetService.create(
      {
        title: 'E2E Test Worksheet',
        description: 'Worksheet for end-to-end bulk operations testing',
        options: []
      },
      mockUser as any
    );
    testWorksheetId = testWorksheet.id;

    // Mock authentication token
    authToken = 'Bearer e2e-test-token';
  });

  afterAll(async () => {
    // Clean up test data
    if (testWorksheetId) {
      try {
        await worksheetService.remove(testWorksheetId, mockUser as any);
      } catch (error) {
        // Ignore cleanup errors
      }
    }
    await app.close();
  });

  describe('Complete Bulk Operations Workflow', () => {
    it('should perform a complete workflow: bulk add, update, and remove', async () => {
      // Step 1: Bulk add questions
      const bulkAddDto: BulkAddQuestionsDto = {
        questions: [
          {
            type: EQuestionType.MULTIPLE_CHOICE,
            content: 'What is the capital of France?',
            options: ['London', 'Berlin', 'Paris', 'Madrid'],
            answer: ['Paris'],
            explain: 'Paris is the capital and largest city of France',
            difficulty: EQuestionDifficulty.EASY,
            subject: 'Geography'
          },
          {
            type: EQuestionType.TRUE_FALSE,
            content: 'The Earth is flat',
            options: ['True', 'False'],
            answer: ['False'],
            explain: 'The Earth is approximately spherical',
            difficulty: EQuestionDifficulty.EASY,
            subject: 'Science'
          },
          {
            type: EQuestionType.MULTIPLE_CHOICE,
            content: 'What is 10 + 15?',
            options: ['20', '25', '30', '35'],
            answer: ['25'],
            explain: '10 + 15 = 25',
            difficulty: EQuestionDifficulty.EASY,
            subject: 'Mathematics'
          }
        ],
        reason: 'E2E test - adding initial questions'
      };

      const addResponse = await request(app.getHttpServer())
        .post(`/worksheets/${testWorksheetId}/questions/bulk-add`)
        .set('Authorization', authToken)
        .send(bulkAddDto)
        .expect(201);

      expect(addResponse.body.success).toBe(true);
      expect(addResponse.body.successCount).toBe(3);
      expect(addResponse.body.failureCount).toBe(0);

      const addedQuestionIds = addResponse.body.successes.map((q: any) => q.id);
      expect(addedQuestionIds).toHaveLength(3);

      // Step 2: Verify questions were added by getting worksheet
      const worksheet = await worksheetService.findOne(testWorksheetId, mockUser as any);
      expect(worksheet.questions).toHaveLength(3);
      expect(worksheet.totalQuestions).toBe(3);

      // Step 3: Bulk update questions
      const bulkUpdateDto: BulkUpdateQuestionsDto = {
        updates: [
          {
            questionId: addedQuestionIds[0],
            updates: {
              content: 'Updated: What is the capital of France?',
              explain: 'Updated explanation: Paris is the capital of France and a major European city'
            }
          },
          {
            questionId: addedQuestionIds[1],
            updates: {
              difficulty: EQuestionDifficulty.MEDIUM
            }
          }
        ],
        reason: 'E2E test - updating questions'
      };

      const updateResponse = await request(app.getHttpServer())
        .patch(`/worksheets/${testWorksheetId}/questions/bulk-update`)
        .set('Authorization', authToken)
        .send(bulkUpdateDto)
        .expect(200);

      expect(updateResponse.body.success).toBe(true);
      expect(updateResponse.body.successCount).toBe(2);
      expect(updateResponse.body.failureCount).toBe(0);

      // Step 4: Verify updates were applied
      const updatedWorksheet = await worksheetService.findOne(testWorksheetId, mockUser as any);
      const updatedQuestion = updatedWorksheet.questions.find(q => q.id === addedQuestionIds[0]);
      expect(updatedQuestion?.content).toBe('Updated: What is the capital of France?');

      // Step 5: Bulk remove some questions
      const bulkRemoveDto: BulkRemoveQuestionsDto = {
        questionIds: [addedQuestionIds[1], addedQuestionIds[2]], // Remove 2 out of 3 questions
        reason: 'E2E test - removing questions'
      };

      const removeResponse = await request(app.getHttpServer())
        .delete(`/worksheets/${testWorksheetId}/questions/bulk-remove`)
        .set('Authorization', authToken)
        .send(bulkRemoveDto)
        .expect(200);

      expect(removeResponse.body.success).toBe(true);
      expect(removeResponse.body.successCount).toBe(2);
      expect(removeResponse.body.failureCount).toBe(0);

      // Step 6: Verify final state
      const finalWorksheet = await worksheetService.findOne(testWorksheetId, mockUser as any);
      expect(finalWorksheet.questions).toHaveLength(1);
      expect(finalWorksheet.totalQuestions).toBe(1);
      expect(finalWorksheet.questions[0].id).toBe(addedQuestionIds[0]);
      expect(finalWorksheet.questions[0].content).toBe('Updated: What is the capital of France?');
    });

    it('should handle mixed success/failure scenarios', async () => {
      // Add some questions first
      const initialQuestions: BulkAddQuestionsDto = {
        questions: [
          {
            type: EQuestionType.MULTIPLE_CHOICE,
            content: 'Test question 1',
            options: ['A', 'B', 'C', 'D'],
            answer: ['A'],
            explain: 'Test explanation 1',
            difficulty: EQuestionDifficulty.EASY
          },
          {
            type: EQuestionType.MULTIPLE_CHOICE,
            content: 'Test question 2',
            options: ['A', 'B', 'C', 'D'],
            answer: ['B'],
            explain: 'Test explanation 2',
            difficulty: EQuestionDifficulty.EASY
          }
        ]
      };

      const addResponse = await request(app.getHttpServer())
        .post(`/worksheets/${testWorksheetId}/questions/bulk-add`)
        .set('Authorization', authToken)
        .send(initialQuestions)
        .expect(201);

      const questionIds = addResponse.body.successes.map((q: any) => q.id);

      // Try to remove valid and invalid question IDs
      const mixedRemoveDto: BulkRemoveQuestionsDto = {
        questionIds: [
          questionIds[0], // Valid
          'nonexistent-question-id', // Invalid
          questionIds[1] // Valid
        ]
      };

      const removeResponse = await request(app.getHttpServer())
        .delete(`/worksheets/${testWorksheetId}/questions/bulk-remove`)
        .set('Authorization', authToken)
        .send(mixedRemoveDto)
        .expect(200);

      expect(removeResponse.body.success).toBe(false); // Mixed results
      expect(removeResponse.body.successCount).toBe(2); // 2 valid removals
      expect(removeResponse.body.failureCount).toBe(1); // 1 invalid removal
      expect(removeResponse.body.failures).toHaveLength(1);
      expect(removeResponse.body.failures[0].questionId).toBe('nonexistent-question-id');
    });

    it('should respect question limits during bulk add', async () => {
      // Try to add more questions than the limit allows
      const tooManyQuestions: BulkAddQuestionsDto = {
        questions: Array(101).fill({
          type: EQuestionType.MULTIPLE_CHOICE,
          content: 'Limit test question',
          options: ['A', 'B', 'C', 'D'],
          answer: ['A'],
          explain: 'This should fail due to limits',
          difficulty: EQuestionDifficulty.EASY
        })
      };

      await request(app.getHttpServer())
        .post(`/worksheets/${testWorksheetId}/questions/bulk-add`)
        .set('Authorization', authToken)
        .send(tooManyQuestions)
        .expect(400); // Should fail with bad request
    });

    it('should prevent removing all questions', async () => {
      // First, ensure we have exactly one question
      const worksheet = await worksheetService.findOne(testWorksheetId, mockUser as any);
      const questionIds = worksheet.questions.map(q => q.id);

      if (questionIds.length === 0) {
        // Add a question first
        const singleQuestion: BulkAddQuestionsDto = {
          questions: [{
            type: EQuestionType.MULTIPLE_CHOICE,
            content: 'Last question',
            options: ['A', 'B', 'C', 'D'],
            answer: ['A'],
            explain: 'This is the last question',
            difficulty: EQuestionDifficulty.EASY
          }]
        };

        const addResponse = await request(app.getHttpServer())
          .post(`/worksheets/${testWorksheetId}/questions/bulk-add`)
          .set('Authorization', authToken)
          .send(singleQuestion)
          .expect(201);

        questionIds.push(addResponse.body.successes[0].id);
      }

      // Try to remove all questions (should fail)
      const removeAllDto: BulkRemoveQuestionsDto = {
        questionIds: questionIds
      };

      await request(app.getHttpServer())
        .delete(`/worksheets/${testWorksheetId}/questions/bulk-remove`)
        .set('Authorization', authToken)
        .send(removeAllDto)
        .expect(400); // Should fail with bad request
    });
  });

  describe('Performance Tests', () => {
    it('should handle bulk operations within reasonable time limits', async () => {
      const startTime = Date.now();

      // Add 20 questions
      const manyQuestions: BulkAddQuestionsDto = {
        questions: Array(20).fill(null).map((_, index) => ({
          type: EQuestionType.MULTIPLE_CHOICE,
          content: `Performance test question ${index + 1}`,
          options: ['A', 'B', 'C', 'D'],
          answer: ['A'],
          explain: `Explanation for question ${index + 1}`,
          difficulty: EQuestionDifficulty.EASY
        }))
      };

      const response = await request(app.getHttpServer())
        .post(`/worksheets/${testWorksheetId}/questions/bulk-add`)
        .set('Authorization', authToken)
        .send(manyQuestions)
        .expect(201);

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      expect(response.body.success).toBe(true);
      expect(response.body.successCount).toBe(20);
      expect(processingTime).toBeLessThan(5000); // Should complete within 5 seconds

      // Clean up - remove the added questions
      const questionIds = response.body.successes.map((q: any) => q.id);
      await request(app.getHttpServer())
        .delete(`/worksheets/${testWorksheetId}/questions/bulk-remove`)
        .set('Authorization', authToken)
        .send({ questionIds, forceRemoval: true })
        .expect(200);
    });
  });
});
