import { Test, TestingModule } from '@nestjs/testing';
import { WorksheetQuestionService, UserContext } from '../services/worksheet-question.service';
import { WorksheetQuestionAuditService } from '../services/worksheet-question-audit.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { WorksheetQuestionCollaborationGateway } from '../gateways/worksheet-question-collaboration.gateway';
import { WorksheetQuestionLockingService } from '../services/worksheet-question-locking.service';
import { WorksheetQuestionMetricsService } from '../services/worksheet-question-metrics.service';
import { WorksheetQuestionEnhancedCacheService } from '../services/worksheet-question-enhanced-cache.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getModelToken } from '@nestjs/mongoose';
import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { EUserRole } from '../../user/dto/create-user.dto';
import { EQuestionType, EQuestionDifficulty } from '../../../shared/interfaces/exercise-question.interface';
import { AddQuestionToWorksheetDto, UpdateWorksheetQuestionDto } from '../dto/worksheet-question.dto';

describe('WorksheetBulkOperations - Performance Tests', () => {
  let service: WorksheetQuestionService;
  let worksheetRepository: any;
  let worksheetQuestionModel: any;

  const mockUserContext: UserContext = {
    sub: 'perf-user-123',
    email: '<EMAIL>',
    role: EUserRole.TEACHER,
    schoolId: 'perf-school-123'
  };

  const createMockWorksheet = (questionCount: number = 0): Worksheet => ({
    id: 'perf-worksheet-1',
    title: 'Performance Test Worksheet',
    questions: Array(questionCount).fill(null).map((_, index) => ({
      id: `perf-question-${index + 1}`,
      type: EQuestionType.MULTIPLE_CHOICE,
      content: `Performance test question ${index + 1}`,
      options: ['A', 'B', 'C', 'D'],
      answer: ['A'],
      explain: `Explanation ${index + 1}`,
      audit: { version: 1, createdBy: 'perf-user-123', createdAt: new Date() }
    })),
    totalQuestions: questionCount,
    maxQuestions: 1000, // Higher limit for performance testing
    schoolId: 'perf-school-123',
    createdBy: 'perf-user-123'
  } as any);

  const createTestQuestions = (count: number): AddQuestionToWorksheetDto[] => {
    return Array(count).fill(null).map((_, index) => ({
      type: EQuestionType.MULTIPLE_CHOICE,
      content: `Bulk test question ${index + 1}`,
      options: ['Option A', 'Option B', 'Option C', 'Option D'],
      answer: ['Option A'],
      explain: `This is the explanation for question ${index + 1}`,
      difficulty: EQuestionDifficulty.MEDIUM,
      subject: 'Performance Testing',
      grade: '10'
    }));
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorksheetQuestionService,
        {
          provide: getRepositoryToken(Worksheet),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          }
        },
        {
          provide: getModelToken(WorksheetQuestionDocument.name),
          useValue: {
            findOneAndUpdate: jest.fn(),
            updateOne: jest.fn(),
          }
        },
        {
          provide: WorksheetQuestionAuditService,
          useValue: {
            logBulkQuestionOperation: jest.fn(),
          }
        },
        {
          provide: SocketGateway,
          useValue: {
            server: {
              to: jest.fn().mockReturnThis(),
              emit: jest.fn(),
            }
          }
        },
        {
          provide: WorksheetQuestionCollaborationGateway,
          useValue: {
            broadcastQuestionUpdate: jest.fn(),
          }
        },
        {
          provide: WorksheetQuestionLockingService,
          useValue: {
            canEditQuestion: jest.fn().mockResolvedValue(true),
          }
        },
        {
          provide: WorksheetQuestionMetricsService,
          useValue: {
            recordMetric: jest.fn(),
          }
        },
        {
          provide: WorksheetQuestionEnhancedCacheService,
          useValue: {
            cacheWorksheetQuestions: jest.fn(),
            invalidateWorksheetCache: jest.fn(),
          }
        }
      ],
    }).compile();

    service = module.get<WorksheetQuestionService>(WorksheetQuestionService);
    worksheetRepository = module.get(getRepositoryToken(Worksheet));
    worksheetQuestionModel = module.get(getModelToken(WorksheetQuestionDocument.name));

    // Setup common mocks
    worksheetQuestionModel.findOneAndUpdate.mockResolvedValue({});
  });

  describe('Bulk Add Performance', () => {
    it('should handle adding 10 questions within 1 second', async () => {
      const mockWorksheet = createMockWorksheet(0);
      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
      worksheetRepository.save.mockResolvedValue(mockWorksheet);

      const questions = createTestQuestions(10);
      const startTime = Date.now();

      const result = await service.bulkAddQuestions(
        'perf-worksheet-1',
        questions,
        mockUserContext
      );

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      expect(result.success).toBe(true);
      expect(result.successCount).toBe(10);
      expect(processingTime).toBeLessThan(1000); // Should complete within 1 second
      expect(result.processingTimeMs).toBeLessThan(1000);
    });

    it('should handle adding 50 questions within 3 seconds', async () => {
      const mockWorksheet = createMockWorksheet(0);
      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
      worksheetRepository.save.mockResolvedValue(mockWorksheet);

      const questions = createTestQuestions(50);
      const startTime = Date.now();

      const result = await service.bulkAddQuestions(
        'perf-worksheet-1',
        questions,
        mockUserContext
      );

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      expect(result.success).toBe(true);
      expect(result.successCount).toBe(50);
      expect(processingTime).toBeLessThan(3000); // Should complete within 3 seconds
      expect(result.processingTimeMs).toBeLessThan(3000);
    });

    it('should maintain linear performance scaling', async () => {
      const testSizes = [5, 10, 20];
      const results: Array<{ size: number; time: number }> = [];

      for (const size of testSizes) {
        const mockWorksheet = createMockWorksheet(0);
        worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
        worksheetRepository.save.mockResolvedValue(mockWorksheet);

        const questions = createTestQuestions(size);
        const startTime = Date.now();

        const result = await service.bulkAddQuestions(
          'perf-worksheet-1',
          questions,
          mockUserContext
        );

        const endTime = Date.now();
        const processingTime = endTime - startTime;

        results.push({ size, time: processingTime });
        expect(result.success).toBe(true);
        expect(result.successCount).toBe(size);
      }

      // Check that performance scales roughly linearly
      const timePerQuestion5 = results[0].time / results[0].size;
      const timePerQuestion20 = results[2].time / results[2].size;
      
      // Time per question shouldn't increase by more than 3x as we scale up
      expect(timePerQuestion20).toBeLessThan(timePerQuestion5 * 3);
    });
  });

  describe('Bulk Remove Performance', () => {
    it('should handle removing 20 questions within 1 second', async () => {
      const mockWorksheet = createMockWorksheet(50); // Start with 50 questions
      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
      worksheetRepository.save.mockResolvedValue(mockWorksheet);

      const questionIdsToRemove = mockWorksheet.questions
        .slice(0, 20)
        .map(q => q.id);

      const startTime = Date.now();

      const result = await service.bulkRemoveQuestions(
        'perf-worksheet-1',
        questionIdsToRemove,
        mockUserContext
      );

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      expect(result.success).toBe(true);
      expect(result.successCount).toBe(20);
      expect(processingTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should efficiently reorder remaining questions after bulk removal', async () => {
      const mockWorksheet = createMockWorksheet(100); // Start with 100 questions
      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
      worksheetRepository.save.mockResolvedValue(mockWorksheet);

      // Remove every other question (50 questions)
      const questionIdsToRemove = mockWorksheet.questions
        .filter((_, index) => index % 2 === 0)
        .map(q => q.id);

      const startTime = Date.now();

      const result = await service.bulkRemoveQuestions(
        'perf-worksheet-1',
        questionIdsToRemove,
        mockUserContext
      );

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      expect(result.success).toBe(true);
      expect(result.successCount).toBe(50);
      expect(processingTime).toBeLessThan(2000); // Should complete within 2 seconds
    });
  });

  describe('Bulk Update Performance', () => {
    it('should handle updating 25 questions within 2 seconds', async () => {
      const mockWorksheet = createMockWorksheet(50);
      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
      worksheetRepository.save.mockResolvedValue(mockWorksheet);

      // Mock the validation and update methods
      jest.spyOn(service as any, 'validateQuestionVersion').mockResolvedValue(undefined);
      jest.spyOn(service as any, 'applyQuestionUpdate').mockImplementation((existing, updates) => ({
        ...existing,
        ...updates,
        audit: { ...existing.audit, version: existing.audit.version + 1 }
      }));

      const updates = mockWorksheet.questions.slice(0, 25).map(question => ({
        questionId: question.id,
        updates: {
          content: `Updated: ${question.content}`,
          explain: `Updated explanation for ${question.id}`
        } as UpdateWorksheetQuestionDto
      }));

      const startTime = Date.now();

      const result = await service.bulkUpdateQuestions(
        'perf-worksheet-1',
        updates,
        mockUserContext
      );

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      expect(result.success).toBe(true);
      expect(result.successCount).toBe(25);
      expect(processingTime).toBeLessThan(2000); // Should complete within 2 seconds
    });
  });

  describe('Memory Usage Tests', () => {
    it('should not cause memory leaks during large bulk operations', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform multiple bulk operations
      for (let i = 0; i < 5; i++) {
        const mockWorksheet = createMockWorksheet(0);
        worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
        worksheetRepository.save.mockResolvedValue(mockWorksheet);

        const questions = createTestQuestions(20);
        
        await service.bulkAddQuestions(
          'perf-worksheet-1',
          questions,
          mockUserContext
        );
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });

  describe('Concurrent Operations Performance', () => {
    it('should handle multiple concurrent bulk operations efficiently', async () => {
      const mockWorksheet = createMockWorksheet(0);
      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
      worksheetRepository.save.mockResolvedValue(mockWorksheet);

      const concurrentOperations = Array(3).fill(null).map((_, index) => {
        const questions = createTestQuestions(10);
        return service.bulkAddQuestions(
          `perf-worksheet-${index + 1}`,
          questions,
          mockUserContext
        );
      });

      const startTime = Date.now();
      const results = await Promise.all(concurrentOperations);
      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // All operations should succeed
      results.forEach(result => {
        expect(result.success).toBe(true);
        expect(result.successCount).toBe(10);
      });

      // Concurrent operations should not take significantly longer than sequential
      expect(totalTime).toBeLessThan(3000); // Should complete within 3 seconds
    });
  });

  describe('Database Operation Efficiency', () => {
    it('should minimize database calls during bulk operations', async () => {
      const mockWorksheet = createMockWorksheet(0);
      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
      worksheetRepository.save.mockResolvedValue(mockWorksheet);

      const questions = createTestQuestions(20);

      await service.bulkAddQuestions(
        'perf-worksheet-1',
        questions,
        mockUserContext
      );

      // Should only call findOne once (for validation) and save once (for persistence)
      expect(worksheetRepository.findOne).toHaveBeenCalledTimes(1);
      expect(worksheetRepository.save).toHaveBeenCalledTimes(1);
      
      // Should only call MongoDB update once (for cache)
      expect(worksheetQuestionModel.findOneAndUpdate).toHaveBeenCalledTimes(1);
    });
  });
});
