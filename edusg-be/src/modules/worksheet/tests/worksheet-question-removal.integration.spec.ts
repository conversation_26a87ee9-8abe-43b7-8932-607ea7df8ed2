import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import * as request from 'supertest';
import { WorksheetController } from '../worksheet.controller';
import { WorksheetService } from '../worksheet.service';
import { WorksheetQuestionService } from '../services/worksheet-question.service';
import { WorksheetQuestionAuditService } from '../services/worksheet-question-audit.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument, WorksheetQuestionDocumentSchema } from '../../mongodb/schemas/worksheet-question-document.schema';
import { EUserRole } from '../../user/dto/create-user.dto';
import { IExerciseQuestion, EQuestionType, EQuestionDifficulty } from '../../../shared/interfaces/exercise-question.interface';

describe('Worksheet Question Removal API (Integration)', () => {
  let app: INestApplication;
  let worksheetService: WorksheetService;
  let worksheetQuestionService: WorksheetQuestionService;

  // Mock user contexts for testing
  const adminUser = {
    id: 'admin-user-id',
    email: '<EMAIL>',
    role: EUserRole.ADMIN,
    schoolId: 'school-1'
  };

  const teacherUser = {
    id: 'teacher-user-id',
    email: '<EMAIL>',
    role: EUserRole.TEACHER,
    schoolId: 'school-1'
  };

  const independentTeacherUser = {
    id: 'independent-teacher-id',
    email: '<EMAIL>',
    role: EUserRole.INDEPENDENT_TEACHER,
    schoolId: null
  };

  // Mock worksheet with multiple questions
  const mockWorksheet: Partial<Worksheet> = {
    id: 'worksheet-1',
    title: 'Test Worksheet',
    schoolId: 'school-1',
    createdBy: 'teacher-user-id',
    questions: [
      {
        id: 'question-1',
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'What is 2+2?',
        options: ['2', '3', '4', '5'],
        answer: ['4'],
        explain: '2+2 equals 4',
        grade: '6',
        difficulty: EQuestionDifficulty.EASY,
        audit: {
          createdBy: 'teacher-user-id',
          createdAt: new Date(),
          updatedBy: 'teacher-user-id',
          updatedAt: new Date(),
          version: 1
        }
      },
      {
        id: 'question-2',
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'What is 3+3?',
        options: ['4', '5', '6', '7'],
        answer: ['6'],
        explain: '3+3 equals 6',
        grade: '6',
        difficulty: EQuestionDifficulty.EASY,
        audit: {
          createdBy: 'teacher-user-id',
          createdAt: new Date(),
          updatedBy: 'teacher-user-id',
          updatedAt: new Date(),
          version: 1
        }
      }
    ] as IExerciseQuestion[],
    totalQuestions: 2,
    maxQuestions: 100
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        // Mock TypeORM and Mongoose modules for testing
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [Worksheet],
          synchronize: true,
        }),
        MongooseModule.forRoot('mongodb://localhost/test'),
        MongooseModule.forFeature([
          { name: WorksheetQuestionDocument.name, schema: WorksheetQuestionDocumentSchema }
        ])
      ],
      controllers: [WorksheetController],
      providers: [
        WorksheetService,
        WorksheetQuestionService,
        WorksheetQuestionAuditService,
        {
          provide: SocketGateway,
          useValue: {
            server: {
              to: jest.fn().mockReturnValue({
                emit: jest.fn()
              })
            }
          }
        }
      ],
    }).compile();

    app = module.createNestApplication();
    await app.init();

    worksheetService = module.get<WorksheetService>(WorksheetService);
    worksheetQuestionService = module.get<WorksheetQuestionService>(WorksheetQuestionService);
  });

  afterEach(async () => {
    await app.close();
  });

  describe('DELETE /worksheets/:id/questions/:questionId', () => {
    it('should successfully remove a question from worksheet with multiple questions', async () => {
      // Mock the service methods
      jest.spyOn(worksheetQuestionService, 'removeQuestionFromWorksheet')
        .mockResolvedValue(undefined);

      const response = await request(app.getHttpServer())
        .delete('/worksheets/worksheet-1/questions/question-1')
        .set('Authorization', 'Bearer mock-token')
        .expect(204);

      expect(worksheetQuestionService.removeQuestionFromWorksheet)
        .toHaveBeenCalledWith('worksheet-1', 'question-1', expect.any(Object));
    });

    it('should return 400 when trying to remove the last question', async () => {
      // Mock service to throw BadRequestException
      jest.spyOn(worksheetQuestionService, 'removeQuestionFromWorksheet')
        .mockRejectedValue(new Error('Cannot remove question. Worksheet must have at least 1 question(s)'));

      await request(app.getHttpServer())
        .delete('/worksheets/worksheet-1/questions/question-1')
        .set('Authorization', 'Bearer mock-token')
        .expect(500); // NestJS will convert the error to 500 by default
    });

    it('should return 404 when worksheet is not found', async () => {
      jest.spyOn(worksheetQuestionService, 'removeQuestionFromWorksheet')
        .mockRejectedValue(new Error('Worksheet with ID non-existent not found'));

      await request(app.getHttpServer())
        .delete('/worksheets/non-existent/questions/question-1')
        .set('Authorization', 'Bearer mock-token')
        .expect(500);
    });

    it('should return 404 when question is not found in worksheet', async () => {
      jest.spyOn(worksheetQuestionService, 'removeQuestionFromWorksheet')
        .mockRejectedValue(new Error('Question with ID non-existent not found in worksheet'));

      await request(app.getHttpServer())
        .delete('/worksheets/worksheet-1/questions/non-existent')
        .set('Authorization', 'Bearer mock-token')
        .expect(500);
    });

    it('should return 403 when user lacks permissions', async () => {
      jest.spyOn(worksheetQuestionService, 'removeQuestionFromWorksheet')
        .mockRejectedValue(new Error('Insufficient permissions to modify worksheet questions'));

      await request(app.getHttpServer())
        .delete('/worksheets/worksheet-1/questions/question-1')
        .set('Authorization', 'Bearer mock-token')
        .expect(500);
    });
  });
});
