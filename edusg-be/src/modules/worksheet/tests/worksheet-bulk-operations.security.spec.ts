import { Test, TestingModule } from '@nestjs/testing';
import { ForbiddenException, BadRequestException } from '@nestjs/common';
import { WorksheetQuestionService, UserContext } from '../services/worksheet-question.service';
import { WorksheetQuestionAuditService } from '../services/worksheet-question-audit.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { WorksheetQuestionCollaborationGateway } from '../gateways/worksheet-question-collaboration.gateway';
import { WorksheetQuestionLockingService } from '../services/worksheet-question-locking.service';
import { WorksheetQuestionMetricsService } from '../services/worksheet-question-metrics.service';
import { WorksheetQuestionEnhancedCacheService } from '../services/worksheet-question-enhanced-cache.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getModelToken } from '@nestjs/mongoose';
import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { EUserRole } from '../../user/dto/create-user.dto';
import { EQuestionType, EQuestionDifficulty } from '../../../shared/interfaces/exercise-question.interface';
import { AddQuestionToWorksheetDto } from '../dto/worksheet-question.dto';

describe('WorksheetBulkOperations - Security Tests', () => {
  let service: WorksheetQuestionService;
  let worksheetRepository: any;
  let worksheetQuestionModel: any;

  const createUserContext = (role: EUserRole, schoolId?: string, userId: string = 'user-123'): UserContext => ({
    sub: userId,
    email: `${userId}@example.com`,
    role,
    schoolId
  });

  const createMockWorksheet = (schoolId: string, createdBy: string): Worksheet => ({
    id: 'worksheet-1',
    title: 'Security Test Worksheet',
    questions: [{
      id: 'question-1',
      type: EQuestionType.MULTIPLE_CHOICE,
      content: 'Test question',
      options: ['A', 'B', 'C', 'D'],
      answer: ['A'],
      explain: 'Test explanation',
      audit: { version: 1, createdBy, createdAt: new Date() }
    }],
    totalQuestions: 1,
    maxQuestions: 100,
    schoolId,
    createdBy
  } as any);

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorksheetQuestionService,
        {
          provide: getRepositoryToken(Worksheet),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          }
        },
        {
          provide: getModelToken(WorksheetQuestionDocument.name),
          useValue: {
            findOneAndUpdate: jest.fn(),
            updateOne: jest.fn(),
          }
        },
        {
          provide: WorksheetQuestionAuditService,
          useValue: {
            logBulkQuestionOperation: jest.fn(),
          }
        },
        {
          provide: SocketGateway,
          useValue: {
            server: {
              to: jest.fn().mockReturnThis(),
              emit: jest.fn(),
            }
          }
        },
        {
          provide: WorksheetQuestionCollaborationGateway,
          useValue: {
            broadcastQuestionUpdate: jest.fn(),
          }
        },
        {
          provide: WorksheetQuestionLockingService,
          useValue: {
            canEditQuestion: jest.fn().mockResolvedValue(true),
          }
        },
        {
          provide: WorksheetQuestionMetricsService,
          useValue: {
            recordMetric: jest.fn(),
          }
        },
        {
          provide: WorksheetQuestionEnhancedCacheService,
          useValue: {
            cacheWorksheetQuestions: jest.fn(),
            invalidateWorksheetCache: jest.fn(),
          }
        }
      ],
    }).compile();

    service = module.get<WorksheetQuestionService>(WorksheetQuestionService);
    worksheetRepository = module.get(getRepositoryToken(Worksheet));
    worksheetQuestionModel = module.get(getModelToken(WorksheetQuestionDocument.name));

    worksheetQuestionModel.findOneAndUpdate.mockResolvedValue({});
  });

  describe('Role-Based Access Control (RBAC)', () => {
    describe('Admin Access', () => {
      it('should allow admin to bulk add questions to any worksheet', async () => {
        const adminUser = createUserContext(EUserRole.ADMIN);
        const worksheetFromAnySchool = createMockWorksheet('other-school', 'other-user');
        
        worksheetRepository.findOne.mockResolvedValue(worksheetFromAnySchool);
        worksheetRepository.save.mockResolvedValue(worksheetFromAnySchool);

        const questions: AddQuestionToWorksheetDto[] = [{
          type: EQuestionType.MULTIPLE_CHOICE,
          content: 'Admin test question',
          options: ['A', 'B', 'C', 'D'],
          answer: ['A'],
          explain: 'Admin explanation',
          difficulty: EQuestionDifficulty.EASY
        }];

        const result = await service.bulkAddQuestions(
          'worksheet-1',
          questions,
          adminUser
        );

        expect(result.success).toBe(true);
        expect(result.successCount).toBe(1);
      });

      it('should allow admin to bulk remove questions from any worksheet', async () => {
        const adminUser = createUserContext(EUserRole.ADMIN);
        const worksheetFromAnySchool = createMockWorksheet('other-school', 'other-user');
        
        worksheetRepository.findOne.mockResolvedValue(worksheetFromAnySchool);
        worksheetRepository.save.mockResolvedValue(worksheetFromAnySchool);

        const result = await service.bulkRemoveQuestions(
          'worksheet-1',
          ['question-1'],
          adminUser,
          { forceRemoval: true } // Allow removing last question for test
        );

        expect(result.success).toBe(true);
        expect(result.successCount).toBe(1);
      });
    });

    describe('School Manager Access', () => {
      it('should allow school manager to bulk modify questions in their school', async () => {
        const schoolManagerUser = createUserContext(EUserRole.SCHOOL_MANAGER, 'school-a');
        const worksheetFromSameSchool = createMockWorksheet('school-a', 'teacher-in-school-a');
        
        worksheetRepository.findOne.mockResolvedValue(worksheetFromSameSchool);
        worksheetRepository.save.mockResolvedValue(worksheetFromSameSchool);

        const questions: AddQuestionToWorksheetDto[] = [{
          type: EQuestionType.MULTIPLE_CHOICE,
          content: 'School manager test question',
          options: ['A', 'B', 'C', 'D'],
          answer: ['A'],
          explain: 'School manager explanation',
          difficulty: EQuestionDifficulty.EASY
        }];

        const result = await service.bulkAddQuestions(
          'worksheet-1',
          questions,
          schoolManagerUser
        );

        expect(result.success).toBe(true);
        expect(result.successCount).toBe(1);
      });

      it('should prevent school manager from modifying questions in other schools', async () => {
        const schoolManagerUser = createUserContext(EUserRole.SCHOOL_MANAGER, 'school-a');
        const worksheetFromOtherSchool = createMockWorksheet('school-b', 'teacher-in-school-b');
        
        worksheetRepository.findOne.mockResolvedValue(worksheetFromOtherSchool);

        const questions: AddQuestionToWorksheetDto[] = [{
          type: EQuestionType.MULTIPLE_CHOICE,
          content: 'Unauthorized question',
          options: ['A', 'B', 'C', 'D'],
          answer: ['A'],
          explain: 'This should fail',
          difficulty: EQuestionDifficulty.EASY
        }];

        await expect(
          service.bulkAddQuestions('worksheet-1', questions, schoolManagerUser)
        ).rejects.toThrow(ForbiddenException);
      });

      it('should prevent school manager without school assignment from any operations', async () => {
        const schoolManagerWithoutSchool = createUserContext(EUserRole.SCHOOL_MANAGER); // No schoolId
        const anyWorksheet = createMockWorksheet('school-a', 'teacher-in-school-a');
        
        worksheetRepository.findOne.mockResolvedValue(anyWorksheet);

        const questions: AddQuestionToWorksheetDto[] = [{
          type: EQuestionType.MULTIPLE_CHOICE,
          content: 'Unauthorized question',
          options: ['A', 'B', 'C', 'D'],
          answer: ['A'],
          explain: 'This should fail',
          difficulty: EQuestionDifficulty.EASY
        }];

        await expect(
          service.bulkAddQuestions('worksheet-1', questions, schoolManagerWithoutSchool)
        ).rejects.toThrow(ForbiddenException);
      });
    });

    describe('Teacher Access', () => {
      it('should allow teacher to bulk modify questions in their school worksheets', async () => {
        const teacherUser = createUserContext(EUserRole.TEACHER, 'school-a');
        const worksheetFromSameSchool = createMockWorksheet('school-a', 'other-teacher');
        
        worksheetRepository.findOne.mockResolvedValue(worksheetFromSameSchool);
        worksheetRepository.save.mockResolvedValue(worksheetFromSameSchool);

        const questions: AddQuestionToWorksheetDto[] = [{
          type: EQuestionType.MULTIPLE_CHOICE,
          content: 'Teacher test question',
          options: ['A', 'B', 'C', 'D'],
          answer: ['A'],
          explain: 'Teacher explanation',
          difficulty: EQuestionDifficulty.EASY
        }];

        const result = await service.bulkAddQuestions(
          'worksheet-1',
          questions,
          teacherUser
        );

        expect(result.success).toBe(true);
        expect(result.successCount).toBe(1);
      });

      it('should prevent teacher from modifying questions in other schools', async () => {
        const teacherUser = createUserContext(EUserRole.TEACHER, 'school-a');
        const worksheetFromOtherSchool = createMockWorksheet('school-b', 'teacher-in-school-b');
        
        worksheetRepository.findOne.mockResolvedValue(worksheetFromOtherSchool);

        const questions: AddQuestionToWorksheetDto[] = [{
          type: EQuestionType.MULTIPLE_CHOICE,
          content: 'Unauthorized question',
          options: ['A', 'B', 'C', 'D'],
          answer: ['A'],
          explain: 'This should fail',
          difficulty: EQuestionDifficulty.EASY
        }];

        await expect(
          service.bulkAddQuestions('worksheet-1', questions, teacherUser)
        ).rejects.toThrow(ForbiddenException);
      });
    });

    describe('Independent Teacher Access', () => {
      it('should allow independent teacher to bulk modify their own worksheets', async () => {
        const independentTeacherUser = createUserContext(EUserRole.INDEPENDENT_TEACHER, 'independent-school', 'independent-teacher-123');
        const ownWorksheet = createMockWorksheet('independent-school', 'independent-teacher-123');
        
        worksheetRepository.findOne.mockResolvedValue(ownWorksheet);
        worksheetRepository.save.mockResolvedValue(ownWorksheet);

        const questions: AddQuestionToWorksheetDto[] = [{
          type: EQuestionType.MULTIPLE_CHOICE,
          content: 'Independent teacher question',
          options: ['A', 'B', 'C', 'D'],
          answer: ['A'],
          explain: 'Independent teacher explanation',
          difficulty: EQuestionDifficulty.EASY
        }];

        const result = await service.bulkAddQuestions(
          'worksheet-1',
          questions,
          independentTeacherUser
        );

        expect(result.success).toBe(true);
        expect(result.successCount).toBe(1);
      });

      it('should prevent independent teacher from modifying other users worksheets', async () => {
        const independentTeacherUser = createUserContext(EUserRole.INDEPENDENT_TEACHER, 'independent-school', 'independent-teacher-123');
        const otherUsersWorksheet = createMockWorksheet('independent-school', 'other-independent-teacher');
        
        worksheetRepository.findOne.mockResolvedValue(otherUsersWorksheet);

        const questions: AddQuestionToWorksheetDto[] = [{
          type: EQuestionType.MULTIPLE_CHOICE,
          content: 'Unauthorized question',
          options: ['A', 'B', 'C', 'D'],
          answer: ['A'],
          explain: 'This should fail',
          difficulty: EQuestionDifficulty.EASY
        }];

        await expect(
          service.bulkAddQuestions('worksheet-1', questions, independentTeacherUser)
        ).rejects.toThrow(ForbiddenException);
      });
    });
  });

  describe('Data Isolation and Security', () => {
    it('should prevent cross-school data leakage in bulk operations', async () => {
      const schoolATeacher = createUserContext(EUserRole.TEACHER, 'school-a');
      const schoolBWorksheet = createMockWorksheet('school-b', 'school-b-teacher');
      
      worksheetRepository.findOne.mockResolvedValue(schoolBWorksheet);

      // Try to add questions to school B worksheet as school A teacher
      const questions: AddQuestionToWorksheetDto[] = [{
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'Cross-school question',
        options: ['A', 'B', 'C', 'D'],
        answer: ['A'],
        explain: 'This should not be allowed',
        difficulty: EQuestionDifficulty.EASY
      }];

      await expect(
        service.bulkAddQuestions('worksheet-1', questions, schoolATeacher)
      ).rejects.toThrow(ForbiddenException);

      // Try to remove questions from school B worksheet
      await expect(
        service.bulkRemoveQuestions('worksheet-1', ['question-1'], schoolATeacher)
      ).rejects.toThrow(ForbiddenException);

      // Try to update questions in school B worksheet
      await expect(
        service.bulkUpdateQuestions('worksheet-1', [
          { questionId: 'question-1', updates: { content: 'Updated content' } }
        ], schoolATeacher)
      ).rejects.toThrow(ForbiddenException);
    });

    it('should ensure school ID is properly set on new questions', async () => {
      const teacherUser = createUserContext(EUserRole.TEACHER, 'school-a');
      const worksheet = createMockWorksheet('school-a', 'teacher-123');
      
      worksheetRepository.findOne.mockResolvedValue(worksheet);
      worksheetRepository.save.mockImplementation((savedWorksheet) => {
        // Verify that new questions have the correct school ID
        const newQuestions = savedWorksheet.questions.slice(1); // Skip existing question
        newQuestions.forEach(question => {
          expect(question.schoolId).toBe('school-a');
        });
        return savedWorksheet;
      });

      const questions: AddQuestionToWorksheetDto[] = [{
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'School isolation test',
        options: ['A', 'B', 'C', 'D'],
        answer: ['A'],
        explain: 'This question should have school ID set',
        difficulty: EQuestionDifficulty.EASY
      }];

      await service.bulkAddQuestions('worksheet-1', questions, teacherUser);

      expect(worksheetRepository.save).toHaveBeenCalled();
    });
  });

  describe('Input Validation and Sanitization', () => {
    it('should prevent injection attacks in question content', async () => {
      const teacherUser = createUserContext(EUserRole.TEACHER, 'school-a');
      const worksheet = createMockWorksheet('school-a', 'teacher-123');
      
      worksheetRepository.findOne.mockResolvedValue(worksheet);

      const maliciousQuestions: AddQuestionToWorksheetDto[] = [{
        type: EQuestionType.MULTIPLE_CHOICE,
        content: '<script>alert("XSS")</script>What is 2+2?',
        options: ['<script>alert("XSS")</script>4', '5', '6', '7'],
        answer: ['<script>alert("XSS")</script>4'],
        explain: '<script>alert("XSS")</script>Explanation',
        difficulty: EQuestionDifficulty.EASY
      }];

      // The service should handle this gracefully without executing scripts
      // In a real implementation, content would be sanitized
      const result = await service.bulkAddQuestions(
        'worksheet-1',
        maliciousQuestions,
        teacherUser
      );

      // The operation should succeed but content should be sanitized
      expect(result.success).toBe(true);
    });

    it('should enforce question limits to prevent resource exhaustion', async () => {
      const teacherUser = createUserContext(EUserRole.TEACHER, 'school-a');
      const worksheet = createMockWorksheet('school-a', 'teacher-123');
      
      worksheetRepository.findOne.mockResolvedValue(worksheet);

      // Try to add more questions than the limit
      const tooManyQuestions: AddQuestionToWorksheetDto[] = Array(200).fill({
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'Resource exhaustion test',
        options: ['A', 'B', 'C', 'D'],
        answer: ['A'],
        explain: 'This should be limited',
        difficulty: EQuestionDifficulty.EASY
      });

      await expect(
        service.bulkAddQuestions('worksheet-1', tooManyQuestions, teacherUser)
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('Audit Trail Security', () => {
    it('should log all bulk operations for security auditing', async () => {
      const teacherUser = createUserContext(EUserRole.TEACHER, 'school-a');
      const worksheet = createMockWorksheet('school-a', 'teacher-123');
      
      worksheetRepository.findOne.mockResolvedValue(worksheet);
      worksheetRepository.save.mockResolvedValue(worksheet);

      const auditService = service['auditService'];
      const logSpy = jest.spyOn(auditService, 'logBulkQuestionOperation');

      const questions: AddQuestionToWorksheetDto[] = [{
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'Audit test question',
        options: ['A', 'B', 'C', 'D'],
        answer: ['A'],
        explain: 'Audit explanation',
        difficulty: EQuestionDifficulty.EASY
      }];

      await service.bulkAddQuestions('worksheet-1', questions, teacherUser);

      expect(logSpy).toHaveBeenCalledWith(
        'worksheet-1',
        'bulk_add',
        teacherUser,
        expect.objectContaining({
          successCount: 1,
          failureCount: 0
        })
      );
    });
  });
});
