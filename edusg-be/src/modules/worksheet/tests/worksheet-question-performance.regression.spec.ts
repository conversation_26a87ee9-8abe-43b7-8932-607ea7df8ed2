import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { WorksheetQuestionPerformanceTestingService, PerformanceTestConfig } from '../services/worksheet-question-performance-testing.service';
import { WorksheetQuestionMetricsService } from '../services/worksheet-question-metrics.service';
import { WorksheetQuestionMemoryOptimizationService } from '../services/worksheet-question-memory-optimization.service';
import { WorksheetRedisCacheService } from '../services/worksheet-redis-cache.service';
import { WorksheetQuestionDocument, WorksheetQuestionDocumentSchema } from '../../mongodb/schemas/worksheet-question-document.schema';

/**
 * Performance regression testing suite for worksheet question management
 * This test suite runs automated performance benchmarks and compares against baseline metrics
 * to detect performance regressions in the system.
 */
describe('WorksheetQuestionPerformance - Regression Tests', () => {
  let app: INestApplication;
  let performanceTestingService: WorksheetQuestionPerformanceTestingService;
  let metricsService: WorksheetQuestionMetricsService;
  
  // Performance baseline thresholds (these should be updated based on actual system performance)
  const PERFORMANCE_BASELINES = {
    basicRetrieval: {
      maxAverageResponseTime: 150, // ms
      minThroughput: 40, // requests/second
      maxErrorRate: 1, // percentage
    },
    bulkOperations: {
      maxAverageResponseTime: 800, // ms
      minThroughput: 15, // requests/second
      maxErrorRate: 2, // percentage
    },
    memoryOptimization: {
      maxAverageResponseTime: 300, // ms
      maxMemoryIncrease: 50, // MB
      maxErrorRate: 1, // percentage
    },
    cachePerformance: {
      maxAverageResponseTime: 50, // ms
      minCacheHitRate: 80, // percentage
      minThroughput: 80, // requests/second
    }
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        MongooseModule.forRootAsync({
          useFactory: (configService: ConfigService) => ({
            uri: configService.get<string>('MONGODB_TEST_URI') || 'mongodb://localhost:27017/edusg_test',
          }),
          inject: [ConfigService],
        }),
        MongooseModule.forFeature([
          { name: WorksheetQuestionDocument.name, schema: WorksheetQuestionDocumentSchema },
        ]),
      ],
      providers: [
        WorksheetQuestionPerformanceTestingService,
        WorksheetQuestionMetricsService,
        WorksheetQuestionMemoryOptimizationService,
        WorksheetRedisCacheService,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    performanceTestingService = moduleFixture.get<WorksheetQuestionPerformanceTestingService>(
      WorksheetQuestionPerformanceTestingService
    );
    metricsService = moduleFixture.get<WorksheetQuestionMetricsService>(WorksheetQuestionMetricsService);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Performance Regression Detection', () => {
    it('should maintain basic question retrieval performance within baseline', async () => {
      const config: PerformanceTestConfig = {
        testName: 'Basic Question Retrieval',
        description: 'Regression test for basic question retrieval performance',
        iterations: 50,
        concurrency: 5,
        warmupIterations: 5,
        dataSize: 'medium',
        cacheEnabled: true,
        timeout: 30000,
      };

      const result = await performanceTestingService.runPerformanceTest(config);
      const baseline = PERFORMANCE_BASELINES.basicRetrieval;

      // Assert performance metrics are within acceptable ranges
      expect(result.results.averageResponseTime).toBeLessThanOrEqual(baseline.maxAverageResponseTime);
      expect(result.results.throughput).toBeGreaterThanOrEqual(baseline.minThroughput);
      expect(result.results.errorRate).toBeLessThanOrEqual(baseline.maxErrorRate);
      expect(result.results.successRate).toBeGreaterThanOrEqual(95);

      // Log performance metrics for monitoring
      console.log(`Basic Retrieval Performance:
        Average Response Time: ${result.results.averageResponseTime}ms (baseline: ${baseline.maxAverageResponseTime}ms)
        Throughput: ${result.results.throughput} req/s (baseline: ${baseline.minThroughput} req/s)
        Error Rate: ${result.results.errorRate}% (baseline: ${baseline.maxErrorRate}%)
        Success Rate: ${result.results.successRate}%`);
    }, 60000);

    it('should maintain bulk operations performance within baseline', async () => {
      const config: PerformanceTestConfig = {
        testName: 'Bulk Question Operations',
        description: 'Regression test for bulk operations performance',
        iterations: 20,
        concurrency: 3,
        dataSize: 'large',
        backgroundProcessing: true,
        timeout: 60000,
      };

      const result = await performanceTestingService.runPerformanceTest(config);
      const baseline = PERFORMANCE_BASELINES.bulkOperations;

      expect(result.results.averageResponseTime).toBeLessThanOrEqual(baseline.maxAverageResponseTime);
      expect(result.results.throughput).toBeGreaterThanOrEqual(baseline.minThroughput);
      expect(result.results.errorRate).toBeLessThanOrEqual(baseline.maxErrorRate);

      console.log(`Bulk Operations Performance:
        Average Response Time: ${result.results.averageResponseTime}ms (baseline: ${baseline.maxAverageResponseTime}ms)
        Throughput: ${result.results.throughput} req/s (baseline: ${baseline.minThroughput} req/s)
        Error Rate: ${result.results.errorRate}% (baseline: ${baseline.maxErrorRate}%)`);
    }, 120000);

    it('should maintain memory optimization performance within baseline', async () => {
      const config: PerformanceTestConfig = {
        testName: 'Memory Optimization',
        description: 'Regression test for memory optimization performance',
        iterations: 15,
        concurrency: 2,
        dataSize: 'xlarge',
        timeout: 90000,
      };

      const result = await performanceTestingService.runPerformanceTest(config);
      const baseline = PERFORMANCE_BASELINES.memoryOptimization;

      const memoryIncrease = (result.results.memoryUsage.peak.heapUsed - result.results.memoryUsage.before.heapUsed) / (1024 * 1024);

      expect(result.results.averageResponseTime).toBeLessThanOrEqual(baseline.maxAverageResponseTime);
      expect(memoryIncrease).toBeLessThanOrEqual(baseline.maxMemoryIncrease);
      expect(result.results.errorRate).toBeLessThanOrEqual(baseline.maxErrorRate);

      console.log(`Memory Optimization Performance:
        Average Response Time: ${result.results.averageResponseTime}ms (baseline: ${baseline.maxAverageResponseTime}ms)
        Memory Increase: ${memoryIncrease.toFixed(2)}MB (baseline: ${baseline.maxMemoryIncrease}MB)
        Error Rate: ${result.results.errorRate}% (baseline: ${baseline.maxErrorRate}%)`);
    }, 180000);

    it('should maintain cache performance within baseline', async () => {
      const config: PerformanceTestConfig = {
        testName: 'Cache Performance',
        description: 'Regression test for cache performance',
        iterations: 100,
        concurrency: 10,
        dataSize: 'medium',
        cacheEnabled: true,
        timeout: 45000,
      };

      const result = await performanceTestingService.runPerformanceTest(config);
      const baseline = PERFORMANCE_BASELINES.cachePerformance;

      expect(result.results.averageResponseTime).toBeLessThanOrEqual(baseline.maxAverageResponseTime);
      expect(result.results.throughput).toBeGreaterThanOrEqual(baseline.minThroughput);
      
      if (result.results.cacheMetrics) {
        expect(result.results.cacheMetrics.hitRate).toBeGreaterThanOrEqual(baseline.minCacheHitRate);
      }

      console.log(`Cache Performance:
        Average Response Time: ${result.results.averageResponseTime}ms (baseline: ${baseline.maxAverageResponseTime}ms)
        Throughput: ${result.results.throughput} req/s (baseline: ${baseline.minThroughput} req/s)
        Cache Hit Rate: ${result.results.cacheMetrics?.hitRate || 'N/A'}% (baseline: ${baseline.minCacheHitRate}%)`);
    }, 90000);
  });

  describe('Performance Trend Analysis', () => {
    it('should generate performance trend report', async () => {
      const testResults = await performanceTestingService.runPerformanceTestSuite();
      const report = performanceTestingService.generatePerformanceReport(testResults);

      expect(report).toBeDefined();
      expect(report.summary).toBeDefined();
      expect(report.summary.totalTests).toBeGreaterThan(0);
      expect(report.summary.overallRecommendations).toBeDefined();

      // Log trend analysis
      console.log('Performance Trend Analysis:', JSON.stringify(report.summary, null, 2));
    }, 300000);
  });

  describe('Stress Testing Scenarios', () => {
    it('should handle high concurrency load without degradation', async () => {
      const config: PerformanceTestConfig = {
        testName: 'High Concurrency Stress Test',
        description: 'Stress test with high concurrency',
        iterations: 200,
        concurrency: 50,
        dataSize: 'large',
        timeout: 120000,
      };

      const result = await performanceTestingService.runPerformanceTest(config);

      // Under stress, we allow for some performance degradation but not failure
      expect(result.results.errorRate).toBeLessThanOrEqual(5); // Allow up to 5% error rate under stress
      expect(result.results.successRate).toBeGreaterThanOrEqual(95);
      expect(result.results.averageResponseTime).toBeLessThanOrEqual(2000); // 2 second max under stress

      console.log(`Stress Test Results:
        Average Response Time: ${result.results.averageResponseTime}ms
        Throughput: ${result.results.throughput} req/s
        Error Rate: ${result.results.errorRate}%
        Success Rate: ${result.results.successRate}%`);
    }, 240000);
  });
});
