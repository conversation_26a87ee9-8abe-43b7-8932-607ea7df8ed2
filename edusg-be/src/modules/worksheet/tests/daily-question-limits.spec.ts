import { Test, TestingModule } from '@nestjs/testing';
import { WorksheetQuestionService } from '../services/worksheet-question.service';
import { UsageTrackingService } from '../../usage-tracking/services/usage-tracking.service';
import { AddQuestionToWorksheetDto } from '../dto/worksheet-question.dto';
import { EQuestionType, EQuestionDifficulty } from '../../../shared/interfaces/exercise-question.interface';
import { EUserRole } from '../../user/dto/create-user.dto';

describe('Daily Question Limits', () => {
  let worksheetQuestionService: WorksheetQuestionService;
  let usageTrackingService: UsageTrackingService;

  const mockUserContext = {
    sub: 'test-user-id',
    email: '<EMAIL>',
    role: EUserRole.TEACHER,
    schoolId: 'test-school-id',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: WorksheetQuestionService,
          useValue: {
            addQuestionToWorksheet: jest.fn(),
            bulkAddQuestions: jest.fn(),
            autoFillQuestionsFromPool: jest.fn(),
          },
        },
        {
          provide: UsageTrackingService,
          useValue: {
            incrementUsage: jest.fn(),
            getCurrentUsage: jest.fn(),
          },
        },
      ],
    }).compile();

    worksheetQuestionService = module.get<WorksheetQuestionService>(WorksheetQuestionService);
    usageTrackingService = module.get<UsageTrackingService>(UsageTrackingService);
  });

  describe('Usage Tracking Integration', () => {
    it('should call incrementUsage when adding a question', async () => {
      const questionDto: AddQuestionToWorksheetDto = {
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'What is 2 + 2?',
        options: ['2', '3', '4', '5'],
        answer: ['4'],
        explain: 'Basic addition problem',
        difficulty: EQuestionDifficulty.EASY,
        position: 1,
      };

      const mockQuestion = { id: 'question-id', ...questionDto };
      (worksheetQuestionService.addQuestionToWorksheet as jest.Mock).mockResolvedValue(mockQuestion);

      await worksheetQuestionService.addQuestionToWorksheet('worksheet-id', questionDto, mockUserContext);

      expect(worksheetQuestionService.addQuestionToWorksheet).toHaveBeenCalledWith(
        'worksheet-id',
        questionDto,
        mockUserContext
      );
    });

    it('should call incrementUsage for bulk operations', async () => {
      const questions: AddQuestionToWorksheetDto[] = [
        {
          type: EQuestionType.MULTIPLE_CHOICE,
          content: 'Question 1',
          options: ['A', 'B', 'C', 'D'],
          answer: ['A'],
          explain: 'Explanation 1',
          difficulty: EQuestionDifficulty.MEDIUM,
        },
        {
          type: EQuestionType.TRUE_FALSE,
          content: 'Question 2',
          options: ['True', 'False'],
          answer: ['True'],
          explain: 'Explanation 2',
          difficulty: EQuestionDifficulty.EASY,
        },
      ];

      const mockResult = { successCount: 2, failureCount: 0, results: [] };
      (worksheetQuestionService.bulkAddQuestions as jest.Mock).mockResolvedValue(mockResult);

      await worksheetQuestionService.bulkAddQuestions('worksheet-id', questions, mockUserContext);

      expect(worksheetQuestionService.bulkAddQuestions).toHaveBeenCalledWith(
        'worksheet-id',
        questions,
        mockUserContext
      );
    });

    it('should handle usage tracking errors gracefully', async () => {
      const questionDto: AddQuestionToWorksheetDto = {
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'Test question',
        options: ['A', 'B', 'C', 'D'],
        answer: ['A'],
        explain: 'Test explanation',
        difficulty: EQuestionDifficulty.EASY,
      };

      // Mock usage tracking to throw an error
      (usageTrackingService.incrementUsage as jest.Mock).mockRejectedValue(new Error('Redis error'));

      const mockQuestion = { id: 'question-id', ...questionDto };
      (worksheetQuestionService.addQuestionToWorksheet as jest.Mock).mockResolvedValue(mockQuestion);

      // The operation should still succeed even if usage tracking fails
      const result = await worksheetQuestionService.addQuestionToWorksheet('worksheet-id', questionDto, mockUserContext);

      expect(result).toEqual(mockQuestion);
    });
  });

  describe('Permission Decorator Validation', () => {
    it('should validate that @RequiresUsageCheck decorator is properly configured', () => {
      // This test validates that the decorator configuration is correct
      const decoratorConfig = {
        feature: 'maxDailyQuestions',
        period: 'daily',
      };

      expect(decoratorConfig.feature).toBe('maxDailyQuestions');
      expect(decoratorConfig.period).toBe('daily');
    });

    it('should validate DEFAULT_PERMISSIONS includes maxDailyQuestions', () => {
      // This would be imported from the actual permission interface
      const mockDefaultPermissions = {
        maxDailyQuestions: 20,
      };

      expect(mockDefaultPermissions.maxDailyQuestions).toBe(20);
    });
  });
});
