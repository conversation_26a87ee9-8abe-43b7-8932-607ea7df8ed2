import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { WorksheetQuestionService } from '../services/worksheet-question.service';
import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetPromptResult } from '../../mongodb/schemas/worksheet-prompt-result.schema';
import { NotFoundException } from '@nestjs/common';

describe('WorksheetQuestionService - MongoDB Sync', () => {
  let service: WorksheetQuestionService;
  let worksheetRepository: Repository<Worksheet>;
  let worksheetPromptResultModel: any;

  const mockWorksheet = {
    id: 'test-worksheet-id',
    questionIds: ['question-1', 'question-2', 'question-3'],
    schoolId: 'test-school-id'
  };

  const mockMongoDoc = {
    worksheetId: 'test-worksheet-id',
    promptResult: {
      result: [
        { id: 'question-1', type: 'multiple_choice', content: 'Question 1' },
        { id: 'question-2', type: 'true_false', content: 'Question 2' },
        { id: 'question-3', type: 'short_answer', content: 'Question 3' }
      ]
    },
    currentQuestionCount: 3,
    totalQuestionCount: 3,
    save: jest.fn().mockResolvedValue(true)
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorksheetQuestionService,
        {
          provide: getRepositoryToken(Worksheet),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn()
          }
        },
        {
          provide: getModelToken(WorksheetPromptResult.name),
          useValue: {
            findOne: jest.fn(),
            updateOne: jest.fn()
          }
        },
        // Mock other dependencies
        {
          provide: 'WorksheetQuestionEnhancedCacheService',
          useValue: {
            cacheWorksheetQuestions: jest.fn(),
            invalidateWorksheetCache: jest.fn()
          }
        },
        {
          provide: 'WorksheetQuestionAuditService',
          useValue: {
            logQuestionRemoved: jest.fn()
          }
        },
        {
          provide: 'WorksheetQuestionLockingService',
          useValue: {
            canEditQuestion: jest.fn().mockResolvedValue(true)
          }
        },
        {
          provide: 'WorksheetQuestionCollaborationGateway',
          useValue: {
            broadcastQuestionUpdate: jest.fn()
          }
        },
        {
          provide: 'WorksheetQuestionSocketGateway',
          useValue: {
            server: {
              to: jest.fn().mockReturnValue({
                emit: jest.fn()
              })
            }
          }
        }
      ]
    }).compile();

    service = module.get<WorksheetQuestionService>(WorksheetQuestionService);
    worksheetRepository = module.get<Repository<Worksheet>>(getRepositoryToken(Worksheet));
    worksheetPromptResultModel = module.get(getModelToken(WorksheetPromptResult.name));
  });

  describe('removeQuestionFromPromptResult', () => {
    it('should throw NotFoundException when WorksheetPromptResult document not found', async () => {
      // Arrange
      worksheetPromptResultModel.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service['removeQuestionFromPromptResult']('test-worksheet-id', 'question-1')
      ).rejects.toThrow(NotFoundException);
      
      expect(worksheetPromptResultModel.findOne).toHaveBeenCalledWith({
        worksheetId: 'test-worksheet-id'
      });
    });

    it('should throw NotFoundException when questions array is missing', async () => {
      // Arrange
      const docWithoutQuestions = {
        worksheetId: 'test-worksheet-id',
        promptResult: null
      };
      worksheetPromptResultModel.findOne.mockResolvedValue(docWithoutQuestions);

      // Act & Assert
      await expect(
        service['removeQuestionFromPromptResult']('test-worksheet-id', 'question-1')
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException when question ID not found in MongoDB', async () => {
      // Arrange
      worksheetPromptResultModel.findOne.mockResolvedValue(mockMongoDoc);

      // Act & Assert
      await expect(
        service['removeQuestionFromPromptResult']('test-worksheet-id', 'non-existent-question')
      ).rejects.toThrow(NotFoundException);
    });

    it('should successfully remove question when found', async () => {
      // Arrange
      const mockDoc = {
        ...mockMongoDoc,
        promptResult: {
          result: [
            { id: 'question-1', type: 'multiple_choice', content: 'Question 1' },
            { id: 'question-2', type: 'true_false', content: 'Question 2' },
            { id: 'question-3', type: 'short_answer', content: 'Question 3' }
          ]
        }
      };
      worksheetPromptResultModel.findOne.mockResolvedValue(mockDoc);

      // Act
      const result = await service['removeQuestionFromPromptResult']('test-worksheet-id', 'question-2');

      // Assert
      expect(result).toEqual({ id: 'question-2', type: 'true_false', content: 'Question 2' });
      expect(mockDoc.promptResult.result).toHaveLength(2);
      expect(mockDoc.promptResult.result.find(q => q.id === 'question-2')).toBeUndefined();
      expect(mockDoc.currentQuestionCount).toBe(2);
      expect(mockDoc.totalQuestionCount).toBe(2);
      expect(mockDoc.save).toHaveBeenCalled();
    });

    it('should handle questions with missing ID fields', async () => {
      // Arrange
      const mockDocWithMissingIds = {
        ...mockMongoDoc,
        promptResult: {
          result: [
            { id: 'question-1', type: 'multiple_choice', content: 'Question 1' },
            { type: 'true_false', content: 'Question without ID' }, // Missing ID
            { id: 'question-3', type: 'short_answer', content: 'Question 3' }
          ]
        }
      };
      worksheetPromptResultModel.findOne.mockResolvedValue(mockDocWithMissingIds);

      // Act & Assert
      await expect(
        service['removeQuestionFromPromptResult']('test-worksheet-id', 'question-2')
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('findQuestionInPromptResult', () => {
    it('should return null when document not found', async () => {
      // Arrange
      worksheetPromptResultModel.findOne.mockResolvedValue(null);

      // Act
      const result = await service['findQuestionInPromptResult']('test-worksheet-id', 'question-1');

      // Assert
      expect(result).toBeNull();
    });

    it('should return null when questions array is missing', async () => {
      // Arrange
      const docWithoutQuestions = {
        worksheetId: 'test-worksheet-id',
        promptResult: null
      };
      worksheetPromptResultModel.findOne.mockResolvedValue(docWithoutQuestions);

      // Act
      const result = await service['findQuestionInPromptResult']('test-worksheet-id', 'question-1');

      // Assert
      expect(result).toBeNull();
    });

    it('should return null when question not found', async () => {
      // Arrange
      worksheetPromptResultModel.findOne.mockResolvedValue(mockMongoDoc);

      // Act
      const result = await service['findQuestionInPromptResult']('test-worksheet-id', 'non-existent');

      // Assert
      expect(result).toBeNull();
    });

    it('should return question data when found', async () => {
      // Arrange
      worksheetPromptResultModel.findOne.mockResolvedValue(mockMongoDoc);

      // Act
      const result = await service['findQuestionInPromptResult']('test-worksheet-id', 'question-2');

      // Assert
      expect(result).not.toBeNull();
      expect(result!.question).toEqual({ id: 'question-2', type: 'true_false', content: 'Question 2' });
      expect(result!.index).toBe(1);
      expect(result!.promptResult).toBe(mockMongoDoc);
    });
  });

  describe('MongoDB Count Consistency', () => {
    it('should update currentQuestionCount and totalQuestionCount when removing questions', async () => {
      // Arrange
      const mockDoc = {
        ...mockMongoDoc,
        promptResult: {
          result: [
            { id: 'question-1', type: 'multiple_choice', content: 'Question 1' },
            { id: 'question-2', type: 'true_false', content: 'Question 2' },
            { id: 'question-3', type: 'short_answer', content: 'Question 3' }
          ]
        },
        currentQuestionCount: 3,
        totalQuestionCount: 3
      };
      worksheetPromptResultModel.findOne.mockResolvedValue(mockDoc);

      // Act
      await service['removeQuestionFromPromptResult']('test-worksheet-id', 'question-2');

      // Assert
      expect(mockDoc.currentQuestionCount).toBe(2);
      expect(mockDoc.totalQuestionCount).toBe(2);
      expect(mockDoc.promptResult.result).toHaveLength(2);
      expect(mockDoc.save).toHaveBeenCalled();
    });

    it('should update counts when bulk removing questions', async () => {
      // Arrange
      const mockDoc = {
        ...mockMongoDoc,
        promptResult: {
          result: [
            { id: 'question-1', type: 'multiple_choice', content: 'Question 1' },
            { id: 'question-2', type: 'true_false', content: 'Question 2' },
            { id: 'question-3', type: 'short_answer', content: 'Question 3' },
            { id: 'question-4', type: 'calculation', content: 'Question 4' }
          ]
        },
        currentQuestionCount: 4,
        totalQuestionCount: 4
      };
      worksheetPromptResultModel.findOne.mockResolvedValue(mockDoc);

      // Act
      const result = await service['bulkRemoveQuestionsFromPromptResult'](
        'test-worksheet-id',
        ['question-2', 'question-4']
      );

      // Assert
      expect(result).toEqual(['question-2', 'question-4']);
      expect(mockDoc.currentQuestionCount).toBe(2);
      expect(mockDoc.totalQuestionCount).toBe(2);
      expect(mockDoc.promptResult.result).toHaveLength(2);
      expect(mockDoc.promptResult.result.map(q => q.id)).toEqual(['question-1', 'question-3']);
      expect(mockDoc.save).toHaveBeenCalled();
    });

    it('should detect and fix count inconsistencies', async () => {
      // Arrange - Mock inconsistent counts
      const mockDoc = {
        worksheetId: 'test-worksheet-id',
        promptResult: {
          result: [
            { id: 'question-1', type: 'multiple_choice', content: 'Question 1' },
            { id: 'question-2', type: 'true_false', content: 'Question 2' }
          ]
        },
        currentQuestionCount: 5, // Inconsistent - should be 2
        totalQuestionCount: 3    // Inconsistent - should be 2
      };
      worksheetPromptResultModel.findOne.mockResolvedValue(mockDoc);

      // Act
      await service['ensureMongoDBCountsConsistent']('test-worksheet-id');

      // Assert
      expect(worksheetPromptResultModel.updateOne).toHaveBeenCalledWith(
        { worksheetId: 'test-worksheet-id' },
        {
          $set: {
            currentQuestionCount: 2,
            totalQuestionCount: 2,
            updatedAt: expect.any(Date)
          }
        }
      );
    });
  });
});
