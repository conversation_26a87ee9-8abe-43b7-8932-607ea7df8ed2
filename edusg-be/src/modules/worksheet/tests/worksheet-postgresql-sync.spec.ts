import { Test, TestingModule } from '@nestjs/testing';
import { WorksheetGenerateConsumer } from '../queue.consumer';
import { WorksheetService } from '../worksheet.service';
import { Model } from 'mongoose';
import { WorksheetPromptResult } from '../../mongodb/schemas/worksheet-prompt-result.schema';
import { getModelToken } from '@nestjs/mongoose';
import { DocumentsService } from '../../documents/documents.service';
import { PromptService } from '../../prompt/services/prompt.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { BatchImageService } from '../../gen-image/batch-image.service';
import { WorksheetDocumentCacheService } from '../services/worksheet-document-cache.service';
import { QuestionPoolService } from '../../question-pool/question-pool.service';
import { QuestionPoolConfigService } from '../../question-pool/question-pool-config.service';
import { ContentValidationService } from '../../validation/content-validation.service';
import { AiOrchestrationService } from '../../ai/ai-orchestration.service';
import { PoolMonitoringService } from '../../monitoring/services/pool-monitoring.service';

describe('WorksheetGenerateConsumer - PostgreSQL Sync', () => {
  let consumer: WorksheetGenerateConsumer;
  let worksheetService: jest.Mocked<WorksheetService>;
  let worksheetPromptResultModel: jest.Mocked<Model<WorksheetPromptResult>>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorksheetGenerateConsumer,
        {
          provide: WorksheetService,
          useValue: {
            updateQuestionData: jest.fn(),
            updateStatus: jest.fn(),
          },
        },
        {
          provide: getModelToken(WorksheetPromptResult.name),
          useValue: {
            findOneAndUpdate: jest.fn(),
          },
        },
        // Mock other dependencies with proper class tokens
        {
          provide: DocumentsService,
          useValue: {},
        },
        {
          provide: PromptService,
          useValue: {},
        },
        {
          provide: SocketGateway,
          useValue: {},
        },
        {
          provide: BatchImageService,
          useValue: {},
        },
        {
          provide: WorksheetDocumentCacheService,
          useValue: {},
        },
        {
          provide: QuestionPoolService,
          useValue: {},
        },
        {
          provide: QuestionPoolConfigService,
          useValue: {},
        },
        {
          provide: ContentValidationService,
          useValue: {},
        },
        {
          provide: AiOrchestrationService,
          useValue: {},
        },
        {
          provide: PoolMonitoringService,
          useValue: {},
        },
      ],
    }).compile();

    consumer = module.get<WorksheetGenerateConsumer>(WorksheetGenerateConsumer);
    worksheetService = module.get(WorksheetService);
    worksheetPromptResultModel = module.get(getModelToken(WorksheetPromptResult.name));
  });

  describe('syncWorksheetQuestionDataToPostgreSQL', () => {
    it('should successfully sync question data to PostgreSQL', async () => {
      const worksheetId = 'test-worksheet-123';
      const mockQuestions = [
        {
          type: 'multiple_choice',
          content: 'What is 2+2?',
          options: ['2', '3', '4', '5'],
          answer: ['4'],
          explain: '2+2 equals 4'
        },
        {
          type: 'fill_blank',
          content: 'The capital of France is ____',
          options: [],
          answer: ['Paris'],
          explain: 'Paris is the capital city of France'
        }
      ];

      // Mock the updateQuestionData method
      worksheetService.updateQuestionData.mockResolvedValue({} as any);

      // Call the private method using reflection
      await (consumer as any).syncWorksheetQuestionDataToPostgreSQL(worksheetId, mockQuestions);

      // Verify that updateQuestionData was called with correct parameters
      expect(worksheetService.updateQuestionData).toHaveBeenCalledWith(
        worksheetId,
        [`${worksheetId}_q1`, `${worksheetId}_q2`], // Expected questionIds
        2, // totalQuestions
        expect.objectContaining({
          lastQuestionUpdate: expect.any(Date),
          questionVersion: 1,
          hasUnsavedChanges: false,
          collaborators: [],
          lockStatus: {
            isLocked: false
          }
        })
      );
    });

    it('should handle empty questions array', async () => {
      const worksheetId = 'test-worksheet-empty';
      const mockQuestions: any[] = [];

      worksheetService.updateQuestionData.mockResolvedValue({} as any);

      await (consumer as any).syncWorksheetQuestionDataToPostgreSQL(worksheetId, mockQuestions);

      expect(worksheetService.updateQuestionData).toHaveBeenCalledWith(
        worksheetId,
        [], // Empty questionIds array
        0, // totalQuestions
        expect.objectContaining({
          lastQuestionUpdate: expect.any(Date),
          questionVersion: 1,
          hasUnsavedChanges: false,
          collaborators: [],
          lockStatus: {
            isLocked: false
          }
        })
      );
    });

    it('should throw error when worksheetService.updateQuestionData fails', async () => {
      const worksheetId = 'test-worksheet-error';
      const mockQuestions = [{ type: 'multiple_choice', content: 'Test question' }];

      worksheetService.updateQuestionData.mockRejectedValue(new Error('Database error'));

      await expect(
        (consumer as any).syncWorksheetQuestionDataToPostgreSQL(worksheetId, mockQuestions)
      ).rejects.toThrow('Database error');
    });
  });
});
