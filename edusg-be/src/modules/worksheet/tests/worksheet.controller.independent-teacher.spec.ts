import { ForbiddenException, NotFoundException } from '@nestjs/common';
import { WorksheetController } from '../worksheet.controller';
import { WorksheetService } from '../worksheet.service';
import { WorksheetCleanupService } from '../worksheet-cleanup.service';
import { EUserRole } from '../../user/dto/create-user.dto';
import { CreateWorksheetDto } from '../dto/create-worksheet.dto';
import { ListWorksheetDto } from '../dto/list-worksheets.dto';

describe('WorksheetController - INDEPENDENT_TEACHER Integration', () => {
  let controller: WorksheetController;
  let worksheetService: jest.Mocked<WorksheetService>;
  let worksheetCleanupService: jest.Mocked<WorksheetCleanupService>;
  let mockWorksheetDocumentModel: any;

  const mockIndependentTeacher = {
    sub: 'independent-teacher-id',
    email: '<EMAIL>',
    role: EUserRole.INDEPENDENT_TEACHER,
    schoolId: 'independent-school-id',
  };

  const mockOtherSchoolUser = {
    sub: 'other-user-id',
    email: '<EMAIL>',
    role: EUserRole.TEACHER,
    schoolId: 'other-school-id',
  };

  const mockWorksheet = {
    id: 'worksheet-id',
    title: 'Test Worksheet',
    description: 'Test Description',
    schoolId: 'independent-school-id',
    generatingStatus: 'Generated',
    subjectData: {},
    selectedOptions: [],
  };

  const mockOtherSchoolWorksheet = {
    id: 'other-worksheet-id',
    title: 'Other School Worksheet',
    description: 'Other Description',
    schoolId: 'other-school-id',
    generatingStatus: 'Generated',
    subjectData: {},
    selectedOptions: [],
  };

  beforeEach(() => {
    worksheetService = {
      create: jest.fn(),
      findAll: jest.fn(),
      findOne: jest.fn(),
      remove: jest.fn(),
    } as any;

    worksheetCleanupService = {
      warmCache: jest.fn(),
    } as any;

    mockWorksheetDocumentModel = {
      countDocuments: jest.fn(),
      aggregate: jest.fn(),
      findOne: jest.fn(),
    };

    // Create controller instance directly without NestJS DI
    controller = new WorksheetController(
      worksheetService,
      worksheetCleanupService,
      mockWorksheetDocumentModel
    );
  });

  describe('create', () => {
    it('should allow INDEPENDENT_TEACHER to create worksheets', async () => {
      const createDto: CreateWorksheetDto = {
        title: 'New Worksheet',
        description: 'New Description',
        options: [],
      };

      worksheetService.create.mockResolvedValue(mockWorksheet as any);

      const result = await controller.create(createDto, mockIndependentTeacher as any);

      expect(worksheetService.create).toHaveBeenCalledWith(createDto, mockIndependentTeacher);
      expect(result).toEqual(mockWorksheet);
    });

    it('should associate worksheet with INDEPENDENT_TEACHER schoolId', async () => {
      const createDto: CreateWorksheetDto = {
        title: 'New Worksheet',
        description: 'New Description',
        options: [],
      };

      worksheetService.create.mockResolvedValue({
        ...mockWorksheet,
        schoolId: mockIndependentTeacher.schoolId,
      } as any);

      await controller.create(createDto, mockIndependentTeacher as any);

      expect(worksheetService.create).toHaveBeenCalledWith(createDto, mockIndependentTeacher);
    });
  });

  describe('findAll', () => {
    it('should allow INDEPENDENT_TEACHER to list their own school worksheets', async () => {
      const listDto: ListWorksheetDto = { page: 1, pageSize: 10 };
      const mockResponse = {
        items: [mockWorksheet],
        meta: { total: 1, page: 1, pageSize: 10, totalPages: 1 },
      };

      worksheetService.findAll.mockResolvedValue(mockResponse as any);

      const result = await controller.findAll(listDto, mockIndependentTeacher as any);

      expect(worksheetService.findAll).toHaveBeenCalledWith(listDto, mockIndependentTeacher);
      expect(result).toEqual(mockResponse);
    });

    it('should only return worksheets from INDEPENDENT_TEACHER school', async () => {
      const listDto: ListWorksheetDto = { page: 1, pageSize: 10 };
      const mockResponse = {
        items: [mockWorksheet], // Only worksheets from their school
        meta: { total: 1, page: 1, pageSize: 10, totalPages: 1 },
      };

      worksheetService.findAll.mockResolvedValue(mockResponse as any);

      const result = await controller.findAll(listDto, mockIndependentTeacher as any);

      expect(result.items).toHaveLength(1);
      expect(result.items[0].schoolId).toBe(mockIndependentTeacher.schoolId);
    });
  });

  describe('findOne', () => {
    it('should allow INDEPENDENT_TEACHER to access their own school worksheets', async () => {
      worksheetService.findOne.mockResolvedValue(mockWorksheet as any);

      const result = await controller.findOne('worksheet-id', mockIndependentTeacher as any);

      expect(worksheetService.findOne).toHaveBeenCalledWith('worksheet-id', mockIndependentTeacher);
      expect(result).toEqual(mockWorksheet);
    });

    it('should prevent INDEPENDENT_TEACHER from accessing other school worksheets', async () => {
      worksheetService.findOne.mockResolvedValue(null); // Service returns null for unauthorized access

      const result = await controller.findOne('other-worksheet-id', mockIndependentTeacher as any);

      expect(result).toBeNull();
    });
  });

  describe('remove', () => {
    it('should allow INDEPENDENT_TEACHER to delete their own school worksheets', async () => {
      worksheetService.findOne.mockResolvedValue(mockWorksheet as any);
      worksheetService.remove.mockResolvedValue(undefined);

      await controller.remove('worksheet-id', mockIndependentTeacher as any);

      expect(worksheetService.findOne).toHaveBeenCalledWith('worksheet-id', mockIndependentTeacher, false);
      expect(worksheetService.remove).toHaveBeenCalledWith('worksheet-id', mockIndependentTeacher);
    });

    it('should prevent INDEPENDENT_TEACHER from deleting other school worksheets', async () => {
      worksheetService.findOne.mockResolvedValue(mockOtherSchoolWorksheet as any);

      await expect(
        controller.remove('other-worksheet-id', mockIndependentTeacher as any)
      ).rejects.toThrow(ForbiddenException);

      expect(worksheetService.remove).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException for non-existent worksheets', async () => {
      worksheetService.findOne.mockResolvedValue(null);

      await expect(
        controller.remove('non-existent-id', mockIndependentTeacher as any)
      ).rejects.toThrow(NotFoundException);

      expect(worksheetService.remove).not.toHaveBeenCalled();
    });
  });
});
