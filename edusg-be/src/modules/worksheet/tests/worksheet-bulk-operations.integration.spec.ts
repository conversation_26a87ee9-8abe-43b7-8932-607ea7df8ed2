import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import * as request from 'supertest';
import { WorksheetController } from '../worksheet.controller';
import { WorksheetService } from '../worksheet.service';
import { WorksheetQuestionService } from '../services/worksheet-question.service';
import { WorksheetQuestionAuditService } from '../services/worksheet-question-audit.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument, WorksheetQuestionDocumentSchema } from '../../mongodb/schemas/worksheet-question-document.schema';
import { BulkAddQuestionsDto, BulkRemoveQuestionsDto, BulkUpdateQuestionsDto } from '../dto/bulk-operations.dto';
import { EUserRole } from '../../user/dto/create-user.dto';
import { EQuestionType, EQuestionDifficulty } from '../../../shared/interfaces/exercise-question.interface';

describe('WorksheetBulkOperations - Integration Tests', () => {
  let app: INestApplication;
  let worksheetService: jest.Mocked<WorksheetService>;
  let worksheetQuestionService: jest.Mocked<WorksheetQuestionService>;
  let auditService: jest.Mocked<WorksheetQuestionAuditService>;
  let socketGateway: jest.Mocked<SocketGateway>;

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    role: EUserRole.TEACHER,
    schoolId: 'school-123'
  };

  const mockWorksheet = {
    id: 'worksheet-1',
    title: 'Test Worksheet',
    questions: [
      {
        id: 'question-1',
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'What is 2+2?',
        options: ['2', '3', '4', '5'],
        answer: ['4'],
        explain: '2+2 equals 4',
        audit: { version: 1 }
      }
    ],
    totalQuestions: 1,
    maxQuestions: 100,
    schoolId: 'school-123',
    createdBy: 'user-123'
  };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [Worksheet],
          synchronize: true,
        }),
        MongooseModule.forRoot('mongodb://localhost/test'),
        MongooseModule.forFeature([
          { name: WorksheetQuestionDocument.name, schema: WorksheetQuestionDocumentSchema }
        ])
      ],
      controllers: [WorksheetController],
      providers: [
        {
          provide: WorksheetService,
          useValue: {
            findOne: jest.fn(),
          }
        },
        {
          provide: WorksheetQuestionService,
          useValue: {
            bulkAddQuestions: jest.fn(),
            bulkRemoveQuestions: jest.fn(),
            bulkUpdateQuestions: jest.fn(),
          }
        },
        {
          provide: WorksheetQuestionAuditService,
          useValue: {
            logBulkQuestionOperation: jest.fn(),
          }
        },
        {
          provide: SocketGateway,
          useValue: {
            server: {
              to: jest.fn().mockReturnThis(),
              emit: jest.fn(),
            }
          }
        }
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    worksheetService = moduleFixture.get(WorksheetService);
    worksheetQuestionService = moduleFixture.get(WorksheetQuestionService);
    auditService = moduleFixture.get(WorksheetQuestionAuditService);
    socketGateway = moduleFixture.get(SocketGateway);
  });

  afterEach(async () => {
    await app.close();
  });

  describe('POST /worksheets/:id/questions/bulk-add', () => {
    it('should successfully bulk add questions', async () => {
      const bulkAddDto: BulkAddQuestionsDto = {
        questions: [
          {
            type: EQuestionType.MULTIPLE_CHOICE,
            content: 'What is 3+3?',
            options: ['5', '6', '7', '8'],
            answer: ['6'],
            explain: '3+3 equals 6',
            difficulty: EQuestionDifficulty.EASY
          },
          {
            type: EQuestionType.TRUE_FALSE,
            content: 'The sky is blue',
            options: ['True', 'False'],
            answer: ['True'],
            explain: 'The sky appears blue due to light scattering',
            difficulty: EQuestionDifficulty.EASY
          }
        ],
        reason: 'Adding test questions'
      };

      const mockResult = {
        success: true,
        successCount: 2,
        failureCount: 0,
        totalCount: 2,
        successes: [
          { id: 'question-2', content: 'What is 3+3?' },
          { id: 'question-3', content: 'The sky is blue' }
        ],
        failures: [],
        processingTimeMs: 150
      };

      worksheetQuestionService.bulkAddQuestions.mockResolvedValue(mockResult);

      const response = await request(app.getHttpServer())
        .post('/worksheets/worksheet-1/questions/bulk-add')
        .set('Authorization', 'Bearer mock-token')
        .send(bulkAddDto)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.successCount).toBe(2);
      expect(response.body.failureCount).toBe(0);
      expect(response.body.totalCount).toBe(2);
      expect(worksheetQuestionService.bulkAddQuestions).toHaveBeenCalledWith(
        'worksheet-1',
        bulkAddDto.questions,
        expect.objectContaining({
          sub: mockUser.id,
          email: mockUser.email,
          role: mockUser.role,
          schoolId: mockUser.schoolId
        }),
        expect.objectContaining({
          reason: 'Adding test questions'
        })
      );
    });

    it('should handle partial failures in bulk add', async () => {
      const bulkAddDto: BulkAddQuestionsDto = {
        questions: [
          {
            type: EQuestionType.MULTIPLE_CHOICE,
            content: 'Valid question',
            options: ['A', 'B', 'C', 'D'],
            answer: ['A'],
            explain: 'Valid explanation',
            difficulty: EQuestionDifficulty.EASY
          },
          {
            type: EQuestionType.MULTIPLE_CHOICE,
            content: '', // Invalid - empty content
            options: ['A', 'B'],
            answer: ['A'],
            explain: 'Invalid question',
            difficulty: EQuestionDifficulty.EASY
          }
        ]
      };

      const mockResult = {
        success: false,
        successCount: 1,
        failureCount: 1,
        totalCount: 2,
        successes: [{ id: 'question-2', content: 'Valid question' }],
        failures: [
          {
            item: bulkAddDto.questions[1],
            error: 'Question content cannot be empty',
            index: 1
          }
        ],
        processingTimeMs: 100
      };

      worksheetQuestionService.bulkAddQuestions.mockResolvedValue(mockResult);

      const response = await request(app.getHttpServer())
        .post('/worksheets/worksheet-1/questions/bulk-add')
        .set('Authorization', 'Bearer mock-token')
        .send(bulkAddDto)
        .expect(201);

      expect(response.body.success).toBe(false);
      expect(response.body.successCount).toBe(1);
      expect(response.body.failureCount).toBe(1);
      expect(response.body.failures).toHaveLength(1);
    });

    it('should reject bulk add when question limit would be exceeded', async () => {
      worksheetQuestionService.bulkAddQuestions.mockRejectedValue(
        new Error('Bulk addition would exceed question limit')
      );

      const bulkAddDto: BulkAddQuestionsDto = {
        questions: Array(101).fill({
          type: EQuestionType.MULTIPLE_CHOICE,
          content: 'Test question',
          options: ['A', 'B', 'C', 'D'],
          answer: ['A'],
          explain: 'Test explanation',
          difficulty: EQuestionDifficulty.EASY
        })
      };

      await request(app.getHttpServer())
        .post('/worksheets/worksheet-1/questions/bulk-add')
        .set('Authorization', 'Bearer mock-token')
        .send(bulkAddDto)
        .expect(500); // Should be handled by global exception filter
    });
  });

  describe('DELETE /worksheets/:id/questions/bulk-remove', () => {
    it('should successfully bulk remove questions', async () => {
      const bulkRemoveDto: BulkRemoveQuestionsDto = {
        questionIds: ['question-2', 'question-3'],
        reason: 'Removing test questions'
      };

      const mockResult = {
        success: true,
        successCount: 2,
        failureCount: 0,
        totalCount: 2,
        successes: ['question-2', 'question-3'],
        failures: [],
        processingTimeMs: 100
      };

      worksheetQuestionService.bulkRemoveQuestions.mockResolvedValue(mockResult);

      const response = await request(app.getHttpServer())
        .delete('/worksheets/worksheet-1/questions/bulk-remove')
        .set('Authorization', 'Bearer mock-token')
        .send(bulkRemoveDto)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.successCount).toBe(2);
      expect(response.body.successes).toEqual(['question-2', 'question-3']);
    });

    it('should handle partial failures in bulk remove', async () => {
      const bulkRemoveDto: BulkRemoveQuestionsDto = {
        questionIds: ['question-2', 'nonexistent-question']
      };

      const mockResult = {
        success: false,
        successCount: 1,
        failureCount: 1,
        totalCount: 2,
        successes: ['question-2'],
        failures: [
          {
            questionId: 'nonexistent-question',
            error: 'Question not found in worksheet',
            index: 1
          }
        ],
        processingTimeMs: 80
      };

      worksheetQuestionService.bulkRemoveQuestions.mockResolvedValue(mockResult);

      const response = await request(app.getHttpServer())
        .delete('/worksheets/worksheet-1/questions/bulk-remove')
        .set('Authorization', 'Bearer mock-token')
        .send(bulkRemoveDto)
        .expect(200);

      expect(response.body.success).toBe(false);
      expect(response.body.failureCount).toBe(1);
      expect(response.body.failures).toHaveLength(1);
    });
  });

  describe('PATCH /worksheets/:id/questions/bulk-update', () => {
    it('should successfully bulk update questions', async () => {
      const bulkUpdateDto: BulkUpdateQuestionsDto = {
        updates: [
          {
            questionId: 'question-1',
            updates: {
              content: 'Updated: What is 2+2?',
              version: 1
            }
          }
        ],
        reason: 'Updating question content'
      };

      const mockResult = {
        success: true,
        successCount: 1,
        failureCount: 0,
        totalCount: 1,
        successes: [
          {
            id: 'question-1',
            content: 'Updated: What is 2+2?',
            audit: { version: 2 }
          }
        ],
        failures: [],
        processingTimeMs: 120
      };

      worksheetQuestionService.bulkUpdateQuestions.mockResolvedValue(mockResult);

      const response = await request(app.getHttpServer())
        .patch('/worksheets/worksheet-1/questions/bulk-update')
        .set('Authorization', 'Bearer mock-token')
        .send(bulkUpdateDto)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.successCount).toBe(1);
      expect(response.body.successes[0].content).toBe('Updated: What is 2+2?');
    });
  });
});
