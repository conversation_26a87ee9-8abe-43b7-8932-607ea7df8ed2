import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getModelToken } from '@nestjs/mongoose';
import { ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { Model } from 'mongoose';

import { WorksheetQuestionService, UserContext } from '../services/worksheet-question.service';
import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { WorksheetQuestionAuditService } from '../services/worksheet-question-audit.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { EUserRole } from '../../user/dto/create-user.dto';
import { BulkReorderQuestionsDto, ReorderQuestionDto } from '../dto/worksheet-question.dto';
import { IExerciseQuestion, EQuestionType } from '../../../shared/interfaces/exercise-question.interface';

describe('WorksheetQuestionService - Reordering', () => {
  let service: WorksheetQuestionService;
  let worksheetRepository: Repository<Worksheet>;
  let questionModel: Model<WorksheetQuestionDocument>;
  let auditService: WorksheetQuestionAuditService;
  let socketGateway: SocketGateway;

  const mockUser: UserContext = {
    sub: 'user-123',
    email: '<EMAIL>',
    role: EUserRole.TEACHER,
    schoolId: 'school-123'
  };

  const createMockQuestion = (id: string, order: number): IExerciseQuestion => ({
    id,
    type: EQuestionType.MULTIPLE_CHOICE,
    content: `Question ${id}`,
    options: ['A', 'B', 'C', 'D'],
    answer: ['A'],
    explain: 'Test explanation',
    order,
    audit: {
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: mockUser.sub,
      version: 1
    }
  });

  const createMockWorksheet = (questions: IExerciseQuestion[]): Worksheet => ({
    id: 'worksheet-123',
    title: 'Test Worksheet',
    questions,
    totalQuestions: questions.length,
    schoolId: mockUser.schoolId,
    lastModifiedBy: mockUser.sub,
    questionMetadata: {
      questionVersion: 1,
      lastQuestionUpdate: new Date()
    }
  } as Worksheet);

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorksheetQuestionService,
        {
          provide: getRepositoryToken(Worksheet),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getModelToken(WorksheetQuestionDocument.name),
          useValue: {
            findOneAndUpdate: jest.fn(),
          },
        },
        {
          provide: WorksheetQuestionAuditService,
          useValue: {
            logQuestionMoved: jest.fn(),
          },
        },
        {
          provide: SocketGateway,
          useValue: {
            server: {
              to: jest.fn().mockReturnValue({
                emit: jest.fn(),
              }),
            },
          },
        },
      ],
    }).compile();

    service = module.get<WorksheetQuestionService>(WorksheetQuestionService);
    worksheetRepository = module.get<Repository<Worksheet>>(getRepositoryToken(Worksheet));
    questionModel = module.get<Model<WorksheetQuestionDocument>>(getModelToken(WorksheetQuestionDocument.name));
    auditService = module.get<WorksheetQuestionAuditService>(WorksheetQuestionAuditService);
    socketGateway = module.get<SocketGateway>(SocketGateway);
  });

  describe('reorderQuestionsInWorksheet', () => {
    it('should successfully reorder a single question', async () => {
      // Arrange
      const questions = [
        createMockQuestion('q1', 1),
        createMockQuestion('q2', 2),
        createMockQuestion('q3', 3),
      ];
      const worksheet = createMockWorksheet(questions);
      
      const reorderDto: BulkReorderQuestionsDto = {
        reorders: [
          { questionId: 'q3', newPosition: 1 }
        ]
      };

      jest.spyOn(worksheetRepository, 'findOne').mockResolvedValue(worksheet);
      jest.spyOn(worksheetRepository, 'save').mockResolvedValue(worksheet);
      jest.spyOn(questionModel, 'findOneAndUpdate').mockResolvedValue({} as any);

      // Act
      const result = await service.reorderQuestionsInWorksheet('worksheet-123', reorderDto, mockUser);

      // Assert
      expect(result.worksheetId).toBe('worksheet-123');
      expect(result.totalQuestions).toBe(3);
      expect(result.reorderedQuestions).toHaveLength(1);
      expect(result.reorderedQuestions[0]).toEqual({
        questionId: 'q3',
        oldPosition: 3,
        newPosition: 1
      });
      expect(worksheetRepository.save).toHaveBeenCalled();
      expect(auditService.logQuestionMoved).toHaveBeenCalledWith(
        'q3', 3, 1, 'worksheet-123', 'worksheet-123', mockUser.sub, { reorderOperation: true }
      );
    });

    it('should successfully reorder multiple questions', async () => {
      // Arrange
      const questions = [
        createMockQuestion('q1', 1),
        createMockQuestion('q2', 2),
        createMockQuestion('q3', 3),
        createMockQuestion('q4', 4),
      ];
      const worksheet = createMockWorksheet(questions);
      
      const reorderDto: BulkReorderQuestionsDto = {
        reorders: [
          { questionId: 'q4', newPosition: 1 },
          { questionId: 'q1', newPosition: 4 }
        ]
      };

      jest.spyOn(worksheetRepository, 'findOne').mockResolvedValue(worksheet);
      jest.spyOn(worksheetRepository, 'save').mockResolvedValue(worksheet);
      jest.spyOn(questionModel, 'findOneAndUpdate').mockResolvedValue({} as any);

      // Act
      const result = await service.reorderQuestionsInWorksheet('worksheet-123', reorderDto, mockUser);

      // Assert
      expect(result.reorderedQuestions).toHaveLength(2);
      expect(result.reorderedQuestions).toContainEqual({
        questionId: 'q4',
        oldPosition: 4,
        newPosition: 1
      });
      expect(result.reorderedQuestions).toContainEqual({
        questionId: 'q1',
        oldPosition: 1,
        newPosition: 4
      });
    });

    it('should throw NotFoundException for non-existent question', async () => {
      // Arrange
      const questions = [createMockQuestion('q1', 1)];
      const worksheet = createMockWorksheet(questions);
      
      const reorderDto: BulkReorderQuestionsDto = {
        reorders: [
          { questionId: 'non-existent', newPosition: 1 }
        ]
      };

      jest.spyOn(worksheetRepository, 'findOne').mockResolvedValue(worksheet);

      // Act & Assert
      await expect(
        service.reorderQuestionsInWorksheet('worksheet-123', reorderDto, mockUser)
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException for invalid position', async () => {
      // Arrange
      const questions = [createMockQuestion('q1', 1)];
      const worksheet = createMockWorksheet(questions);
      
      const reorderDto: BulkReorderQuestionsDto = {
        reorders: [
          { questionId: 'q1', newPosition: 5 } // Invalid - only 1 question exists
        ]
      };

      jest.spyOn(worksheetRepository, 'findOne').mockResolvedValue(worksheet);

      // Act & Assert
      await expect(
        service.reorderQuestionsInWorksheet('worksheet-123', reorderDto, mockUser)
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for duplicate positions', async () => {
      // Arrange
      const questions = [
        createMockQuestion('q1', 1),
        createMockQuestion('q2', 2),
      ];
      const worksheet = createMockWorksheet(questions);
      
      const reorderDto: BulkReorderQuestionsDto = {
        reorders: [
          { questionId: 'q1', newPosition: 1 },
          { questionId: 'q2', newPosition: 1 } // Duplicate position
        ]
      };

      jest.spyOn(worksheetRepository, 'findOne').mockResolvedValue(worksheet);

      // Act & Assert
      await expect(
        service.reorderQuestionsInWorksheet('worksheet-123', reorderDto, mockUser)
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle empty worksheet', async () => {
      // Arrange
      const worksheet = createMockWorksheet([]);
      
      const reorderDto: BulkReorderQuestionsDto = {
        reorders: [
          { questionId: 'q1', newPosition: 1 }
        ]
      };

      jest.spyOn(worksheetRepository, 'findOne').mockResolvedValue(worksheet);

      // Act & Assert
      await expect(
        service.reorderQuestionsInWorksheet('worksheet-123', reorderDto, mockUser)
      ).rejects.toThrow(BadRequestException);
    });
  });
});
