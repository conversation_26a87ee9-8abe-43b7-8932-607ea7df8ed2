import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { Socket, io } from 'socket.io-client';
import { WorksheetQuestionCollaborationGateway } from '../gateways/worksheet-question-collaboration.gateway';
import { WorksheetCollaborationService } from '../services/worksheet-collaboration.service';
import { WorksheetQuestionLockingService } from '../services/worksheet-question-locking.service';
import { RedisService } from '../../redis/redis.service';
import { JwtService } from '@nestjs/jwt';
import { RbacService } from '../../auth/services/rbac.service';
import { CollaborationEvent, UserAction } from '../enums/collaboration-events.enum';
import { EUserRole } from '../../user/dto/create-user.dto';

describe('Worksheet Collaboration Integration', () => {
  let app: INestApplication;
  let gateway: WorksheetQuestionCollaborationGateway;
  let collaborationService: WorksheetCollaborationService;
  let lockingService: WorksheetQuestionLockingService;
  let jwtService: JwtService;
  
  let client1: Socket;
  let client2: Socket;
  let testToken: string;
  let testWorksheetId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      providers: [
        WorksheetQuestionCollaborationGateway,
        {
          provide: WorksheetCollaborationService,
          useValue: {
            validateWorksheetAccess: jest.fn(),
            joinRoom: jest.fn(),
            leaveRoom: jest.fn(),
            updateUserPresence: jest.fn(),
            getRoomUsers: jest.fn(),
          },
        },
        {
          provide: WorksheetQuestionLockingService,
          useValue: {
            acquireLock: jest.fn(),
            releaseLock: jest.fn(),
            canEditQuestion: jest.fn(),
            releaseUserLocks: jest.fn(),
            getWorksheetLocks: jest.fn(),
          },
        },
        {
          provide: RedisService,
          useValue: {
            getClient: jest.fn().mockReturnValue({
              get: jest.fn(),
              set: jest.fn(),
              setex: jest.fn(),
              del: jest.fn(),
              keys: jest.fn(),
            }),
          },
        },
        {
          provide: JwtService,
          useValue: {
            verifyAsync: jest.fn(),
            sign: jest.fn(),
          },
        },
        {
          provide: RbacService,
          useValue: {
            validateWorksheetAccess: jest.fn(),
          },
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    gateway = moduleFixture.get<WorksheetQuestionCollaborationGateway>(WorksheetQuestionCollaborationGateway);
    collaborationService = moduleFixture.get<WorksheetCollaborationService>(WorksheetCollaborationService);
    lockingService = moduleFixture.get<WorksheetQuestionLockingService>(WorksheetQuestionLockingService);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    await app.listen(3001);

    // Setup test data
    testWorksheetId = 'test-worksheet-123';
    testToken = 'test-jwt-token';

    // Mock JWT verification
    jest.spyOn(jwtService, 'verifyAsync').mockResolvedValue({
      sub: 'user-123',
      email: '<EMAIL>',
      role: EUserRole.TEACHER,
      schoolId: 'school-123'
    });
  });

  afterAll(async () => {
    if (client1) client1.disconnect();
    if (client2) client2.disconnect();
    await app.close();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('WebSocket Connection and Room Management', () => {
    it('should establish connection and join worksheet room', (done) => {
      client1 = io('http://localhost:3001/worksheet-collaboration');

      // Mock collaboration service responses
      jest.spyOn(collaborationService, 'validateWorksheetAccess').mockResolvedValue({
        sub: 'user-123',
        email: '<EMAIL>',
        role: EUserRole.TEACHER,
        schoolId: 'school-123'
      });

      jest.spyOn(collaborationService, 'joinRoom').mockResolvedValue({
        worksheetId: testWorksheetId,
        activeUsers: new Map(),
        questionLocks: new Map(),
        editSessions: new Map(),
        conflicts: new Map(),
        roomStatus: 'active' as any,
        createdAt: new Date(),
        lastActivity: new Date(),
        maxUsers: 10,
        settings: {
          lockTimeout: 300000,
          presenceTimeout: 30000,
          maxConcurrentEditors: 5,
          enableTypingIndicators: true,
          enableAutoSave: true,
          autoSaveInterval: 10000,
          conflictResolutionStrategy: 'last_writer_wins' as any,
          allowGuestUsers: false
        }
      });

      jest.spyOn(collaborationService, 'getRoomUsers').mockResolvedValue([]);
      jest.spyOn(lockingService, 'getWorksheetLocks').mockResolvedValue([]);

      client1.on('connect', () => {
        client1.emit(CollaborationEvent.JOIN_WORKSHEET, {
          worksheetId: testWorksheetId,
          token: testToken
        });
      });

      client1.on(CollaborationEvent.ACTIVE_USERS_LIST, (data) => {
        expect(data.users).toBeDefined();
        expect(Array.isArray(data.users)).toBe(true);
        done();
      });

      client1.on(CollaborationEvent.COLLABORATION_ERROR, (error) => {
        done(new Error(`Collaboration error: ${error.message}`));
      });
    });

    it('should handle user presence updates', (done) => {
      client1 = io('http://localhost:3001/worksheet-collaboration');

      // Setup mocks
      jest.spyOn(collaborationService, 'validateWorksheetAccess').mockResolvedValue({
        sub: 'user-123',
        email: '<EMAIL>',
        role: EUserRole.TEACHER,
        schoolId: 'school-123'
      });

      jest.spyOn(collaborationService, 'updateUserPresence').mockResolvedValue({
        userId: 'user-123',
        email: '<EMAIL>',
        role: EUserRole.TEACHER,
        socketId: 'socket-123',
        joinedAt: new Date(),
        lastSeen: new Date(),
        currentAction: UserAction.EDITING_QUESTION,
        currentQuestionId: 'question-456',
        isActive: true
      });

      client1.on('connect', () => {
        // First join the room
        client1.emit(CollaborationEvent.JOIN_WORKSHEET, {
          worksheetId: testWorksheetId,
          token: testToken
        });

        // Then update presence
        setTimeout(() => {
          client1.emit(CollaborationEvent.USER_PRESENCE_UPDATE, {
            action: UserAction.EDITING_QUESTION,
            questionId: 'question-456'
          });
        }, 100);
      });

      client1.on(CollaborationEvent.USER_PRESENCE_UPDATE, (data) => {
        expect(data.user.currentAction).toBe(UserAction.EDITING_QUESTION);
        expect(data.user.currentQuestionId).toBe('question-456');
        done();
      });
    });
  });

  describe('Question Locking', () => {
    it('should acquire and release question locks', (done) => {
      client1 = io('http://localhost:3001/worksheet-collaboration');

      // Setup mocks
      jest.spyOn(lockingService, 'acquireLock').mockResolvedValue({
        success: true,
        lock: {
          questionId: 'question-456',
          worksheetId: testWorksheetId,
          lockedBy: 'user-123',
          lockType: 'pessimistic' as any,
          acquiredAt: new Date(),
          expiresAt: new Date(Date.now() + 300000),
          sessionId: 'session-123',
          isActive: true
        }
      });

      jest.spyOn(lockingService, 'releaseLock').mockResolvedValue(true);

      let lockAcquired = false;

      client1.on('connect', () => {
        // Join room first
        client1.emit(CollaborationEvent.JOIN_WORKSHEET, {
          worksheetId: testWorksheetId,
          token: testToken
        });

        // Acquire lock
        setTimeout(() => {
          client1.emit(CollaborationEvent.QUESTION_LOCK_ACQUIRED, {
            questionId: 'question-456',
            lockType: 'pessimistic'
          });
        }, 100);
      });

      client1.on(CollaborationEvent.QUESTION_LOCK_ACQUIRED, (data) => {
        if (data.success) {
          expect(data.lock.questionId).toBe('question-456');
          lockAcquired = true;

          // Release lock
          client1.emit(CollaborationEvent.QUESTION_LOCK_RELEASED, {
            questionId: 'question-456'
          });
        }
      });

      client1.on(CollaborationEvent.QUESTION_LOCK_RELEASED, (data) => {
        if (lockAcquired && data.success) {
          expect(data.questionId).toBe('question-456');
          done();
        }
      });
    });
  });

  describe('Real-time Updates', () => {
    it('should broadcast typing indicators', (done) => {
      client1 = io('http://localhost:3001/worksheet-collaboration');
      client2 = io('http://localhost:3001/worksheet-collaboration');

      let client1Connected = false;
      let client2Connected = false;

      const checkBothConnected = () => {
        if (client1Connected && client2Connected) {
          // Client 1 starts typing
          client1.emit(CollaborationEvent.QUESTION_TYPING_INDICATOR, {
            questionId: 'question-456',
            isTyping: true,
            cursorPosition: 10
          });
        }
      };

      client1.on('connect', () => {
        client1.emit(CollaborationEvent.JOIN_WORKSHEET, {
          worksheetId: testWorksheetId,
          token: testToken
        });
        client1Connected = true;
        checkBothConnected();
      });

      client2.on('connect', () => {
        client2.emit(CollaborationEvent.JOIN_WORKSHEET, {
          worksheetId: testWorksheetId,
          token: testToken
        });
        client2Connected = true;
        checkBothConnected();
      });

      // Client 2 should receive typing indicator from Client 1
      client2.on(CollaborationEvent.QUESTION_TYPING_INDICATOR, (data) => {
        expect(data.questionId).toBe('question-456');
        expect(data.isTyping).toBe(true);
        expect(data.cursorPosition).toBe(10);
        done();
      });
    });
  });
});
