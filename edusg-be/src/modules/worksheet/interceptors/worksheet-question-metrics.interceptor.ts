import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { WorksheetQuestionMetricsService } from '../services/worksheet-question-metrics.service';
import { UserContext } from '../../../shared/interfaces/user-context.interface';

/**
 * Interceptor to automatically collect performance metrics for worksheet question operations
 */
@Injectable()
export class WorksheetQuestionMetricsInterceptor implements NestInterceptor {
  private readonly logger = new Logger(WorksheetQuestionMetricsInterceptor.name);

  constructor(
    private readonly metricsService: WorksheetQuestionMetricsService
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const startTime = Date.now();
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    
    // Extract request information
    const method = request.method;
    const url = request.url;
    const endpoint = this.normalizeEndpoint(url);
    const user: UserContext = request.user;
    const userRole = user?.role || 'anonymous';

    // Track active operations
    this.metricsService.updateActiveOperations('api_request', 1);

    return next.handle().pipe(
      tap((data) => {
        // Record successful operation
        const duration = (Date.now() - startTime) / 1000;
        const statusCode = response.statusCode;

        this.metricsService.recordApiOperation(
          duration,
          method,
          endpoint,
          statusCode,
          userRole
        );

        // Update memory usage if available
        this.updateMemoryMetrics('api_success');

        // Decrement active operations
        this.metricsService.updateActiveOperations('api_request', -1);

        this.logger.debug(
          `API operation completed: ${method} ${endpoint} - ${statusCode} - ${duration}s - ${userRole}`
        );
      }),
      catchError((error) => {
        // Record error operation
        const duration = (Date.now() - startTime) / 1000;
        const statusCode = error.status || 500;
        const errorType = this.getErrorType(error);

        this.metricsService.recordApiOperation(
          duration,
          method,
          endpoint,
          statusCode,
          userRole
        );

        this.metricsService.recordApiError(
          method,
          endpoint,
          errorType,
          userRole
        );

        // Update memory usage
        this.updateMemoryMetrics('api_error');

        // Decrement active operations
        this.metricsService.updateActiveOperations('api_request', -1);

        this.logger.error(
          `API operation failed: ${method} ${endpoint} - ${statusCode} - ${duration}s - ${userRole} - ${errorType}`,
          error.stack
        );

        return throwError(() => error);
      })
    );
  }

  /**
   * Normalize endpoint URL to remove dynamic parameters
   */
  private normalizeEndpoint(url: string): string {
    // Remove query parameters
    const baseUrl = url.split('?')[0];
    
    // Replace UUIDs and numeric IDs with placeholders
    const normalized = baseUrl
      .replace(/\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '/:id')
      .replace(/\/\d+/g, '/:id')
      .replace(/\/questions\/[^\/]+/g, '/questions/:questionId');

    return normalized;
  }

  /**
   * Determine error type from error object
   */
  private getErrorType(error: any): string {
    if (error.name) {
      return error.name;
    }

    if (error.status) {
      switch (error.status) {
        case 400:
          return 'BadRequestException';
        case 401:
          return 'UnauthorizedException';
        case 403:
          return 'ForbiddenException';
        case 404:
          return 'NotFoundException';
        case 409:
          return 'ConflictException';
        case 422:
          return 'UnprocessableEntityException';
        case 429:
          return 'ThrottlerException';
        case 500:
          return 'InternalServerErrorException';
        default:
          return 'UnknownException';
      }
    }

    return 'UnknownException';
  }

  /**
   * Update memory usage metrics
   */
  private updateMemoryMetrics(operationType: string): void {
    try {
      const memoryUsage = process.memoryUsage();
      this.metricsService.updateMemoryUsage(operationType, memoryUsage.heapUsed);
    } catch (error) {
      // Silently fail memory metrics collection to avoid impacting main operation
      this.logger.debug('Failed to collect memory metrics', error);
    }
  }
}

/**
 * Decorator to apply metrics collection to specific endpoints
 */
export function CollectMetrics() {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      const metricsService = this.metricsService || this.worksheetQuestionMetricsService;
      
      if (!metricsService) {
        // If metrics service is not available, just execute the method
        return method.apply(this, args);
      }

      try {
        // Track active operations
        metricsService.updateActiveOperations(propertyName, 1);

        const result = await method.apply(this, args);
        
        // Record successful operation
        const duration = (Date.now() - startTime) / 1000;
        
        // Decrement active operations
        metricsService.updateActiveOperations(propertyName, -1);

        return result;
      } catch (error) {
        // Record failed operation
        const duration = (Date.now() - startTime) / 1000;
        
        // Decrement active operations
        metricsService.updateActiveOperations(propertyName, -1);

        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Decorator to collect database operation metrics
 */
export function CollectDbMetrics(operation: string, collection: string, queryType: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      const metricsService = this.metricsService || this.worksheetQuestionMetricsService;
      
      if (!metricsService) {
        return method.apply(this, args);
      }

      try {
        const result = await method.apply(this, args);
        
        // Record successful database operation
        const duration = (Date.now() - startTime) / 1000;
        metricsService.recordDbQuery(duration, operation, collection, queryType);

        return result;
      } catch (error) {
        // Record failed database operation
        const duration = (Date.now() - startTime) / 1000;
        metricsService.recordDbQuery(duration, operation, collection, 'error');

        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Decorator to collect cache operation metrics
 */
export function CollectCacheMetrics(cacheType: string, operation: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      const metricsService = this.metricsService || this.worksheetQuestionMetricsService;
      
      if (!metricsService) {
        return method.apply(this, args);
      }

      try {
        const result = await method.apply(this, args);
        
        // Determine if it was a hit or miss based on result
        const cacheResult = result ? 'hit' : 'miss';
        const duration = (Date.now() - startTime) / 1000;
        
        metricsService.recordCacheOperation(duration, cacheType, operation, cacheResult);
        
        if (cacheResult === 'hit') {
          metricsService.recordCacheHit(cacheType, operation);
        } else {
          metricsService.recordCacheMiss(cacheType, operation);
        }

        return result;
      } catch (error) {
        // Record cache error
        const duration = (Date.now() - startTime) / 1000;
        metricsService.recordCacheOperation(duration, cacheType, operation, 'error');

        throw error;
      }
    };

    return descriptor;
  };
}
