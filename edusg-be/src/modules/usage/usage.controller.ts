import { Controller, Get, UseGuards, Logger } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AuthGuard } from '../auth/guards/auth.guard';
import { ActiveUser } from '../auth/decorators/active-user.decorator';
import { ActiveUserData } from '../auth/decorators/active-user.decorator';
import { UsageTrackingService } from '../usage-tracking/services/usage-tracking.service';
import { UsageLimitCheckResult } from '../usage-tracking/interfaces/usage-tracking.interface';

@ApiTags('Usage')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('usage')
export class UsageController {
  private readonly logger = new Logger(UsageController.name);

  constructor(private readonly usageTrackingService: UsageTrackingService) {}

  @Get('summary/daily')
  @ApiOperation({
    summary: 'Get daily usage summary',
    description: 'Returns a summary of daily usage limits and consumption for the user'
  })
  @ApiResponse({
    status: 200,
    description: 'Daily usage summary retrieved successfully'
  })
  async getDailyUsageSummary(@ActiveUser() user: ActiveUserData) {
    try {
      // Get usage limits for all relevant features
      const [worksheetsLimit, questionsLimit] = await Promise.all([
        this.usageTrackingService.checkUsageLimit(user.sub, 'maxWorksheets'),
        this.usageTrackingService.checkUsageLimit(user.sub, 'maxQuestionsPerWorksheet')
      ]);

      return {
        success: true,
        data: {
          userId: user.sub,
          dailySummary: {
            maxWorksheets: {
              usage: worksheetsLimit.current,
              limit: worksheetsLimit.limit,
              remaining: worksheetsLimit.remaining,
              withinLimit: worksheetsLimit.withinLimit,
              period: worksheetsLimit.period,
              feature: worksheetsLimit.feature
            },
            maxQuestionsPerWorksheet: {
              usage: questionsLimit.current,
              limit: questionsLimit.limit,
              remaining: questionsLimit.remaining,
              withinLimit: questionsLimit.withinLimit,
              period: questionsLimit.period,
              feature: questionsLimit.feature
            }
          }
        }
      };
    } catch (error) {
      this.logger.error(`Error getting daily usage summary: ${error.message}`, error.stack);
      throw error;
    }
  }
} 