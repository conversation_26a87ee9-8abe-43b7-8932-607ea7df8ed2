import {forwardRef, HttpException, HttpStatus, Inject, Injectable, Logger} from '@nestjs/common';
import {VectorDBDocument, VectorDBService} from '../vector-db/interfaces/vector-db.interface';
import * as path from 'path';
import {PDFReader} from '@llamaindex/readers/pdf';
import {Document, Settings, storageContextFromDefaults, VectorStoreIndex,} from 'llamaindex';
import {Gemini, GEMINI_MODEL, GeminiEmbedding, GEMINI_EMBEDDING_MODEL} from '@llamaindex/google';
import {openai} from "@llamaindex/openai";
import {
  DocumentMetadata,
  ProcessDocumentOptions,
  ProcessDocumentResult,
  QueryResult,
  SourceNode,
} from './interfaces/document.interface';
import {ConfigService} from "@nestjs/config";
import {ModelConfigService} from '../ai/model-config.service';
import {BuildPromptService} from '../build-prompt/build-prompt.service';
import {MultimodalService} from '../multimodal/multimodal.service';
import {QueryCacheService} from '../mongodb/services/query-cache.service';
import {EmbeddingService} from '../embedding/embedding.service';

@Injectable()
export class DocumentsService {
  private documentCache: Map<string, VectorStoreIndex> = new Map();
  private cacheStats: {
    hits: number;
    misses: number;
    lastCleared: Date;
  } = {
    hits: 0,
    misses: 0,
    lastCleared: new Date(),
  };

  // Cache expiration time (30 minutes)
  private readonly QUERY_CACHE_EXPIRATION_MS = 30 * 60 * 1000;

  private readonly logger: Logger = new Logger(DocumentsService.name)

  constructor(
    @Inject('VECTOR_DB')
    private readonly vectorDBService: VectorDBService,
    private configService: ConfigService,
    private modelConfigService: ModelConfigService,
    @Inject(forwardRef(() => BuildPromptService))
    private buildPromptService: BuildPromptService,
    @Inject(forwardRef(() => MultimodalService))
    private multimodalService: MultimodalService,
    private queryCacheService: QueryCacheService,
    @Inject(forwardRef(() => EmbeddingService))
    private embeddingService: EmbeddingService,
  ) {}

  /**
   * Process a document and create a vector index
   * @param file The document file to process
   * @param metadata Metadata for the document
   * @param options Options for processing the document
   * @returns The processed document result
   */
  async processDocument(
    file: Express.Multer.File,
    metadata: DocumentMetadata,
    options: ProcessDocumentOptions = {},
  ): Promise<ProcessDocumentResult> {
    try {
      const { documentId } = metadata;
      const {
        forceReprocess = false,
      } = options;

      // Check if document is in cache and we're not forcing reprocessing
      if (!forceReprocess && this.documentCache.has(documentId)) {
        this.logger.debug(`Using cached document: ${documentId}`)
        this.cacheStats.hits++;
        const cachedVector = this.documentCache.get(documentId);
        if (!cachedVector) {
          throw new Error(
            `Document ${documentId} found in cache but vector is undefined`,
          );
        }
        return {
          documentId,
          vector: cachedVector,
          fromCache: true,
        };
      }

      this.cacheStats.misses++;
      const startTime = Date.now();

      // Process the document with the specified options
      const vectorIndex = await this.extractText(file, metadata);

      // Store in cache for future use
      this.documentCache.set(documentId, vectorIndex);

      const duration = Date.now() - startTime;
      this.logger.debug(`Document processing completed in ${duration}ms`);

      return {
        documentId,
        vector: vectorIndex,
        fromCache: false,
      };
    } catch (error) {
      throw new Error(`Failed to process document: ${error.message}`);
    }
  }

  /**
   * Query documents in the vector database
   * @param query The query string
   * @param similarityTopK Maximum number of similar chunks to retrieve
   * @param timeoutMs Timeout in milliseconds
   * @param category Optional category to filter documents by
   * @returns The query result
   */
  async queryDocuments(
    query: string,
    similarityTopK: number = 5, // Reduced to 5 for better performance
    timeoutMs: number = 60000, // Increased timeout to 60 seconds
    category?: string,
    skipCache: boolean = false, // Option to skip cache for fresh results
  ): Promise<string> {
    try {
      const queryStartTime = Date.now();
      this.logger.debug(`Querying documents with parameters: similarityTopK=${similarityTopK}, timeoutMs=${timeoutMs}, category=${category || 'all'}, skipCache=${skipCache}`)

      // Create query parameters object for MongoDB cache
      const queryParams = {
        similarityTopK,
        category: category || 'all',
      };

      // Check if we should use cache
      if (!skipCache) {
        // Check MongoDB cache using QueryCacheService
        const cachedResult = await this.queryCacheService.getFromCache(
          this.queryCacheService.generateCacheKey(query, queryParams)
        );

        if (cachedResult) {
          this.logger.debug(`Using cached query result for: "${query}"`);
          return cachedResult;
        }
      }

      // Configure LLM and embedding model
      Settings.llm = openai({
        baseURL: 'https://openrouter.ai/api/v1',
        apiKey: this.configService.get<string>('OPENAI_API_KEY'),
        model: this.modelConfigService.getLLMModel(),
      });

      // Use GeminiEmbedding directly from the @llamaindex/google package
      Settings.embedModel = new GeminiEmbedding({
        model: GEMINI_EMBEDDING_MODEL.EMBEDDING_001,
      });

      // Get vector collection
      const vectorStore = this.vectorDBService.getVectorCollection();

      // Set timeout for query
      this.logger.debug(`Setting query timeout: ${timeoutMs}ms`)

      // Simple query enhancement without using LLM
      let enhancedQuery = query.trim();

      // Add category terms if provided
      if (category && category.trim() !== '') {
        enhancedQuery = `${enhancedQuery} ${category}`;
      }

      // Add common educational terms for better matching
      if (query.toLowerCase().includes('exam') || query.toLowerCase().includes('test') ||
          query.toLowerCase().includes('question') || query.toLowerCase().includes('exercise')) {
        enhancedQuery = `${enhancedQuery} examination test questions exercises problems`;
      }

      this.logger.debug(`Enhanced query: "${enhancedQuery}"`);
      const expandedQuery = enhancedQuery;

      // Create index from vector store with optimized settings
      const index = await VectorStoreIndex.fromVectorStore(vectorStore);

      // Configure retriever with exact similarityTopK to avoid retrieving too many results
      const retriever = index.asRetriever({
        similarityTopK: similarityTopK,
      });

      // Configure simple query engine
      const queryEngine = index.asQueryEngine({
        retriever: retriever,
      });

      // Performance tracking - retrieval start
      const retrievalStartTime = Date.now();

      // Execute query with timeout
      const response = (await Promise.race([
        queryEngine.query({
          query: expandedQuery, // Use the expanded query instead of the original
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Query timed out')), timeoutMs),
        ),
      ])) as QueryResult;

      // Performance tracking - retrieval end
      const retrievalDuration = Date.now() - retrievalStartTime;
      this.logger.debug(`Retrieval completed in ${retrievalDuration}ms`);

      // Skip complex filtering to improve performance
      // Just log the number of results
      if (response.sourceNodes && response.sourceNodes.length > 0) {
        this.logger.debug(`Retrieved ${response.sourceNodes.length} results`);

        // Very simple filtering - just remove results with extremely low scores
        // This is much faster than the complex filtering logic
        const minThreshold = 0.2; // Very low threshold to keep most results
        const originalCount = response.sourceNodes.length;

        response.sourceNodes = response.sourceNodes.filter(node => node.score >= minThreshold);

        if (originalCount !== response.sourceNodes.length) {
          this.logger.debug(`Filtered out ${originalCount - response.sourceNodes.length} results with scores below ${minThreshold}`);
        }
      }

      // If we have source nodes after filtering, simply concatenate their text
      let resultText = "";
      if (response.sourceNodes && response.sourceNodes.length > 0) {
        try {
          // Extract text from source nodes, sorted by relevance score
          resultText = response.sourceNodes
            .sort((a, b) => b.score - a.score)
            .map((sourceNode) => {
              // Add metadata information if available
              let metadataInfo = '';
              if (sourceNode.node.metadata) {
                if (sourceNode.node.metadata.title) {
                  metadataInfo += `Title: ${sourceNode.node.metadata.title}\n`;
                }
                if (sourceNode.node.metadata.section) {
                  metadataInfo += `Section: ${sourceNode.node.metadata.section}\n`;
                }
              }
              return `${metadataInfo}\n${sourceNode.node.text}`;
            })
            .join('\n\n---\n\n');

          this.logger.debug(`Generated response with ${response.sourceNodes.length} source nodes`);
        } catch (error) {
          this.logger.error(`Error generating response: ${error.message}`, error.stack);
          // If generation fails, use a simple fallback
          resultText = "Unable to generate a detailed response. Please try a more specific query.";
        }
      } else {
        resultText = "No relevant content found. Please try a different query.";
      }

      // Calculate total duration for logging
      const queryEndTime = Date.now();
      const totalDuration = queryEndTime - queryStartTime;

      this.logger.debug(`Query completed in ${totalDuration}ms with ${response.sourceNodes?.length || 0} results`);

      // Save to MongoDB cache (if not skipping cache)
      if (!skipCache) {
        await this.queryCacheService.saveToCache(
          query,
          queryParams,
          resultText,
          Math.floor(this.QUERY_CACHE_EXPIRATION_MS / (60 * 1000)) // Convert ms to minutes
        );
      }

      // Return the result text
      return resultText;
    } catch (error) {
      this.logger.error('Error querying documents:', error);
      throw new Error(`Failed to query documents: ${error.message}`);
    }
  }

  /**
   * Extract JSON from a string that might contain markdown code blocks
   * @param text The text that might contain JSON in markdown code blocks
   * @returns The extracted JSON string
   * @private
   */
  private extractJsonFromMarkdown(text: string): string {
    // Check if the text contains markdown code blocks
    const jsonCodeBlockRegex = /```(?:json)?\s*([\s\S]*?)```/;
    const match = text.match(jsonCodeBlockRegex);

    if (match && match[1]) {
      // Return the content inside the code block
      return match[1].trim();
    }

    // If no code block is found, return the original text
    return text.trim();
  }

  /**
   * Process documents to extract images and identify exercise formats
   * @param documents The documents to process
   * @returns The processed documents with image and exercise format information
   * @private
   */
  /**
   * Clear all caches (both in-memory and MongoDB)
   */
  async clearAllCaches(): Promise<void> {
    // Clear document cache
    const docCacheSize = this.documentCache.size;
    this.documentCache.clear();
    this.logger.debug(`Cleared in-memory document cache with ${docCacheSize} entries`);

    try {
      // Clear memory caches in services
      this.queryCacheService.clearMemoryCache();
      this.embeddingService.clearEmbeddingMemoryCache();

      // Clear expired entries in MongoDB
      const expiredQueryEntries = await this.queryCacheService.clearExpiredEntries();
      this.logger.debug(`Cleared ${expiredQueryEntries} expired query cache entries from MongoDB`);

      // Clear expired embedding cache entries through the embedding service
      const expiredEmbeddingEntries = await this.embeddingService.clearExpiredEmbeddingCache();
      this.logger.debug(`Cleared ${expiredEmbeddingEntries} expired embedding cache entries from MongoDB`);

    } catch (error) {
      this.logger.error(`Error clearing caches: ${error.message}`);
    }
  }

  /**
   * Retry a function with exponential backoff
   * @param fn The function to retry
   * @param maxRetries Maximum number of retries
   * @param initialDelay Initial delay in milliseconds
   * @returns The result of the function
   * @private
   */
  private async retryWithBackoff<T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    initialDelay: number = 1000
  ): Promise<T> {
    let retries = 0;
    let delay = initialDelay;

    while (true) {
      try {
        return await fn();
      } catch (error) {
        retries++;
        if (retries > maxRetries) {
          this.logger.error(`Max retries (${maxRetries}) exceeded:`, error);
          throw error;
        }

        // Check if error is related to rate limiting
        const isRateLimitError =
          error.message?.includes('rate limit') ||
          error.message?.includes('quota') ||
          error.message?.includes('too many requests');

        if (isRateLimitError) {
          this.logger.warn(`Rate limit error detected, retrying after delay (attempt ${retries}/${maxRetries})`);
        } else {
          this.logger.warn(`Error occurred, retrying after delay (attempt ${retries}/${maxRetries}): ${error.message}`);
        }

        // Exponential backoff with jitter
        delay = delay * 2 + Math.random() * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * Process documents to extract images and identify exercise formats
   * Optimized to reduce API calls by consolidating documents into a single request
   * @param documents The documents to process
   * @returns The processed documents with image and exercise format information
   * @private
   */
  private async processDocumentsWithImagesAndExercises(
    documents: Document[],
  ): Promise<Document[]> {
    try {
      this.logger.debug(`Processing ${documents.length} documents for images and exercise formats`);

      // Configure LLM for content analysis - using a more capable model for better extraction
      const llm = new Gemini({
        model: GEMINI_MODEL.GEMINI_2_0_PRO_EXPERIMENTAL,
      });

      // First, check if we need to merge any document chunks that might have been split incorrectly
      // This is especially important for examination content where questions might be split across chunks
      const mergedDocuments = this.mergeRelatedDocumentChunks(documents);
      this.logger.debug(`After merging related chunks: ${mergedDocuments.length} documents`);

      // Initialize the processed documents array
      const processedDocuments: Document[] = [];

      // Estimate token count per document (average)
      // Each document gets truncated to 1500 chars max, which is roughly 375 tokens
      // Plus overhead for the document index and formatting
      const ESTIMATED_TOKENS_PER_DOC = 400;

      // Gemini 2.0 Pro has a context window of ~1 million tokens, but we'll be conservative
      // and limit to 100K tokens to avoid issues and leave room for the response
      const MAX_TOKENS = 100000;

      // Calculate maximum documents per batch based on token limit
      const MAX_DOCS_PER_BATCH = Math.floor(MAX_TOKENS / ESTIMATED_TOKENS_PER_DOC);

      // Use the smaller of our calculated max or all documents
      const BATCH_SIZE = Math.min(MAX_DOCS_PER_BATCH, mergedDocuments.length);

      this.logger.debug(`Processing documents in batches of up to ${BATCH_SIZE} documents`);

      // Split documents into batches based on token limit
      for (let i = 0; i < mergedDocuments.length; i += BATCH_SIZE) {
        const batch = mergedDocuments.slice(i, i + BATCH_SIZE);
        this.logger.debug(`Processing batch ${Math.floor(i/BATCH_SIZE) + 1} with ${batch.length} documents`);

        // Create a combined prompt for all documents in the batch
        let combinedPrompt = `You are an expert in analyzing educational documents. Analyze the following ${batch.length} document chunks and provide analysis for each one.

        For each document, determine:
        1. If it contains examination content (questions, problems, exercises)
        2. If it references or describes images, diagrams, charts, or figures
        3. If it contains educational exercises or examination questions

        Respond with a JSON array where each element contains the analysis for one document. Each element should have the following structure:
        {
          "documentIndex": number,
          "isExamContent": boolean,
          "examContentDescription": string or null,
          "containsImage": boolean,
          "imageDescription": string or null,
          "containsExercise": boolean,
          "exerciseFormat": JSON object or null
        }

        Here are the document chunks to analyze:\n\n`;

        // Add each document to the prompt
        for (let j = 0; j < batch.length; j++) {
          const doc = batch[j];
          combinedPrompt += `DOCUMENT ${j + 1}:\n"${doc.text.substring(0, 1500)}"${doc.text.length > 1500 ? ' ... (text truncated)' : ''}\n\n`;
        }

        // Make a single API call for the entire batch with retry logic
        const batchResponse = await this.retryWithBackoff(async () => {
          return await llm.complete({
            prompt: combinedPrompt,
          });
        });

        // Parse the response
        let batchAnalysis;
        try {
          // Extract JSON from markdown code blocks if present
          const cleanedResponse = this.extractJsonFromMarkdown(batchResponse.text.trim());
          batchAnalysis = JSON.parse(cleanedResponse);

          // Ensure it's an array
          if (!Array.isArray(batchAnalysis)) {
            this.logger.warn('Batch analysis response is not an array, attempting to wrap it');
            batchAnalysis = [batchAnalysis];
          }
        } catch (error) {
          this.logger.error(`Error parsing batch analysis: ${error.message}`, error.stack);
          // If parsing fails, process each document individually as fallback
          for (let j = 0; j < batch.length; j++) {
            const doc = batch[j];
            // Initialize metadata if not present
            if (!doc.metadata) {
              doc.metadata = {};
            }
            processedDocuments.push(doc);
          }
          continue;
        }

        // Apply the analysis to each document
        for (let j = 0; j < batch.length; j++) {
          const doc = batch[j];
          // Initialize metadata if not present
          if (!doc.metadata) {
            doc.metadata = {};
          }

          // Find the analysis for this document
          const analysis = batchAnalysis.find(a => a.documentIndex === j + 1) ||
                          batchAnalysis[j]; // Fallback to index if documentIndex doesn't match

          if (analysis) {
            // Apply exam content metadata
            if (analysis.isExamContent) {
              doc.metadata.isExamContent = true;
              if (analysis.examContentDescription) {
                doc.metadata.examContentDescription = analysis.examContentDescription;
              }
            }

            // Apply image metadata
            if (analysis.containsImage) {
              doc.metadata.containsImage = true;
              if (analysis.imageDescription) {
                doc.metadata.imageDescription = analysis.imageDescription;
              }
            }

            // Apply exercise metadata
            if (analysis.containsExercise && analysis.exerciseFormat) {
              doc.metadata.containsExercise = true;
              doc.metadata.isExamContent = true; // Mark as exam content if exercise is found

              // Stringify the exerciseFormat object to comply with Pinecone metadata requirements
              try {
                const exerciseFormat = typeof analysis.exerciseFormat === 'string' ?
                                      JSON.parse(analysis.exerciseFormat) : analysis.exerciseFormat;
                doc.metadata.exerciseFormat = JSON.stringify(exerciseFormat);
              } catch (error) {
                this.logger.error(`Error processing exercise format: ${error.message}`, error.stack);
              }
            }
          } else {
            this.logger.warn(`No analysis found for document ${j + 1} in batch ${Math.floor(i/BATCH_SIZE) + 1}`);
          }

          processedDocuments.push(doc);
        }
      }

      this.logger.debug(`Successfully processed ${processedDocuments.length} documents`);
      return processedDocuments;
    } catch (error) {
      this.logger.error('Error processing documents for images and exercises:', error);
      return documents; // Return original documents if processing fails
    }
  }

  /**
   * Merge document chunks that appear to be related or part of the same content
   * This is especially important for examination content where questions might be split across chunks
   * @param documents The original document chunks
   * @returns Merged document chunks
   * @private
   */
  private mergeRelatedDocumentChunks(documents: Document[]): Document[] {
    if (documents.length <= 1) {
      return documents;
    }

    this.logger.debug(`Attempting to merge related chunks from ${documents.length} original chunks`);

    const mergedDocuments: Document[] = [];
    let currentDoc = documents[0];

    for (let i = 1; i < documents.length; i++) {
      const nextDoc = documents[i];

      // Check if this chunk appears to be a continuation of the previous one
      // Look for indicators like incomplete sentences, split questions, etc.
      const currentEndsWithIncomplete = /[,;:]\s*$/.test(currentDoc.text) ||
                                       !currentDoc.text.trim().endsWith('.');

      const nextStartsLowercase = /^\s*[a-z]/.test(nextDoc.text);

      // Check for split question patterns
      const currentEndsWithQuestion = /\d+\s*[.)]\s*[^.!?]*$/.test(currentDoc.text);
      const nextStartsWithOptions = /^\s*(?:[A-D][.)]|\([A-D]\))/.test(nextDoc.text);

      // If chunks appear to be related, merge them
      if (currentEndsWithIncomplete || nextStartsLowercase ||
          (currentEndsWithQuestion && nextStartsWithOptions)) {
        this.logger.debug(`Merging chunks ${i-1} and ${i} - detected continuation`);

        // Merge the text content
        currentDoc.text = `${currentDoc.text}\n${nextDoc.text}`;

        // Merge metadata if present
        if (nextDoc.metadata) {
          currentDoc.metadata = { ...currentDoc.metadata, ...nextDoc.metadata };
        }
      } else {
        // If not related, add the current document to results and move to next
        mergedDocuments.push(currentDoc);
        currentDoc = nextDoc;
      }
    }

    // Add the last document
    mergedDocuments.push(currentDoc);

    this.logger.debug(`After merging: ${mergedDocuments.length} document chunks (reduced from ${documents.length})`);
    return mergedDocuments;
  }

  /**
   * Get all examination formats for a specific school
   * @param schoolId The school ID
   * @returns Array of examination format texts
   */
  async getAllExaminationFormats(schoolId: string): Promise<string[]> {
    try {
      this.logger.debug(`Retrieving all examination formats for school ID: ${schoolId}`);

      // Query the vector database for all documents with the specified metadata
      const documents = await this.vectorDBService.queryDocumentsByMetadata({
        schoolId: schoolId,
        documentType: 'examinationFormat'
      }); // No limit to get all formats

      if (!documents || documents.length === 0) {
        this.logger.warn(`No examination formats found for school ID: ${schoolId}`);
        return [];
      }

      // Extract text content from all documents
      const formats = documents
        .map(doc => doc.metadata?.textContent as string)
        .filter(content => content && content.trim().length > 0);

      this.logger.debug(`Found ${formats.length} examination formats for school ID: ${schoolId}`);
      return formats;
    } catch (error) {
      this.logger.error(`Error retrieving examination formats: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Get the examination format for a specific school (most recent)
   * @param schoolId The school ID
   * @returns The examination format text or null if not found
   */
  async getExaminationFormat(schoolId: string): Promise<string | null> {
    try {
      this.logger.debug(`Retrieving examination format for school ID: ${schoolId}`);

      // Query the vector database for documents with the specified metadata
      const documents = await this.vectorDBService.queryDocumentsByMetadata({
        schoolId: schoolId,
        documentType: 'examinationFormat'
      }, 1); // Limit to 1 result (most recent)

      if (documents && documents.length > 0) {
        // Get the first document (most recent)
        const document = documents[0];

        // Extract the text content from the metadata
        const textContent = document.metadata.textContent as string;

        if (textContent) {
          this.logger.debug(`Found examination format for school ID: ${schoolId}`);
          return textContent;
        }
      }

      this.logger.warn(`No examination format found for school ID: ${schoolId}`);
      return null;
    } catch (error) {
      this.logger.error(`Error retrieving examination format: ${error.message}`, error.stack);
      // Return null instead of throwing an error to allow question generation to proceed
      return null;
    }
  }

  /**
   * Store an examination format for a specific school
   * @param schoolId The school ID
   * @param textContent The examination format text content
   * @param documentId Optional document ID (generated if not provided)
   * @returns The document ID
   */
  async storeExaminationFormat(
    schoolId: string, 
    textContent: string, 
    documentId?: string
  ): Promise<string> {
    try {
      this.logger.debug(`Storing examination format for school ID: ${schoolId}`);

      // Generate a document ID if not provided
      const docId = documentId || `examination-format-${schoolId}-${Date.now()}`;

      // Generate embeddings for the text content
      const embedding = await this.embeddingService.getQueryEmbedding(textContent);

      // Create a document with the text content and metadata
      const document: VectorDBDocument = {
        id: docId,
        embedding: embedding,
        metadata: {
          schoolId: schoolId,
          documentType: 'examinationFormat',
          textContent: textContent,
          timestamp: Date.now()
        }
      };

      // Store the document in the vector database
      await this.vectorDBService.upsertDocuments([document]);

      this.logger.debug(`Successfully stored examination format for school ID: ${schoolId}`);
      return docId;
    } catch (error) {
      this.logger.error(`Error storing examination format: ${error.message}`, error.stack);
      throw new Error(`Failed to store examination format: ${error.message}`);
    }
  }

  /**
   * Delete the examination format for a specific school
   * @param schoolId The school ID
   * @returns True if deletion was successful, false otherwise
   */
  async deleteExaminationFormat(schoolId: string): Promise<boolean> {
    try {
      this.logger.debug(`Deleting examination format for school ID: ${schoolId}`);

      // Query the vector database for documents with the specified metadata
      const documents = await this.vectorDBService.queryDocumentsByMetadata({
        schoolId: schoolId,
        documentType: 'examinationFormat'
      });

      if (!documents || documents.length === 0) {
        this.logger.warn(`No examination format found for school ID: ${schoolId}`);
        return false;
      }

      // Get the document IDs to delete
      const documentIds = documents.map(doc => doc.id);

      // Delete the documents from the vector database
      await this.vectorDBService.deleteDocuments(documentIds);

      this.logger.debug(`Successfully deleted examination format for school ID: ${schoolId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error deleting examination format: ${error.message}`, error.stack);
      return false;
    }
  }

  private async extractText(
    file: Express.Multer.File,
    metadata: DocumentMetadata,
  ): Promise<VectorStoreIndex> {
    const filePath = file.originalname;
    const fileExtension = path.extname(filePath).toLowerCase();
    this.logger.debug(`Processing file: ${filePath} with extension: ${fileExtension}`);

    if (fileExtension === '.pdf') {
      try {
        let documents: Document[];
        const useMultimodal = this.configService.get<string>('USE_MULTIMODAL_PROCESSING') === 'true';

        if (useMultimodal) {
          // Use multimodal processing for PDFs that might contain images
          this.logger.debug(`Using multimodal processing for PDF: ${filePath}`);
          documents = await this.multimodalService.processPdfWithMultimodalAI(file.buffer, metadata.documentId);
          this.logger.debug(`Multimodal processing extracted ${documents.length} document chunks from PDF`);
        } else {
          // Use standard text-only processing
          this.logger.debug(`Using standard text-only processing for PDF: ${filePath}`);
          const reader = new PDFReader();
          documents = await reader.loadDataAsContent(file.buffer);
          this.logger.debug(`Standard processing extracted ${documents.length} document chunks from PDF`);
        }

        // Log the first few characters of each document to debug extraction issues
        documents.forEach((doc, index) => {
          const previewText = doc.text.substring(0, 100).replace(/\n/g, ' ');
          this.logger.debug(`Document chunk ${index + 1} preview: ${previewText}...`);

          // Log if the document has image content
          if (doc.metadata && (doc.metadata.hasImage || doc.metadata.imageUrl)) {
            this.logger.debug(`Document chunk ${index + 1} has image content: ${doc.metadata.imageUrl}`);
          }
        });

        // Process documents to extract images and identify exercise formats
        // Skip this step for multimodal processing as it already handles images
        const processedDocuments = useMultimodal ?
          documents :
          await this.processDocumentsWithImagesAndExercises(documents);

        // Configure LLM and embedding model
        Settings.llm = openai({
            baseURL: 'https://openrouter.ai/api/v1',
            apiKey: this.configService.get<string>('OPENAI_API_KEY'),
            model: this.modelConfigService.getLLMModel(),
        })

        // Use GeminiEmbedding directly from the @llamaindex/google package
        Settings.embedModel = new GeminiEmbedding({
          model: GEMINI_EMBEDDING_MODEL.EMBEDDING_001,
        });

        // Configure chunk size to accommodate larger metadata
        Settings.chunkSize = 2048;  // Increased from 512 to accommodate metadata length of 1329+
        Settings.chunkOverlap = 200; // Increased overlap to maintain context

        // Get vector collection
        const vectorStore = this.vectorDBService.getVectorCollection();

        // Create a storage context with the vector store
        const storageContext = await storageContextFromDefaults({
          vectorStore: vectorStore,
        });

        this.logger.debug(`Creating vector index with ${processedDocuments.length} processed documents`);

        if (processedDocuments.length === 0) {
          this.logger.warn(`No documents were processed from file: ${filePath}. Returning an empty index.`);
          // Assuming storageContext is already initialized and vectorStore is available
          return VectorStoreIndex.init({ nodes: [], storageContext });
        }

        // Create index with storage context
        return await VectorStoreIndex.fromDocuments(processedDocuments, {
          storageContext,
        });
      } catch (error) {
        this.logger.error(`Error processing PDF: ${error.message}`, error.stack);
        throw new HttpException(`Failed to process PDF: ${error.message}`, HttpStatus.BAD_REQUEST);
      }
    }

    throw new Error(`Unsupported file type: ${fileExtension}`);
  }

  /**
   * Store a narrative structure for a specific school
   * @param schoolId The school ID
   * @param narrativeContent The narrative structure content
   * @param metadata Additional metadata about the extraction
   * @returns The document ID
   */
  async storeNarrativeStructure(
    schoolId: string,
    narrativeContent: string,
    metadata?: Record<string, any>
  ): Promise<string> {
    try {
      this.logger.debug(`Storing narrative structure for school ID: ${schoolId}`);

      // First, delete any existing narrative structure for this school
      await this.deleteNarrativeStructure(schoolId);

      // Generate a document ID
      const docId = `narrative-structure-${schoolId}-${Date.now()}`;

      // Generate embeddings for the narrative content
      const embedding = await this.embeddingService.getQueryEmbedding(narrativeContent);

      // Create a document with the narrative content and metadata
      const document: VectorDBDocument = {
        id: docId,
        embedding: embedding,
        metadata: {
          schoolId: schoolId,
          documentType: 'narrativeStructure',
          textContent: narrativeContent,
          timestamp: Date.now(),
          ...metadata
        }
      };

      // Store the document in the vector database
      await this.vectorDBService.upsertDocuments([document]);

      this.logger.debug(`Successfully stored narrative structure for school ID: ${schoolId}`);
      return docId;
    } catch (error) {
      this.logger.error(`Error storing narrative structure: ${error.message}`, error.stack);
      throw new Error(`Failed to store narrative structure: ${error.message}`);
    }
  }

  /**
   * Get the narrative structure for a specific school
   * @param schoolId The school ID
   * @returns The narrative structure text or null if not found
   */
  async getNarrativeStructure(schoolId: string): Promise<string | null> {
    try {
      if (!schoolId) {
        this.logger.warn('No schoolId provided for narrative structure retrieval');
        return null;
      }

      this.logger.debug(`Retrieving narrative structure for school ID: ${schoolId}`);

      // Query the vector database for documents with the specified metadata
      const documents = await this.vectorDBService.queryDocumentsByMetadata({
        schoolId: schoolId,
        documentType: 'narrativeStructure'
      }, 1); // Limit to 1 result (most recent)

      if (!documents || documents.length === 0) {
        this.logger.debug(`No narrative structure found for school ID: ${schoolId}`);
        return null;
      }

      // Get the first document (most recent)
      const document = documents[0];
      const textContent = document.metadata?.textContent as string;

      if (!textContent) {
        this.logger.warn(`Narrative structure document found but no text content for school ID: ${schoolId}`);
        return null;
      }

      this.logger.debug(`Successfully retrieved narrative structure for school ID: ${schoolId}`);
      return textContent;
    } catch (error) {
      this.logger.error(`Error retrieving narrative structure: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Delete the narrative structure for a specific school
   * @param schoolId The school ID
   * @returns True if deletion was successful, false otherwise
   */
  async deleteNarrativeStructure(schoolId: string): Promise<boolean> {
    try {
      this.logger.debug(`Deleting narrative structure for school ID: ${schoolId}`);

      // Query the vector database for documents with the specified metadata
      const documents = await this.vectorDBService.queryDocumentsByMetadata({
        schoolId: schoolId,
        documentType: 'narrativeStructure'
      });

      if (!documents || documents.length === 0) {
        this.logger.debug(`No narrative structure found to delete for school ID: ${schoolId}`);
        return true; // Consider it successful if nothing to delete
      }

      // Extract document IDs
      const documentIds = documents.map(doc => doc.id);

      // Delete the documents from the vector database
      await this.vectorDBService.deleteDocuments(documentIds);

      this.logger.debug(`Successfully deleted ${documentIds.length} narrative structure documents for school ID: ${schoolId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error deleting narrative structure: ${error.message}`, error.stack);
      return false;
    }
  }
}
