import { Injectable, Logger, forwardRef, Inject } from '@nestjs/common';
import { AiService } from '../../ai/ai.service';
import { ModelConfigService } from '../../ai/model-config.service';
import { AiServiceLogService } from '../../gen-image/ai-service-log.service';
import { UserRequest } from '../dto/genPrompt.dto';
import { ExerciseQuestionItem, ExerciseResult } from '../interfaces/exercise-result.interface';
import { FileMetadata } from '../../files/interfaces/file.interface';
import { FileAttachmentService } from './file-attachment.service';
import { JsonParsingStrategy } from '../strategies/response-parsing';
import { ResponseValidator } from '../strategies/response-parsing';
import { ImageStrategyFactory } from '../strategies/image-generation';
import { DocumentsService } from '../../documents/documents.service';
import OpenAI from 'openai';
import { v4 as uuidv4 } from 'uuid';

/**
 * Service for generating questions using AI
 */
@Injectable()
export class QuestionGeneratorService {
  private readonly logger = new Logger(QuestionGeneratorService.name);

  constructor(
    private modelConfigService: ModelConfigService,
    @Inject(forwardRef(() => AiService))
    private aiService: AiService,
    @Inject(forwardRef(() => FileAttachmentService))
    private fileAttachmentService: FileAttachmentService,
    @Inject(forwardRef(() => DocumentsService))
    private documentsService: DocumentsService,
    private jsonParsingStrategy: JsonParsingStrategy,
    private responseValidator: ResponseValidator,
    private imageStrategyFactory: ImageStrategyFactory,
    private aiServiceLogService: AiServiceLogService,
  ) {}

  /**
   * Generates questions using the provided prompts
   * @param systemPrompt The system prompt
   * @param userPrompt The user prompt
   * @param userRequest Optional user request parameters
   * @param isFirstQuery
   * @returns The generated exercise result with exactly the number of questions specified in userRequest.totalQuestions
   */
  async generateQuestions(
    systemPrompt: string,
    userPrompt: string,
    userRequest?: UserRequest,
    isFirstQuery: boolean = true // Flag to indicate if this is the first query for a document
  ): Promise<ExerciseResult> {
    const model = this.modelConfigService.getQuestionGenerationModel();

    // Use the relevantContent from userPrompt or get it from vector database if not provided
    let relevantContent: string | null = null;

    // Get narrative structure if schoolId is provided (preferred over raw examination format)
    let narrativeStructureText: string | null = null;
    let examinationFormatText: string | null = null;

    // Enhanced logging to track schoolId flow
    this.logger.log(`QuestionGeneratorService: Checking for schoolId in userRequest. schoolId: ${userRequest?.schoolId || 'NOT PROVIDED'}`);

    if (userRequest?.schoolId) {
      try {
        this.logger.log(`QuestionGeneratorService: Retrieving narrative structure for school ID: ${userRequest.schoolId}`);
        narrativeStructureText = await this.documentsService.getNarrativeStructure(userRequest.schoolId);

        if (narrativeStructureText) {
          this.logger.log(`QuestionGeneratorService: Successfully found narrative structure for school ID: ${userRequest.schoolId} (${narrativeStructureText.length} characters)`);
        } else {
          this.logger.warn(`QuestionGeneratorService: No narrative structure found for school ID: ${userRequest.schoolId}, falling back to examination format`);

          // Fallback to examination format if narrative structure is not available
          examinationFormatText = await this.documentsService.getExaminationFormat(userRequest.schoolId);

          if (examinationFormatText) {
            this.logger.log(`QuestionGeneratorService: Found examination format for school ID: ${userRequest.schoolId} (${examinationFormatText.length} characters)`);
          } else {
            this.logger.warn(`QuestionGeneratorService: No examination format found for school ID: ${userRequest.schoolId}`);
          }
        }
      } catch (error) {
        this.logger.error(`QuestionGeneratorService: Error retrieving narrative structure/examination format for school ID ${userRequest.schoolId}: ${error.message}`, error.stack);
      }
    } else {
        this.logger.warn('QuestionGeneratorService: No school ID provided in userRequest, skipping narrative structure and examination format retrieval');
        // Log the entire userRequest structure for debugging (excluding sensitive data)
        const debugUserRequest = userRequest ? {
          grade: userRequest.grade,
          topic: userRequest.topic,
          subject: userRequest.subject,
          parentSubject: userRequest.parentSubject,
          difficulty: userRequest.difficulty,
          totalQuestions: userRequest.totalQuestions,
          schoolId: userRequest.schoolId,
          hasSchoolId: !!userRequest.schoolId
        } : 'null';
        this.logger.debug(`QuestionGeneratorService: UserRequest debug info: ${JSON.stringify(debugUserRequest)}`);
    }

    // Check if we already have relevant content in the userPrompt
    const relevantContentInPrompt = this.extractRelevantContentFromPrompt(userPrompt);

    if (relevantContentInPrompt) {
      // Use the content already provided in the prompt
      this.logger.log('Using relevant content from prompt');
      relevantContent = relevantContentInPrompt;
    } else if (userRequest) {
      // If no content in prompt, query the vector database
      try {
        this.logger.log('No relevant content found in prompt. Querying vector database...');

        // Construct a simplified query to avoid timeout issues
        // Focus on the most important terms only
        let mainTopic = '';
        if (userRequest.topic &&  userRequest.topic.trim() !== '') {
          mainTopic = userRequest.topic.trim();
        } else {
          mainTopic = 'mathematics science english';
        }

        let mainGrade = '';
        if (userRequest.grade &&  userRequest.grade.trim() !== '') {
          mainGrade = userRequest.grade.trim();
        } else {
          mainGrade = 'primary secondary';
        }

        let queryTerms: string[] = [];

        // Add specific lesson content if available
        if (userRequest.subject && userRequest.subject.trim() !== '') {
          queryTerms.push(userRequest.subject.trim());
        }

        // Add chapter/unit if available
        if (userRequest.parentSubject && userRequest.parentSubject.trim() !== '') {
          queryTerms.push(userRequest.parentSubject.trim());
        }

        // Add main subject area
        if (mainTopic !== '') {
          queryTerms.push(mainTopic);
        }

        // Add grade level
        if (mainGrade !== '') {
          queryTerms.push(mainGrade);
        }

        // Add examination context
        queryTerms.push('examination questions');

        // Join terms with higher weight for more specific terms
        const query = queryTerms.join(' ');
        this.logger.log(`Querying vector database for content with: "${query}"`);

        let category;

        // Build category from most specific to most general
        if (userRequest.subject && userRequest.subject.trim() !== '' &&
            userRequest.parentSubject && userRequest.parentSubject.trim() !== '' &&
            userRequest.topic && userRequest.topic.trim() !== '') {
          // If we have all three levels, use the most specific combination
          category = `${userRequest.subject} ${userRequest.parentSubject} ${userRequest.topic}`;
        } else if (userRequest.parentSubject && userRequest.parentSubject.trim() !== '' &&
                  userRequest.topic && userRequest.topic.trim() !== '') {
          // If we have chapter and main subject area
          category = `${userRequest.parentSubject} ${userRequest.topic}`;
        } else if (userRequest.subject && userRequest.subject.trim() !== '' &&
                  userRequest.parentSubject && userRequest.parentSubject.trim() !== '') {
          // If we have specific lesson and chapter
          category = `${userRequest.subject} ${userRequest.parentSubject}`;
        } else if (userRequest.topic && userRequest.topic.trim() !== '') {
          // If we have just the main subject area
          category = `${userRequest.topic} examination`;
        } else if (userRequest.parentSubject && userRequest.parentSubject.trim() !== '') {
          // If we have just the chapter/unit
          category = `${userRequest.parentSubject} examination`;
        } else if (userRequest.subject && userRequest.subject.trim() !== '') {
          // If we have just the specific lesson content
          category = `${userRequest.subject} examination`;
        } else if (userRequest.grade && userRequest.grade.trim() !== '') {
          // If we have just the grade
          category = `${userRequest.grade} examination`;
        } else {
          // If nothing specific, use generic examination category
          category = 'examination';
        }
        this.logger.log(`Using category: "${category}" for vector database query`);

        try {
          this.logger.log(`Attempting query with category: "${category}"`);
          relevantContent = await this.documentsService.queryDocuments(
            query,
            5, // Get top 5 most relevant chunks to avoid timeout
            60000, // 60 second timeout
            category, // Use combined category
            isFirstQuery // Skip cache only for first query
          );
        } catch (queryError) {
          this.logger.error(`Query attempt failed: ${queryError.message}`);
          // Use fallback content
          relevantContent = this.generateFallbackContent(userRequest);
        }

        // Ensure the content isn't too long for the API call (max ~8000 tokens)
        const maxContentLength = 16000; // ~8000 tokens
        if (relevantContent && relevantContent.length > maxContentLength) {
          this.logger.log(`Truncating relevant content from ${relevantContent.length} to ${maxContentLength} characters`);
          relevantContent = relevantContent.substring(0, maxContentLength) + '\n[Content truncated due to length]';
        }

        // Log detailed information about the results
        if (relevantContent && relevantContent.trim() !== '') {
          this.logger.log(`Successfully retrieved ${relevantContent.length} characters of relevant content`);
          // Log a preview of the content (first 100 characters)
          const contentPreview = relevantContent.substring(0, 100).replace(/\n/g, ' ');
          this.logger.log(`Content preview: "${contentPreview}..."`);
        } else {
          this.logger.error(`Failed to retrieve any relevant content. Using fallback content.`);
          relevantContent = this.generateFallbackContent(userRequest);
          this.logger.log(`Using fallback content of ${relevantContent.length} characters`);
        }
      } catch (error) {
        this.logger.error(`Failed to retrieve relevant content: ${error.message}`, error.stack);
      }
    }

    // If we have a narrative structure or examination format, modify the system prompt to include it
    let finalSystemPrompt = systemPrompt;
    if (narrativeStructureText && userRequest) {
      this.logger.log(`Incorporating narrative structure into system prompt`);

      const narrativeBlock = `
      # SCHOOL QUESTION CONTEXT NARRATIVE STRUCTURE
      The following narrative structure captures how this school writes and structures their questions context.
      The structure follows the format: ${narrativeStructureText}

      # APPLICATION INSTRUCTIONS
      When generating questions, follow this school's specific pattern:
      1. **Professional/Technical Context**: Frame questions using the school's approach to real-world or technical contexts
      2. **Precise Initial Conditions**: Present given information and starting conditions in the school's style
      3. **System Changes**: Describe what happens or what needs to be analyzed using the school's language patterns
      4. **Mathematical Precision Requirements**: Specify expected precision, units, or answer formats as this school does
      5. Adapt the complexity and vocabulary to be appropriate for the specified grade level
      6. Maintain the school's structural approach while ensuring content is grade-appropriate
      7. Follow the school's formatting and presentation style for questions and answer choices
      8. Use consistent terminology and phrasing as defined in the narrative structure
      9. Apply the same difficulty progression and question patterns, adapted to grade level
      10. Every character, word, and punctuation must match the school's style
      11. Every character will have a specific meaning and purpose in the context of the question
      12. Every character will be used to convey a specific piece of information or instruction
      13. Every character will be used to create a specific context or scenario for the question
      14. Every character must have a name, and it must be used consistently throughout the question
      15. Every scenario must be normal real-world scenarios in Singapore, and must be appropriate for the target grade level
      16. Every location, object must have a real-world counterpart in Singapore, and must be appropriate for the target grade level
      17. Every question must have words played for specific meanings, and must be appropriate for the target grade level
      18. Every question must have words played for students to think critically, and must be appropriate for the target grade level
      19. Every question must have meaningful context, and must be appropriate for the target grade level
        

      CRITICAL: You MUST strictly adhere to this narrative structure in ALL generated questions.
      `;
      finalSystemPrompt = `${systemPrompt}\n\n${narrativeBlock}`;
    } else if (examinationFormatText && userRequest) {
      this.logger.log(`Incorporating examination format into system prompt (fallback)`);

      const examinationBlock = `
      # EXAMINATION FORMAT GUIDELINES
      The following guidelines define the examination format for this school. Adhere to them while ensuring grade-appropriate content:
      ${examinationFormatText}

      # APPLICATION INSTRUCTIONS
      When generating questions:
      1. EVERY question MUST follow the examination format guidelines above while being appropriate for the target grade
      2. Match the exact style, structure, and language patterns shown in the guidelines
      3. Maintain consistent terminology as defined in the guidelines
      4. Apply the same difficulty progression and question patterns, adapted to grade level
      5. Use identical formatting for options, solutions, and explanations
      6. Preserve any special instructions or requirements from the guidelines

      CRITICAL: You MUST strictly follow these examination guidelines in ALL generated questions.
      `;
      finalSystemPrompt = `${systemPrompt}\n\n${examinationBlock}`;
    }

    // Modify userPrompt to include narrative structure or examination format
    let finalUserPrompt = userPrompt;
    if (narrativeStructureText) {
      this.logger.log(`Incorporating narrative structure into userPrompt`);
      const narrativeBlockForUserPrompt = `
      === SCHOOL QUESTION CONTEXT NARRATIVE STRUCTURE ===
      The following narrative structure need to be followed when generating questions context for this school:
      ${narrativeStructureText}
      === END OF SCHOOL QUESTION CONTEXT NARRATIVE STRUCTURE ===

      Please ensure the generated questions align with this structure.
      `;
      finalUserPrompt = `${userPrompt}\n\n${narrativeBlockForUserPrompt}`;
    } else if (examinationFormatText) {
      this.logger.log(`Incorporating examination format into userPrompt (fallback)`);
      const examinationBlockForUserPrompt = `
      === EXAMINATION FORMAT GUIDELINES ===
      The following guidelines define the examination format for this school:
      ${examinationFormatText}
      === END OF EXAMINATION FORMAT GUIDELINES ===

      Please ensure the generated questions align with these guidelines.
      `;
      finalUserPrompt = `${userPrompt}\n\n${examinationBlockForUserPrompt}`;
    }

    // Prepare the messages for the chat
    const messages = [
      {
        role: 'system' as const,
        content: finalSystemPrompt,
      },
      {
        role: "assistant" as const,
        content: "You are a developer who can generate JSON code. Please respond with only the JSON code.",
      },
      {
        role: "assistant" as const,
        content: "You are an assistance who have knowledge about teaching and education. Please respond with only the JSON code.",
      },
      {
        role: 'user' as const,
        content: [
          {
            type: "text",
            text: finalUserPrompt, // Use the modified userPrompt
          }
        ],
      },
    ];

    // Add relevant content to user message if available
    if (relevantContent && relevantContent.trim().length > 0) {
      // Format the content to clearly indicate it's reference material
      // Include the full subject hierarchy for better context
      let hierarchyDescription = '';

      if (userRequest) {
        // Build a description based on the available hierarchy components
        const hasTopic = userRequest.topic && userRequest.topic.trim() !== '';
        const hasParentSubject = userRequest.parentSubject && userRequest.parentSubject.trim() !== '';
        const hasSubject = userRequest.subject && userRequest.subject.trim() !== '';

        if (hasSubject && hasParentSubject && hasTopic) {
          // All three levels available
          hierarchyDescription = `the "${userRequest.subject}" lesson within the "${userRequest.parentSubject}" chapter of "${userRequest.topic}"`;
        } else if (hasSubject && hasParentSubject) {
          // Specific lesson and chapter
          hierarchyDescription = `the "${userRequest.subject}" lesson within the "${userRequest.parentSubject}" chapter`;
        } else if (hasParentSubject && hasTopic) {
          // Chapter and main subject area
          hierarchyDescription = `the "${userRequest.parentSubject}" chapter of "${userRequest.topic}"`;
        } else if (hasSubject && hasTopic) {
          // Specific lesson and main subject area
          hierarchyDescription = `the "${userRequest.subject}" lesson in "${userRequest.topic}"`;
        } else if (hasSubject) {
          // Just specific lesson
          hierarchyDescription = `the "${userRequest.subject}" lesson`;
        } else if (hasParentSubject) {
          // Just chapter
          hierarchyDescription = `the "${userRequest.parentSubject}" chapter`;
        } else if (hasTopic) {
          // Just main subject area
          hierarchyDescription = `the "${userRequest.topic}" subject area`;
        } else {
          // Fallback
          hierarchyDescription = 'the requested topic';
        }
      } else {
        hierarchyDescription = 'the requested topic';
      }

      const formattedContent = `
        === REFERENCE MATERIAL FROM CURRICULUM DATABASE ===
        The following content is relevant to ${hierarchyDescription} for grade ${userRequest?.grade || 'level'} and contains examination materials, examples, and explanations that should be used as reference:

        ${relevantContent}
        === END OF REFERENCE MATERIAL ===

        Please use the above reference material to help generate accurate, curriculum-aligned questions.
      `;

      // Add the relevant content as context in the message
      (messages[3].content as any[]).push({
        type: 'text',
        text: formattedContent,
      });

      this.logger.log(`Added ${relevantContent.length} characters of relevant content from vector database to the message`);
    } else {
      this.logger.warn('No relevant content found in vector database. Questions will be generated without specific curriculum references.');
    }

    try {
      // Log the request before sending to AI
      const promptsForLog: string[] = [];
      messages.forEach(msg => {
        if (msg.role !== 'system') {
          if (typeof msg.content === 'string') {
            promptsForLog.push(msg.content);
          } else if (Array.isArray(msg.content)) {
            msg.content.forEach(part => {
              if (part.type === 'text') {
                promptsForLog.push(part.text);
              }
              // Potentially handle other types like 'image_url' if needed for logging
            });
          }
        }
      });

      await this.aiServiceLogService.logAiServiceRequest({
        worksheetId: userRequest?.worksheetId, // Assuming worksheetId might be on UserRequest
        status: 'success', // Logging the attempt to send
        prompts: promptsForLog,
        systemPrompt: finalSystemPrompt,
      });

      // Make the API call with or without relevant content
      const response = await this.aiService.chat(
        model,
        messages as OpenAI.ChatCompletionMessageParam[]
      );

      if (!response || !response.choices || response.choices.length === 0) {
        throw new Error('Empty or invalid response from AI service');
      }

      const content = response.choices[0].message.content as string;

      if (!content) {
        throw new Error('Empty content in AI response');
      }

      // Parse the response
      const data = this.jsonParsingStrategy.parseResponse(content);

      // Validate the response
      const validatedData = this.responseValidator.validateResponse(data);

      // Process each item in the result array
      const processedItems = await Promise.all(
        validatedData.result.map(async (item: any): Promise<ExerciseQuestionItem | null> => {
          return await this.processQuestionItem(item, userRequest);
        })
      );

      // Filter out null items
      let validResults = this.responseValidator.filterValidItems(processedItems);

      this.logger.log(`Generated ${validResults.length} valid questions out of ${data.result.length} total items`);

      // Ensure we don't return more questions than requested
      if (userRequest && userRequest.totalQuestions && validResults.length > userRequest.totalQuestions) {
        this.logger.warn(
          `Generated ${validResults.length} questions but only ${userRequest.totalQuestions} were requested. Trimming excess questions.`
        );
        validResults = validResults.slice(0, userRequest.totalQuestions);
      }

      return {
        result: validResults,
      } as ExerciseResult;
    } catch (error) {
      this.logger.error(`Error in generateQuestions: ${error.message}`, error.stack);
      // Return an empty result if there's an error
      return {
        result: []
      } as ExerciseResult;
    }
  }

  /**
   * Processes a single question item, skipping image generation for batch processing
   * @param item The question item to process
   * @param userRequest Optional user request parameters
   * @returns The processed question item
   */
  private async processQuestionItem(
    item: any,
    userRequest?: UserRequest
  ): Promise<ExerciseQuestionItem | null> {
    // Ensure item is an object
    if (!item || typeof item !== 'object') {
      this.logger.warn(`Skipping invalid item in result array: ${JSON.stringify(item)}`);
      return null;
    }

    // Generate a unique ID for this question if it doesn't already have one
    const questionId = item.id || uuidv4();

    // Extract image prompt and question content for validation only
    const imagePrompt = item.imagePrompt && item.imagePrompt.trim() !== '' ? item.imagePrompt.trim() : '';
    const questionContent = item.content ? item.content.trim() : '';

    // Create the appropriate image generation strategy just to check if an image should be generated
    const topic = userRequest?.topic || '';
    const imageStrategy = this.imageStrategyFactory.createStrategy(imagePrompt, questionContent, topic);

    // Skip actual image generation - we'll do this in batch later
    // Just set image to empty string for now
    if (imageStrategy.shouldGenerateImage(imagePrompt, questionContent, topic) &&
        ((imagePrompt && imagePrompt.trim() !== '') || (questionContent && questionContent.trim() !== ''))) {
      // Log that we're skipping individual image generation
      this.logger.debug(`Skipping individual image generation for batch processing. Image prompt: ${imagePrompt.substring(0, 50)}...`);
      return { ...item, id: questionId, image: '' };
    } else {
      return { ...item, id: questionId, image: '' };
    }
  }

  /**
   * Extracts relevant content from the user prompt if it exists
   * @param userPrompt The user prompt to extract content from
   * @returns The extracted content or null if none found
   */
  private extractRelevantContentFromPrompt(userPrompt: string): string | null {
    // Look for curriculum content section in the prompt
    const curriculumContentRegex = /## CURRICULUM CONTENT\s*([\s\S]*?)(?:##|$)/;
    const match = userPrompt.match(curriculumContentRegex);

    if (match && match[1] && match[1].trim().length > 0) {
      this.logger.log('Found curriculum content in prompt');
      return match[1].trim();
    }

    return null;
  }

  /**
   * Generates fallback content when no relevant content is found
   * @param userRequest The user request parameters
   * @returns Generic fallback content
   */
  private generateFallbackContent(userRequest?: UserRequest): string {
    const subject = userRequest?.topic || 'general subjects';
    const grade = userRequest?.grade || 'school';

    return `
    # Examination Content for ${subject} at ${grade} level

    ## Sample Questions

    1. Multiple choice questions are common in examinations.
    2. Short answer questions test understanding of key concepts.
    3. Essay questions evaluate deeper understanding and critical thinking.
    4. Problem-solving questions are important for mathematics and sciences.
    5. Diagram interpretation questions test visual understanding.

    ## Key Concepts

    - Fundamental principles should be clearly understood
    - Application of concepts to real-world scenarios
    - Analysis and evaluation of information
    - Clear communication of ideas and solutions

    ## Examination Format

    Typical examinations include a mix of question types and difficulty levels to assess different skills and knowledge areas.
    `;
  }
}
