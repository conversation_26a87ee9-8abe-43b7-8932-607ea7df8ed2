import { WebSocketGateway, WebSocketServer } from '@nestjs/websockets';
import { Server } from 'socket.io';
import { Injectable, Logger } from '@nestjs/common';
import {
  WebSocketErrorCode,
  WebSocketErrorEvent,
  WebSocketErrorSeverity,
  ERROR_SEVERITY_MAP
} from '../../core/enums/websocket-error-codes.enum';

@WebSocketGateway({
  namespace: 'ws',
  cors: {
    origin: '*',
  },
})
@Injectable()
export class SocketGateway {
  @WebSocketServer()
  server: Server;

  private logger: Logger = new Logger('SocketGateway');

  emitWorksheetGenerated(worksheetId: string, data: any) {
    this.server.emit('worksheet:generated', {
      worksheetId,
      data,
      timestamp: new Date().toISOString(),
    });
    this.logger.log(`Emitted worksheet:generated for worksheet ${worksheetId}`);
  }

  /**
   * Emits progress updates for worksheet generation and image generation
   *
   * Events emitted:
   * - 'worksheet:progress': General progress updates for worksheet generation
   *   - For question generation: includes current/total question counts
   *   - For image generation: includes imageGenerationStarted, imageGenerationProgress, or imageGenerationComplete flags
   *
   * - 'worksheet:images:complete': Specific event when all images are generated
   * - 'worksheet:images:error': Specific event when image generation encounters an error
   *
   * @param worksheetId The ID of the worksheet
   * @param currentQuestionCount Current count of questions or images processed
   * @param totalQuestionCount Total count of questions or images to process
   * @param questionResult Optional result data to include in the event
   */
  emitWorksheetProgress(worksheetId: string, currentQuestionCount: number, totalQuestionCount: number, questionResult?: any) {
    // Prevent division by zero
    const percentage = totalQuestionCount > 0
      ? Math.round((currentQuestionCount / totalQuestionCount) * 100)
      : 0;

    this.server.emit('worksheet:progress', {
      worksheetId,
      progress: {
        current: currentQuestionCount,
        total: totalQuestionCount,
        percentage: percentage,
      },
      questionResult,
      timestamp: new Date().toISOString(),
    });

    // Log different messages based on the type of progress update
    if (questionResult && questionResult.imageGenerationStarted) {
      this.logger.log(`Emitted worksheet:progress for worksheet ${worksheetId}: Image generation started`);
    } else if (questionResult && questionResult.imageGenerationProgress) {
      // Include batch information if available
      const batchInfo = questionResult.currentBatch && questionResult.totalBatches
        ? ` (batch ${questionResult.currentBatch}/${questionResult.totalBatches})`
        : '';
      this.logger.log(`Emitted worksheet:progress for worksheet ${worksheetId}: Image generation progress ${currentQuestionCount}/${totalQuestionCount}${batchInfo}`);
    } else if (questionResult && questionResult.imageGenerationComplete) {
      this.logger.log(`Emitted worksheet:progress for worksheet ${worksheetId}: Image generation complete`);
    } else if (questionResult && questionResult.imageGenerationError) {
      this.logger.log(`Emitted worksheet:progress for worksheet ${worksheetId}: Image generation error: ${questionResult.errorMessage || 'Unknown error'}`);
    } else {
      this.logger.log(`Emitted worksheet:progress for worksheet ${worksheetId}: ${currentQuestionCount}/${totalQuestionCount}`);
    }
  }

  /**
   * Emit structured error events for worksheet generation failures
   *
   * @param worksheetId The ID of the worksheet
   * @param errorCode Specific error code from WebSocketErrorCode enum
   * @param message User-friendly error message
   * @param details Optional additional error details (non-sensitive)
   * @param context Optional context information (e.g., operation that failed)
   */
  emitWorksheetError(
    worksheetId: string,
    errorCode: WebSocketErrorCode,
    message: string,
    details?: any,
    context?: string
  ) {
    const severity = ERROR_SEVERITY_MAP[errorCode] || WebSocketErrorSeverity.MEDIUM;
    const errorEvent = this.getErrorEventType(errorCode);

    const errorPayload = {
      worksheetId,
      errorCode,
      message,
      severity,
      timestamp: new Date().toISOString(),
      ...(details && { details }),
      ...(context && { context }),
    };

    // Emit specific error event
    this.server.emit(errorEvent, errorPayload);

    // Also emit general worksheet generation error for backward compatibility
    this.server.emit(WebSocketErrorEvent.WORKSHEET_GENERATION_ERROR, errorPayload);

    this.logger.error(
      `Emitted ${errorEvent} for worksheet ${worksheetId}: ${errorCode} - ${message}` +
      (context ? ` (context: ${context})` : '')
    );
  }

  /**
   * Emit database-related errors
   */
  emitDatabaseError(
    worksheetId: string,
    errorCode: WebSocketErrorCode,
    message: string,
    operation?: string
  ) {
    this.emitWorksheetError(
      worksheetId,
      errorCode,
      message,
      undefined,
      operation ? `Database operation: ${operation}` : 'Database operation'
    );
  }

  /**
   * Emit AI service-related errors
   */
  emitAiServiceError(
    worksheetId: string,
    errorCode: WebSocketErrorCode,
    message: string,
    provider?: string,
    attemptDetails?: any
  ) {
    this.emitWorksheetError(
      worksheetId,
      errorCode,
      message,
      attemptDetails,
      provider ? `AI Provider: ${provider}` : 'AI Service'
    );
  }

  /**
   * Emit question pool-related errors
   */
  emitQuestionPoolError(
    worksheetId: string,
    errorCode: WebSocketErrorCode,
    message: string,
    poolDetails?: any
  ) {
    this.emitWorksheetError(
      worksheetId,
      errorCode,
      message,
      poolDetails,
      'Question Pool Operation'
    );
  }

  /**
   * Emit system-level errors
   */
  emitSystemError(
    worksheetId: string,
    errorCode: WebSocketErrorCode,
    message: string,
    systemDetails?: any
  ) {
    this.emitWorksheetError(
      worksheetId,
      errorCode,
      message,
      systemDetails,
      'System Operation'
    );
  }

  /**
   * Emit retry attempt notifications
   */
  emitRetryAttempt(
    worksheetId: string,
    operation: string,
    attempt: number,
    maxAttempts: number,
    nextRetryDelay?: number
  ) {
    const retryPayload = {
      worksheetId,
      operation,
      attempt,
      maxAttempts,
      nextRetryDelay,
      timestamp: new Date().toISOString(),
    };

    this.server.emit('worksheet:retry:attempt', retryPayload);

    this.logger.log(
      `Emitted retry attempt for worksheet ${worksheetId}: ${operation} (${attempt}/${maxAttempts})` +
      (nextRetryDelay ? ` - next retry in ${nextRetryDelay}ms` : '')
    );
  }

  /**
   * Emit fallback activation notifications
   */
  emitFallbackActivated(
    worksheetId: string,
    primaryOperation: string,
    fallbackOperation: string,
    reason: string
  ) {
    const fallbackPayload = {
      worksheetId,
      primaryOperation,
      fallbackOperation,
      reason,
      timestamp: new Date().toISOString(),
    };

    this.server.emit('worksheet:fallback:activated', fallbackPayload);

    this.logger.log(
      `Emitted fallback activation for worksheet ${worksheetId}: ${primaryOperation} -> ${fallbackOperation} (${reason})`
    );
  }

  /**
   * Determine the appropriate error event type based on error code
   */
  private getErrorEventType(errorCode: WebSocketErrorCode): WebSocketErrorEvent {
    // Database errors
    if ([
      WebSocketErrorCode.DATABASE_UNAVAILABLE,
      WebSocketErrorCode.DATABASE_CONNECTION_FAILED,
      WebSocketErrorCode.DATABASE_TIMEOUT,
      WebSocketErrorCode.DATABASE_QUERY_FAILED,
    ].includes(errorCode)) {
      return WebSocketErrorEvent.DATABASE_ERROR;
    }

    // AI service errors
    if ([
      WebSocketErrorCode.AI_SERVICE_FAILED,
      WebSocketErrorCode.OPENAI_SERVICE_FAILED,
      WebSocketErrorCode.GOOGLE_AI_SERVICE_FAILED,
      WebSocketErrorCode.AI_GENERATION_TIMEOUT,
      WebSocketErrorCode.AI_RESPONSE_INVALID,
      WebSocketErrorCode.ALL_AI_SERVICES_FAILED,
    ].includes(errorCode)) {
      return WebSocketErrorEvent.AI_SERVICE_ERROR;
    }

    // Question pool errors
    if ([
      WebSocketErrorCode.INSUFFICIENT_QUESTIONS,
      WebSocketErrorCode.QUESTION_POOL_EMPTY,
      WebSocketErrorCode.QUESTION_POOL_UNAVAILABLE,
      WebSocketErrorCode.QUESTION_VALIDATION_FAILED,
      WebSocketErrorCode.INSUFFICIENT_QUESTIONS_NO_FALLBACK,
    ].includes(errorCode)) {
      return WebSocketErrorEvent.QUESTION_POOL_ERROR;
    }

    // System errors
    if ([
      WebSocketErrorCode.INVALID_CONFIGURATION,
      WebSocketErrorCode.FEATURE_DISABLED,
      WebSocketErrorCode.MAX_RETRIES_REACHED,
      WebSocketErrorCode.SYSTEM_OVERLOAD,
      WebSocketErrorCode.INTERNAL_SERVER_ERROR,
    ].includes(errorCode)) {
      return WebSocketErrorEvent.SYSTEM_ERROR;
    }

    // Default to worksheet generation error
    return WebSocketErrorEvent.WORKSHEET_GENERATION_ERROR;
  }
}
