import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Logger } from '@nestjs/common';
import { QuestionPoolService } from './question-pool.service';
import { QuestionPool } from '../mongodb/schemas/question-pool.schema';
import { QuestionPoolCacheService } from './services/question-pool-cache.service';
import { QuestionPoolMetricsService } from './services/question-pool-metrics.service';
import { PoolMonitoringService } from '../monitoring/services/pool-monitoring.service';
import { ContentValidationService } from '../validation/content-validation.service';
import { QuestionPoolConfigService } from './question-pool-config.service';
import { ExerciseQuestionItem } from '../prompt/interfaces/exercise-result.interface';

/**
 * Test suite specifically for the field consistency fix
 * Tests the fix for the critical bug where questions with optionValue.value 
 * but no childSubject field cannot be retrieved
 */
describe('QuestionPoolService - Field Consistency Fix', () => {
  let service: QuestionPoolService;
  let mockQuestionPoolModel: any;
  let mockCacheService: jest.Mocked<QuestionPoolCacheService>;
  let mockMetricsService: jest.Mocked<QuestionPoolMetricsService>;
  let mockMonitoringService: jest.Mocked<PoolMonitoringService>;
  let mockContentValidationService: jest.Mocked<ContentValidationService>;
  let mockConfigService: jest.Mocked<QuestionPoolConfigService>;

  // Mock questions with different field combinations to test the fix
  const mockQuestionsWithOptionValue = [
    {
      _id: '507f1f77bcf86cd799439011',
      content: 'Question with optionValue only',
      type: 'multiple_choice',
      options: ['A', 'B', 'C', 'D'],
      answer: 'B',
      subject: 'Mathematics',
      parentSubject: 'Mathematics',
      // No childSubject field
      optionValue: { value: 'Addition', id: 'opt_123' },
      grade: 'Primary 2',
      language: 'English',
      status: 'active',
    },
    {
      _id: '507f1f77bcf86cd799439012',
      content: 'Question with both fields',
      type: 'multiple_choice',
      options: ['A', 'B', 'C', 'D'],
      answer: 'C',
      subject: 'Mathematics',
      parentSubject: 'Mathematics',
      childSubject: 'Multiplication',
      optionValue: { value: 'Multiplication', id: 'opt_456' },
      grade: 'Primary 3',
      language: 'English',
      status: 'active',
    },
    {
      _id: '507f1f77bcf86cd799439013',
      content: 'Question with childSubject only',
      type: 'fill_blank',
      answer: '5',
      subject: 'Mathematics',
      parentSubject: 'Mathematics',
      childSubject: 'Algebra',
      // No optionValue field
      grade: 'Secondary 1',
      language: 'English',
      status: 'active',
    },
  ];

  beforeEach(async () => {
    // Create mock aggregation pipeline
    const mockAggregate = jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue(mockQuestionsWithOptionValue),
    });

    // Create mock model with save functionality
    const mockSave = jest.fn().mockImplementation(function() {
      return Promise.resolve(this);
    });

    mockQuestionPoolModel = {
      aggregate: mockAggregate,
      find: jest.fn().mockReturnValue({
        sort: jest.fn().mockReturnValue({
          limit: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              exec: jest.fn().mockResolvedValue(mockQuestionsWithOptionValue),
            }),
          }),
        }),
      }),
      countDocuments: jest.fn().mockResolvedValue(100),
      distinct: jest.fn().mockResolvedValue(['Easy', 'Medium', 'Advanced']),
      // Mock constructor for new documents
      mockImplementation: jest.fn().mockImplementation((data) => ({
        ...data,
        save: mockSave,
      })),
    };

    // Mock the constructor call
    mockQuestionPoolModel.constructor = jest.fn().mockImplementation((data) => ({
      ...data,
      save: mockSave,
    }));

    // Create mock services
    mockCacheService = {
      getFromCache: jest.fn(),
      saveToCache: jest.fn(),
      invalidateCache: jest.fn(),
      generateCacheKey: jest.fn(),
    } as any;

    mockMetricsService = {
      recordQuery: jest.fn(),
      recordQueryDuration: jest.fn(),
      recordCacheHit: jest.fn(),
      recordCacheMiss: jest.fn(),
      updateQuestionCounts: jest.fn(),
      recordError: jest.fn(),
      recordDbQueryDuration: jest.fn(),
    } as any;

    mockMonitoringService = {
      emitEvent: jest.fn(),
    } as any;

    mockContentValidationService = {
      validateQuestion: jest.fn().mockResolvedValue({ isValid: true, score: 0.9 }),
    } as any;

    mockConfigService = {
      getConfig: jest.fn().mockReturnValue({
        distribution: {
          defaultDifficultyDistribution: { Easy: 0.3, Medium: 0.4, Advanced: 0.3 },
          defaultQuestionTypeBalancing: { enabled: false, preferDiversity: false },
          defaultDiversityConfig: { enabled: false },
          defaultQualityValidationConfig: { enabled: false },
          defaultFallbackConfig: { allowBestEffort: true, relaxDistributionOnShortfall: true, logFallbackReasons: true },
        },
      }),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuestionPoolService,
        {
          provide: getModelToken(QuestionPool.name),
          useValue: mockQuestionPoolModel,
        },
        {
          provide: QuestionPoolCacheService,
          useValue: mockCacheService,
        },
        {
          provide: QuestionPoolMetricsService,
          useValue: mockMetricsService,
        },
        {
          provide: PoolMonitoringService,
          useValue: mockMonitoringService,
        },
        {
          provide: ContentValidationService,
          useValue: mockContentValidationService,
        },
        {
          provide: QuestionPoolConfigService,
          useValue: mockConfigService,
        },
        {
          provide: Logger,
          useValue: {
            debug: jest.fn(),
            log: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<QuestionPoolService>(QuestionPoolService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Query Building with Field Consistency Fix', () => {
    it('should build $or query for childSubject to support both childSubject and optionValue.value', async () => {
      const filters = {
        subject: 'Mathematics',
        childSubject: 'Addition',
      };

      mockCacheService.getFromCache.mockResolvedValue(null);

      await service.getRandomQuestions(filters, 5);

      // Verify that the aggregation was called with $or condition
      const aggregateCall = mockQuestionPoolModel.aggregate.mock.calls[0][0];
      const matchStage = aggregateCall.find((stage: any) => stage.$match);

      expect(matchStage.$match).toMatchObject({
        subject: 'Mathematics',
        status: 'active',
        $or: [
          { childSubject: 'Addition' },
          { 'optionValue.value': 'Addition' }
        ],
      });
    });

    it('should build $or query in getQuestions method for backward compatibility', async () => {
      const filters = {
        subject: 'Mathematics',
        childSubject: 'Multiplication',
      };

      await service.getQuestions(filters, 10, 0);

      // Verify that the find was called with $or condition
      const findCall = mockQuestionPoolModel.find.mock.calls[0][0];

      expect(findCall).toMatchObject({
        subject: 'Mathematics',
        status: 'active',
        $or: [
          { childSubject: 'Multiplication' },
          { 'optionValue.value': 'Multiplication' }
        ],
      });
    });

    it('should not add $or condition when childSubject is not provided', async () => {
      const filters = {
        subject: 'Mathematics',
        grade: 'Primary 2',
      };

      mockCacheService.getFromCache.mockResolvedValue(null);

      await service.getRandomQuestions(filters, 5);

      const aggregateCall = mockQuestionPoolModel.aggregate.mock.calls[0][0];
      const matchStage = aggregateCall.find((stage: any) => stage.$match);

      expect(matchStage.$match).toMatchObject({
        subject: 'Mathematics',
        grade: 'Primary 2',
        status: 'active',
      });
      expect(matchStage.$match.$or).toBeUndefined();
    });
  });

  describe('addQuestion Method with Field Consistency', () => {
    it('should set childSubject when optionValue.value exists but childSubject is missing', async () => {
      const questionData: ExerciseQuestionItem = {
        type: 'multiple_choice',
        content: 'Test question',
        options: ['A', 'B', 'C', 'D'],
        answer: ['B'],
        subject: 'Mathematics',
        parentSubject: 'Mathematics',
        // No childSubject provided
        grade: 'Primary 2',
      };

      const optionValue = { value: 'Addition', id: 'opt_123' };

      // Mock the constructor to capture the data passed to it
      let capturedData: any;
      mockQuestionPoolModel.constructor = jest.fn().mockImplementation((data) => {
        capturedData = data;
        return {
          ...data,
          save: jest.fn().mockResolvedValue({ ...data, _id: 'new_id' }),
        };
      });

      await service.addQuestion(questionData, optionValue);

      expect(capturedData.childSubject).toBe('Addition');
      expect(capturedData.optionValue).toEqual(optionValue);
    });

    it('should set optionValue.value when childSubject exists but optionValue.value is missing', async () => {
      const questionData: ExerciseQuestionItem = {
        type: 'multiple_choice',
        content: 'Test question',
        options: ['A', 'B', 'C', 'D'],
        answer: ['B'],
        subject: 'Mathematics',
        parentSubject: 'Mathematics',
        childSubject: 'Multiplication',
        grade: 'Primary 3',
      };

      const optionValue = { id: 'opt_456' }; // Missing value field

      let capturedData: any;
      mockQuestionPoolModel.constructor = jest.fn().mockImplementation((data) => {
        capturedData = data;
        return {
          ...data,
          save: jest.fn().mockResolvedValue({ ...data, _id: 'new_id' }),
        };
      });

      await service.addQuestion(questionData, optionValue);

      expect(capturedData.childSubject).toBe('Multiplication');
      expect(capturedData.optionValue.value).toBe('Multiplication');
      expect(capturedData.optionValue.id).toBe('opt_456');
    });

    it('should maintain both fields when both are provided and consistent', async () => {
      const questionData: ExerciseQuestionItem = {
        type: 'multiple_choice',
        content: 'Test question',
        options: ['A', 'B', 'C', 'D'],
        answer: ['B'],
        subject: 'Mathematics',
        parentSubject: 'Mathematics',
        childSubject: 'Algebra',
        grade: 'Secondary 1',
      };

      const optionValue = { value: 'Algebra', id: 'opt_789' };

      let capturedData: any;
      mockQuestionPoolModel.constructor = jest.fn().mockImplementation((data) => {
        capturedData = data;
        return {
          ...data,
          save: jest.fn().mockResolvedValue({ ...data, _id: 'new_id' }),
        };
      });

      await service.addQuestion(questionData, optionValue);

      expect(capturedData.childSubject).toBe('Algebra');
      expect(capturedData.optionValue.value).toBe('Algebra');
      expect(capturedData.optionValue.id).toBe('opt_789');
    });

    it('should handle case where neither field is provided', async () => {
      const questionData: ExerciseQuestionItem = {
        type: 'multiple_choice',
        content: 'Test question',
        options: ['A', 'B', 'C', 'D'],
        answer: ['B'],
        subject: 'Mathematics',
        parentSubject: 'Mathematics',
        grade: 'Primary 1',
      };

      let capturedData: any;
      mockQuestionPoolModel.constructor = jest.fn().mockImplementation((data) => {
        capturedData = data;
        return {
          ...data,
          save: jest.fn().mockResolvedValue({ ...data, _id: 'new_id' }),
        };
      });

      await service.addQuestion(questionData);

      expect(capturedData.childSubject).toBeUndefined();
      expect(capturedData.optionValue).toBeUndefined();
    });
  });

  describe('Enhanced Logging', () => {
    it('should log query parameters and results for debugging', async () => {
      const mockLogger = {
        debug: jest.fn(),
        log: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
      };

      // Replace the logger in the service
      (service as any).logger = mockLogger;

      const filters = {
        subject: 'Mathematics',
        childSubject: 'Addition',
      };

      await service.getQuestions(filters, 5, 0);

      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Building getQuestions query with childSubject OR optionValue.value: Addition')
      );
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('getQuestions completed:')
      );
    });

    it('should warn when no questions are found', async () => {
      const mockLogger = {
        debug: jest.fn(),
        log: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
      };

      (service as any).logger = mockLogger;

      // Mock empty result
      mockQuestionPoolModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          limit: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              exec: jest.fn().mockResolvedValue([]),
            }),
          }),
        }),
      });
      mockQuestionPoolModel.countDocuments.mockResolvedValue(0);

      const filters = {
        subject: 'NonExistentSubject',
        childSubject: 'NonExistentTopic',
      };

      await service.getQuestions(filters, 5, 0);

      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('No questions found for filters:')
      );
    });
  });
});
