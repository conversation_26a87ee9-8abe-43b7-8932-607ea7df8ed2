import { Test, TestingModule } from '@nestjs/testing';
import { QuestionPoolCacheService } from './question-pool-cache.service';
import { WorksheetDocumentCacheService } from '../../worksheet/services/worksheet-document-cache.service';
import { QuestionSelectionParams, QuestionSelectionResult } from '../interfaces/distribution.interface';

describe('QuestionPoolCacheService', () => {
  let service: QuestionPoolCacheService;
  let mockWorksheetDocumentCacheService: jest.Mocked<WorksheetDocumentCacheService>;

  beforeEach(async () => {
    const mockWorksheetCache = {
      getFromCache: jest.fn(),
      saveToCache: jest.fn(),
      invalidateCache: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuestionPoolCacheService,
        {
          provide: WorksheetDocumentCacheService,
          useValue: mockWorksheetCache,
        },
      ],
    }).compile();

    service = module.get<QuestionPoolCacheService>(QuestionPoolCacheService);
    mockWorksheetDocumentCacheService = module.get(WorksheetDocumentCacheService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateCacheKey', () => {
    it('should generate consistent cache keys for identical parameters', () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        grade: 'Primary 5',
        type: 'multiple_choice',
        count: 10,
      };

      const key1 = service.generateCacheKey(params);
      const key2 = service.generateCacheKey(params);

      expect(key1).toBe(key2);
      expect(key1).toMatch(/^question_pool_[a-f0-9]{32}$/);
    });

    it('should generate different cache keys for different parameters', () => {
      const params1: QuestionSelectionParams = {
        subject: 'Mathematics',
        grade: 'Primary 5',
        count: 10,
      };

      const params2: QuestionSelectionParams = {
        subject: 'Science',
        grade: 'Primary 5',
        count: 10,
      };

      const key1 = service.generateCacheKey(params1);
      const key2 = service.generateCacheKey(params2);

      expect(key1).not.toBe(key2);
    });

    it('should handle array types consistently', () => {
      const params1: QuestionSelectionParams = {
        type: ['multiple_choice', 'fill_blank'],
        count: 10,
      };

      const params2: QuestionSelectionParams = {
        type: ['fill_blank', 'multiple_choice'], // Different order
        count: 10,
      };

      const key1 = service.generateCacheKey(params1);
      const key2 = service.generateCacheKey(params2);

      expect(key1).toBe(key2); // Should be same due to sorting
    });
  });

  describe('getFromCache', () => {
    it('should return cached result when available', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 5,
      };

      const mockCachedResult = {
        documentResult: {
          questions: [
            { _id: '1', type: 'multiple_choice', content: 'Test question' },
          ],
          metadata: {
            totalRequested: 5,
            totalReturned: 1,
            selectionTime: 100,
          },
        },
      };

      mockWorksheetDocumentCacheService.getFromCache.mockResolvedValue(mockCachedResult as any);

      const result = await service.getFromCache(params);

      expect(result).toEqual(mockCachedResult.documentResult);
      expect(mockWorksheetDocumentCacheService.getFromCache).toHaveBeenCalledWith(
        'question_pool_query',
        'cache',
        expect.objectContaining({ cacheKey: expect.any(String) })
      );
    });

    it('should return null when cache miss', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 5,
      };

      mockWorksheetDocumentCacheService.getFromCache.mockResolvedValue(null);

      const result = await service.getFromCache(params);

      expect(result).toBeNull();
    });

    it('should handle invalid cached result structure', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 5,
      };

      const mockInvalidCachedResult = {
        documentResult: {
          // Missing questions array and metadata
          invalidData: 'test',
        },
      };

      mockWorksheetDocumentCacheService.getFromCache.mockResolvedValue(mockInvalidCachedResult as any);

      const result = await service.getFromCache(params);

      expect(result).toBeNull();
    });
  });

  describe('saveToCache', () => {
    it('should save valid results to cache', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 5,
      };

      const result: QuestionSelectionResult = {
        questions: [
          {
            _id: '1',
            type: 'multiple_choice',
            content: 'Test question',
            subject: 'Mathematics',
          } as any,
        ],
        metadata: {
          totalRequested: 5,
          totalReturned: 1,
          selectionTime: 100,
          distributionAchieved: {},
          fallbacksTriggered: [],
          validationStats: {
            totalValidated: 1,
            passed: 1,
            failed: 0,
            successRate: 1,
          },
        },
      };

      await service.saveToCache(params, result);

      expect(mockWorksheetDocumentCacheService.saveToCache).toHaveBeenCalledWith(
        'question_pool_query',
        'cache',
        expect.objectContaining({
          questions: expect.any(Array),
          metadata: expect.objectContaining({
            cachedAt: expect.any(Date),
            cacheKey: expect.any(String),
          }),
        }),
        expect.any(String),
        false,
        expect.objectContaining({
          cacheKey: expect.any(String),
          ttlHours: 2,
        })
      );
    });

    it('should skip caching for empty results', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 5,
      };

      const result: QuestionSelectionResult = {
        questions: [],
        metadata: {
          totalRequested: 5,
          totalReturned: 0,
          selectionTime: 100,
          distributionAchieved: {},
          fallbacksTriggered: [],
          validationStats: {
            totalValidated: 0,
            passed: 0,
            failed: 0,
            successRate: 1,
          },
        },
      };

      await service.saveToCache(params, result);

      expect(mockWorksheetDocumentCacheService.saveToCache).not.toHaveBeenCalled();
    });
  });

  describe('invalidateCache', () => {
    it('should invalidate cache for specific parameters', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 5,
      };

      await service.invalidateCache(params);

      expect(mockWorksheetDocumentCacheService.invalidateCache).toHaveBeenCalledWith(
        expect.any(String)
      );
    });
  });
});
