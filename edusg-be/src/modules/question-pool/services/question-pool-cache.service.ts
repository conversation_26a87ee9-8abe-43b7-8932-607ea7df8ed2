import { Injectable, Logger } from '@nestjs/common';
import { WorksheetDocumentCacheService } from '../../worksheet/services/worksheet-document-cache.service';
import { QuestionSelectionParams, QuestionSelectionResult } from '../interfaces/distribution.interface';
import { QuestionPool } from '../../mongodb/schemas/question-pool.schema';
import * as crypto from 'crypto';

@Injectable()
export class QuestionPoolCacheService {
  private readonly logger = new Logger(QuestionPoolCacheService.name);
  private readonly CACHE_TTL_HOURS = 2; // TTL for question pool cache in hours
  private readonly CACHE_PREFIX = 'question_pool_';

  constructor(
    private worksheetDocumentCacheService: WorksheetDocumentCacheService,
  ) {}

  /**
   * Generates a unique cache key for question pool queries
   * @param params Question selection parameters
   * @returns A unique cache key string
   */
  generateCacheKey(params: QuestionSelectionParams): string {
    // Create a normalized object with all relevant parameters
    const cacheParams = {
      subject: params.subject || null,
      parentSubject: params.parentSubject || null,
      childSubject: params.childSubject || null,
      type: Array.isArray(params.type) ? params.type.sort() : params.type || null,
      status: params.status || 'active',
      grade: params.grade || null,
      language: params.language || null,
      count: params.count || 10,
      skipDistribution: params.skipDistribution || false,
      skipDiversity: params.skipDiversity || false,
      skipValidation: params.skipValidation || false,
      distributionConfig: params.distributionConfig ? {
        difficultyDistribution: params.distributionConfig.difficultyDistribution,
        questionTypeBalancing: params.distributionConfig.questionTypeBalancing,
        diversity: params.distributionConfig.diversity,
        qualityValidation: params.distributionConfig.qualityValidation,
        fallback: params.distributionConfig.fallback,
      } : null,
    };

    // Create a deterministic string representation
    const paramString = JSON.stringify(cacheParams, Object.keys(cacheParams).sort());
    
    // Generate MD5 hash for the cache key
    const hash = crypto.createHash('md5').update(paramString).digest('hex');
    
    return `${this.CACHE_PREFIX}${hash}`;
  }

  /**
   * Retrieves cached question selection results
   * @param params Question selection parameters
   * @returns Cached results or null if not found
   */
  async getFromCache(params: QuestionSelectionParams): Promise<QuestionSelectionResult | null> {
    try {
      const cacheKey = this.generateCacheKey(params);
      
      // Use WorksheetDocumentCacheService with a synthetic topic/grade for compatibility
      const cachedResult = await this.worksheetDocumentCacheService.getFromCache(
        'question_pool_query',
        'cache',
        { cacheKey }
      );

      if (cachedResult && cachedResult.documentResult) {
        this.logger.debug(`Cache HIT for question pool query: ${cacheKey}`);
        
        // Validate that cached result structure is correct
        const result = cachedResult.documentResult as QuestionSelectionResult;
        if (result.questions && Array.isArray(result.questions) && result.metadata) {
          return result;
        } else {
          this.logger.warn(`Invalid cached result structure for key: ${cacheKey}`);
          // Invalidate the corrupted cache entry
          await this.invalidateCache(params);
          return null;
        }
      }

      this.logger.debug(`Cache MISS for question pool query: ${cacheKey}`);
      return null;
    } catch (error) {
      this.logger.error(`Error retrieving from question pool cache: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Saves question selection results to cache
   * @param params Question selection parameters
   * @param result Question selection results to cache
   * @returns Promise that resolves when caching is complete
   */
  async saveToCache(
    params: QuestionSelectionParams,
    result: QuestionSelectionResult
  ): Promise<void> {
    try {
      // Only cache successful results with questions
      if (!result.questions || result.questions.length === 0) {
        this.logger.debug('Skipping cache save for empty result');
        return;
      }

      const cacheKey = this.generateCacheKey(params);
      
      // Create a serializable version of the result
      const cacheableResult: QuestionSelectionResult = {
        questions: result.questions.map(q => ({
          _id: q._id,
          type: q.type,
          content: q.content,
          options: q.options,
          answer: q.answer,
          explain: q.explain,
          imagePrompt: q.imagePrompt,
          image: q.image,
          subject: q.subject,
          parentSubject: q.parentSubject,
          childSubject: q.childSubject,
          grade: q.grade,
          language: q.language,
          status: q.status,
          difficulty: q.difficulty,
          lastSelectedTimestamp: q.lastSelectedTimestamp,
          selectionFrequency: q.selectionFrequency,
          createdAt: q.createdAt,
          updatedAt: q.updatedAt,
        })),
        metadata: {
          ...result.metadata,
          cachedAt: new Date(),
          cacheKey,
        },
      };

      // Use WorksheetDocumentCacheService with synthetic topic/grade
      await this.worksheetDocumentCacheService.saveToCache(
        'question_pool_query',
        'cache',
        cacheableResult,
        cacheKey,
        false, // fromCache = false since this is original data
        { cacheKey, ttlHours: this.CACHE_TTL_HOURS }
      );

      this.logger.debug(`Cached question pool result: ${cacheKey} (${result.questions.length} questions)`);
    } catch (error) {
      this.logger.error(`Error saving to question pool cache: ${error.message}`, error.stack);
    }
  }

  /**
   * Invalidates cached results for specific parameters
   * @param params Question selection parameters
   */
  async invalidateCache(params: QuestionSelectionParams): Promise<void> {
    try {
      const cacheKey = this.generateCacheKey(params);
      
      // Use WorksheetDocumentCacheService invalidation
      await this.worksheetDocumentCacheService.invalidateCache(cacheKey);
      
      this.logger.debug(`Invalidated question pool cache: ${cacheKey}`);
    } catch (error) {
      this.logger.error(`Error invalidating question pool cache: ${error.message}`, error.stack);
    }
  }

  /**
   * Invalidates all question pool cache entries
   * This should be called when questions are added, updated, or deleted
   */
  async invalidateAllCache(): Promise<void> {
    try {
      // This is a simplified approach - in production, you might want to track
      // all cache keys or use a more sophisticated invalidation strategy
      this.logger.warn('Full question pool cache invalidation requested - consider implementing selective invalidation');
      
      // For now, we'll rely on TTL-based expiration
      // In a production system, you might maintain a list of active cache keys
    } catch (error) {
      this.logger.error(`Error invalidating all question pool cache: ${error.message}`, error.stack);
    }
  }

  /**
   * Gets cache statistics for monitoring
   */
  async getCacheStats(): Promise<{
    totalCacheEntries: number;
    cacheHitRate: number;
    averageResponseTime: number;
  }> {
    // This is a placeholder for cache statistics
    // In a production system, you would track these metrics
    return {
      totalCacheEntries: 0,
      cacheHitRate: 0,
      averageResponseTime: 0,
    };
  }
}
