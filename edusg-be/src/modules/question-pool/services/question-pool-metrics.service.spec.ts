import { Test, TestingModule } from '@nestjs/testing';
import { QuestionPoolMetricsService } from './question-pool-metrics.service';

describe('QuestionPoolMetricsService', () => {
  let service: QuestionPoolMetricsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [QuestionPoolMetricsService],
    }).compile();

    service = module.get<QuestionPoolMetricsService>(QuestionPoolMetricsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('recordQueryDuration', () => {
    it('should record query duration metrics', () => {
      const duration = 0.5; // 500ms
      const method = 'getRandomQuestions';
      const cacheStatus = 'hit';

      expect(() => {
        service.recordQueryDuration(duration, method, cacheStatus, true, false);
      }).not.toThrow();
    });
  });

  describe('recordDbQueryDuration', () => {
    it('should record database query duration metrics', () => {
      const duration = 0.1; // 100ms
      const queryType = 'aggregation';

      expect(() => {
        service.recordDbQueryDuration(duration, queryType);
      }).not.toThrow();
    });
  });

  describe('cache metrics', () => {
    it('should record cache hits and misses', () => {
      expect(() => {
        service.recordCacheHit();
        service.recordCacheMiss();
      }).not.toThrow();
    });

    it('should calculate cache hit rate', async () => {
      // Record some cache hits and misses
      service.recordCacheHit();
      service.recordCacheHit();
      service.recordCacheMiss();

      const hitRate = await service.getCacheHitRate();

      // Should be 2/3 = 0.667 (approximately)
      expect(hitRate).toBeCloseTo(0.667, 2);
    });

    it('should return 0 hit rate when no cache operations', async () => {
      const hitRate = await service.getCacheHitRate();
      expect(hitRate).toBe(0);
    });
  });

  describe('recordQuery', () => {
    it('should record successful queries', () => {
      expect(() => {
        service.recordQuery('getRandomQuestions', 'success');
      }).not.toThrow();
    });

    it('should record failed queries', () => {
      expect(() => {
        service.recordQuery('getRandomQuestions', 'error');
      }).not.toThrow();
    });
  });

  describe('recordError', () => {
    it('should record errors with operation and type', () => {
      expect(() => {
        service.recordError('getRandomQuestions', 'DatabaseError');
      }).not.toThrow();
    });
  });

  describe('updateQuestionCounts', () => {
    it('should update question count gauges', () => {
      expect(() => {
        service.updateQuestionCounts(10, 8);
      }).not.toThrow();
    });
  });

  describe('updateDbConnections', () => {
    it('should update database connection gauge', () => {
      expect(() => {
        service.updateDbConnections(5);
      }).not.toThrow();
    });
  });

  describe('getMetrics', () => {
    it('should return metrics in Prometheus format', async () => {
      // Record some metrics first
      service.recordQuery('getRandomQuestions', 'success');
      service.recordCacheHit();
      service.recordQueryDuration(0.5, 'getRandomQuestions', 'hit', true, false);

      const metrics = await service.getMetrics();

      expect(typeof metrics).toBe('string');
      // The metrics should contain at least some content
      expect(metrics.length).toBeGreaterThan(0);
    });
  });

  describe('getPerformanceSummary', () => {
    it('should return performance summary', async () => {
      // Record some test data
      service.recordQuery('getRandomQuestions', 'success');
      service.recordQuery('getRandomQuestions', 'success');
      service.recordQuery('getRandomQuestions', 'error');
      service.recordCacheHit();
      service.recordCacheMiss();
      service.recordQueryDuration(0.5, 'getRandomQuestions', 'hit', true, false);
      service.recordQueryDuration(0.3, 'getRandomQuestions', 'miss', true, false);

      const summary = await service.getPerformanceSummary();

      expect(summary).toHaveProperty('totalQueries');
      expect(summary).toHaveProperty('cacheHitRate');
      expect(summary).toHaveProperty('averageQueryTime');
      expect(summary).toHaveProperty('errorRate');

      // Note: These values might be cumulative from previous tests
      expect(summary.totalQueries).toBeGreaterThanOrEqual(3);
      expect(summary.cacheHitRate).toBeGreaterThan(0);
      expect(summary.errorRate).toBeGreaterThanOrEqual(0); // Error rate can be 0 if no errors
    });

    it('should handle empty metrics gracefully', async () => {
      const summary = await service.getPerformanceSummary();
      
      expect(summary.totalQueries).toBe(0);
      expect(summary.cacheHitRate).toBe(0);
      expect(summary.averageQueryTime).toBe(0);
      expect(summary.errorRate).toBe(0);
    });
  });

  describe('getRegistry', () => {
    it('should return the Prometheus registry', () => {
      const registry = service.getRegistry();
      expect(registry).toBeDefined();
      expect(typeof registry.metrics).toBe('function');
    });
  });
});
