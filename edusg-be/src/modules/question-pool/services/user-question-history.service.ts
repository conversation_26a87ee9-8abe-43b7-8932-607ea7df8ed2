import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectModel } from '@nestjs/mongoose';
import { Repository } from 'typeorm';
import { Model } from 'mongoose';
import { Worksheet } from '../../worksheet/entities/worksheet.entity';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { IExerciseQuestion } from '../../../shared/interfaces/exercise-question.interface';

/**
 * Service to track and retrieve user's question selection history
 * This helps exclude questions that users have already chosen
 */
@Injectable()
export class UserQuestionHistoryService {
  private readonly logger = new Logger(UserQuestionHistoryService.name);

  constructor(
    @InjectRepository(Worksheet)
    private readonly worksheetRepository: Repository<Worksheet>,
    @InjectModel(WorksheetQuestionDocument.name)
    private readonly worksheetQuestionModel: Model<WorksheetQuestionDocument>,
  ) {}

  /**
   * Get all question pool IDs that a user has already chosen
   * @param userId User ID
   * @param schoolId Optional school ID for filtering
   * @returns Array of question pool IDs
   */
  async getUserChosenQuestionIds(userId: string, schoolId?: string): Promise<string[]> {
    try {
      this.logger.debug(`Getting chosen question IDs for user ${userId}`);

      // Build query conditions
      const whereConditions: any = {
        createdBy: userId
      };

      // Add school filter if provided
      if (schoolId) {
        whereConditions.schoolId = schoolId;
      }

      // Get all worksheets created by the user
      const userWorksheets = await this.worksheetRepository.find({
        where: whereConditions,
        select: ['id', 'questions']
      });

      // Extract question pool IDs from all worksheets
      const questionPoolIds = new Set<string>();

      for (const worksheet of userWorksheets) {
        if (worksheet.questions && Array.isArray(worksheet.questions)) {
          for (const question of worksheet.questions) {
            // Check if question came from pool
            if (question.questionPoolId) {
              questionPoolIds.add(question.questionPoolId);
            }
          }
        }
      }

      const result = Array.from(questionPoolIds);
      this.logger.debug(`Found ${result.length} chosen question pool IDs for user ${userId}`);
      
      return result;

    } catch (error) {
      this.logger.error(`Error getting chosen question IDs for user ${userId}: ${error.message}`, error.stack);
      return []; // Return empty array on error to not break the search
    }
  }

  /**
   * Get question pool IDs from specific worksheets
   * @param worksheetIds Array of worksheet IDs
   * @param userId User ID for access control
   * @returns Array of question pool IDs
   */
  async getQuestionIdsFromWorksheets(worksheetIds: string[], userId: string): Promise<string[]> {
    try {
      this.logger.debug(`Getting question IDs from worksheets ${worksheetIds.join(', ')} for user ${userId}`);

      if (!worksheetIds || worksheetIds.length === 0) {
        return [];
      }

      // Get worksheets that user has access to
      const worksheets = await this.worksheetRepository
        .createQueryBuilder('worksheet')
        .where('worksheet.id IN (:...worksheetIds)', { worksheetIds })
        .andWhere('(worksheet.createdBy = :userId OR worksheet.lastModifiedBy = :userId)', { userId })
        .select(['worksheet.id', 'worksheet.questions'])
        .getMany();

      // Extract question pool IDs
      const questionPoolIds = new Set<string>();

      for (const worksheet of worksheets) {
        if (worksheet.questions && Array.isArray(worksheet.questions)) {
          for (const question of worksheet.questions) {
            if (question.questionPoolId) {
              questionPoolIds.add(question.questionPoolId);
            }
          }
        }
      }

      const result = Array.from(questionPoolIds);
      this.logger.debug(`Found ${result.length} question pool IDs from specified worksheets`);
      
      return result;

    } catch (error) {
      this.logger.error(`Error getting question IDs from worksheets: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Get recently used question pool IDs by user within a time period
   * @param userId User ID
   * @param hoursBack Number of hours to look back
   * @param schoolId Optional school ID for filtering
   * @returns Array of question pool IDs
   */
  async getRecentlyUsedQuestionIds(
    userId: string, 
    hoursBack: number, 
    schoolId?: string
  ): Promise<string[]> {
    try {
      this.logger.debug(`Getting recently used question IDs for user ${userId} within ${hoursBack} hours`);

      const cutoffDate = new Date();
      cutoffDate.setHours(cutoffDate.getHours() - hoursBack);

      // Build query conditions
      const whereConditions: any = {
        createdBy: userId,
        createdAt: { $gte: cutoffDate } as any
      };

      if (schoolId) {
        whereConditions.schoolId = schoolId;
      }

      // Get recent worksheets
      const recentWorksheets = await this.worksheetRepository.find({
        where: whereConditions,
        select: ['id', 'questions', 'createdAt']
      });

      // Extract question pool IDs
      const questionPoolIds = new Set<string>();

      for (const worksheet of recentWorksheets) {
        if (worksheet.questions && Array.isArray(worksheet.questions)) {
          for (const question of worksheet.questions) {
            if (question.questionPoolId) {
              questionPoolIds.add(question.questionPoolId);
            }
          }
        }
      }

      const result = Array.from(questionPoolIds);
      this.logger.debug(`Found ${result.length} recently used question pool IDs for user ${userId}`);
      
      return result;

    } catch (error) {
      this.logger.error(`Error getting recently used question IDs: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Get comprehensive list of question IDs to exclude for a user
   * @param userId User ID
   * @param options Exclusion options
   * @returns Array of question pool IDs to exclude
   */
  async getQuestionIdsToExclude(
    userId: string,
    options: {
      excludeUserChosenQuestions?: boolean;
      excludeRecentlyUsedHours?: number;
      excludeFromWorksheets?: string[];
      excludeQuestionIds?: string[];
      schoolId?: string;
    } = {}
  ): Promise<string[]> {
    try {
      const excludeIds = new Set<string>();

      // Add explicitly excluded question IDs
      if (options.excludeQuestionIds && options.excludeQuestionIds.length > 0) {
        options.excludeQuestionIds.forEach(id => excludeIds.add(id));
      }

      // Add questions from specific worksheets
      if (options.excludeFromWorksheets && options.excludeFromWorksheets.length > 0) {
        const worksheetQuestionIds = await this.getQuestionIdsFromWorksheets(
          options.excludeFromWorksheets,
          userId
        );
        worksheetQuestionIds.forEach(id => excludeIds.add(id));
      }

      // Add recently used questions
      if (options.excludeRecentlyUsedHours && options.excludeRecentlyUsedHours > 0) {
        const recentQuestionIds = await this.getRecentlyUsedQuestionIds(
          userId,
          options.excludeRecentlyUsedHours,
          options.schoolId
        );
        recentQuestionIds.forEach(id => excludeIds.add(id));
      }

      // Add all user chosen questions (default behavior)
      if (options.excludeUserChosenQuestions !== false) {
        const userChosenIds = await this.getUserChosenQuestionIds(userId, options.schoolId);
        userChosenIds.forEach(id => excludeIds.add(id));
      }

      const result = Array.from(excludeIds);
      this.logger.debug(`Total question IDs to exclude for user ${userId}: ${result.length}`);
      
      return result;

    } catch (error) {
      this.logger.error(`Error getting question IDs to exclude: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Check if a specific question has been chosen by the user
   * @param userId User ID
   * @param questionPoolId Question pool ID to check
   * @param schoolId Optional school ID for filtering
   * @returns True if question has been chosen by user
   */
  async hasUserChosenQuestion(
    userId: string, 
    questionPoolId: string, 
    schoolId?: string
  ): Promise<boolean> {
    try {
      const chosenIds = await this.getUserChosenQuestionIds(userId, schoolId);
      return chosenIds.includes(questionPoolId);
    } catch (error) {
      this.logger.error(`Error checking if user has chosen question: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Get statistics about user's question usage
   * @param userId User ID
   * @param schoolId Optional school ID for filtering
   * @returns Usage statistics
   */
  async getUserQuestionStats(userId: string, schoolId?: string): Promise<{
    totalWorksheetsCreated: number;
    totalQuestionsUsed: number;
    uniquePoolQuestionsUsed: number;
    customQuestionsCreated: number;
    mostUsedSubjects: Array<{ subject: string; count: number }>;
  }> {
    try {
      const whereConditions: any = { createdBy: userId };
      if (schoolId) {
        whereConditions.schoolId = schoolId;
      }

      const userWorksheets = await this.worksheetRepository.find({
        where: whereConditions,
        select: ['id', 'questions']
      });

      const poolQuestionIds = new Set<string>();
      let totalQuestions = 0;
      let customQuestions = 0;
      const subjectCounts: Record<string, number> = {};

      for (const worksheet of userWorksheets) {
        if (worksheet.questions && Array.isArray(worksheet.questions)) {
          totalQuestions += worksheet.questions.length;
          
          for (const question of worksheet.questions) {
            if (question.questionPoolId) {
              poolQuestionIds.add(question.questionPoolId);
            } else {
              customQuestions++;
            }

            // Count subjects
            const subject = question.subject || 'Unknown';
            subjectCounts[subject] = (subjectCounts[subject] || 0) + 1;
          }
        }
      }

      // Sort subjects by usage
      const mostUsedSubjects = Object.entries(subjectCounts)
        .map(([subject, count]) => ({ subject, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      return {
        totalWorksheetsCreated: userWorksheets.length,
        totalQuestionsUsed: totalQuestions,
        uniquePoolQuestionsUsed: poolQuestionIds.size,
        customQuestionsCreated: customQuestions,
        mostUsedSubjects
      };

    } catch (error) {
      this.logger.error(`Error getting user question stats: ${error.message}`, error.stack);
      return {
        totalWorksheetsCreated: 0,
        totalQuestionsUsed: 0,
        uniquePoolQuestionsUsed: 0,
        customQuestionsCreated: 0,
        mostUsedSubjects: []
      };
    }
  }
}
