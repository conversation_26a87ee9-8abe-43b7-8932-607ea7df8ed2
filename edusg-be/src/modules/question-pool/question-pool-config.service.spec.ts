import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { QuestionPoolConfigService } from './question-pool-config.service';
import questionPoolConfig from '../../core/configs/question-pool/question-pool.config';
import { SelectionStrategy, WorksheetGenerationOptionsDto } from '../worksheet/dto/worksheet-generation-options.dto';

describe('QuestionPoolConfigService', () => {
  let service: QuestionPoolConfigService;

  beforeEach(async () => {
    // Clear environment variables
    delete process.env.QUESTION_POOL_ENABLED;
    delete process.env.DEFAULT_SELECTION_STRATEGY;
    delete process.env.MIN_POOL_QUESTIONS_THRESHOLD;
    delete process.env.ALLOW_POOL_ONLY_STRATEGY;
    delete process.env.ALLOW_AI_ONLY_STRATEGY;
    delete process.env.ALLOW_HYBRID_STRATEGY;
    delete process.env.ALLOW_MIXED_STRATEGY;

    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [questionPoolConfig],
          isGlobal: true,
        }),
      ],
      providers: [QuestionPoolConfigService],
    }).compile();

    service = module.get<QuestionPoolConfigService>(QuestionPoolConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Basic Configuration Access', () => {
    it('should return the configuration', () => {
      const config = service.getConfig();
      expect(config).toBeDefined();
      expect(config.enabled).toBe(true);
      expect(config.defaultSelectionStrategy).toBe('hybrid');
    });

    it('should check if question pool is enabled', () => {
      expect(service.isQuestionPoolEnabled()).toBe(true);
    });

    it('should get default selection strategy', () => {
      expect(service.getDefaultSelectionStrategy()).toBe(SelectionStrategy.HYBRID);
    });

    it('should get minimum pool questions threshold', () => {
      expect(service.getMinPoolQuestionsThreshold()).toBe(10);
    });
  });

  describe('Strategy Validation', () => {
    it('should validate allowed strategies', () => {
      expect(service.isStrategyAllowed(SelectionStrategy.POOL_ONLY)).toBe(true);
      expect(service.isStrategyAllowed(SelectionStrategy.AI_ONLY)).toBe(true);
      expect(service.isStrategyAllowed(SelectionStrategy.HYBRID)).toBe(true);
      expect(service.isStrategyAllowed(SelectionStrategy.MIXED)).toBe(true);
    });

    it('should return all allowed strategies', () => {
      const allowed = service.getAllowedStrategies();
      expect(allowed).toHaveLength(4);
      expect(allowed).toContain(SelectionStrategy.POOL_ONLY);
      expect(allowed).toContain(SelectionStrategy.AI_ONLY);
      expect(allowed).toContain(SelectionStrategy.HYBRID);
      expect(allowed).toContain(SelectionStrategy.MIXED);
    });
  });

  describe('Options Resolution', () => {
    it('should resolve options with defaults when no options provided', () => {
      const resolved = service.resolveWorksheetGenerationOptions();

      expect(resolved.poolEnabled).toBe(true);
      expect(resolved.resolvedStrategy).toBe(SelectionStrategy.HYBRID);
      expect(resolved.strategyAllowed).toBe(true);
      expect(resolved.minPoolQuestionsRequired).toBe(10);
    });

    it('should resolve options with user-provided strategy', () => {
      const options: WorksheetGenerationOptionsDto = {
        selectionStrategy: SelectionStrategy.POOL_ONLY,
      };

      const resolved = service.resolveWorksheetGenerationOptions(options);

      expect(resolved.resolvedStrategy).toBe(SelectionStrategy.POOL_ONLY);
      expect(resolved.strategyAllowed).toBe(true);
    });

    it('should handle user override for question pool', () => {
      const options: WorksheetGenerationOptionsDto = {
        useQuestionPoolOverride: false,
      };

      const resolved = service.resolveWorksheetGenerationOptions(options);

      expect(resolved.poolEnabled).toBe(false);
    });

    it('should apply user-provided minimum questions threshold', () => {
      const options: WorksheetGenerationOptionsDto = {
        minPoolQuestionsRequired: 25,
      };

      const resolved = service.resolveWorksheetGenerationOptions(options);

      expect(resolved.minPoolQuestionsRequired).toBe(25);
    });
  });

  describe('Options Validation', () => {
    it('should validate valid resolved options', () => {
      const resolved = service.resolveWorksheetGenerationOptions({
        selectionStrategy: SelectionStrategy.HYBRID,
        minPoolQuestionsRequired: 10,
      });

      const validation = service.validateResolvedOptions(resolved);

      expect(validation.isValid).toBe(true);
      expect(validation.issues).toHaveLength(0);
    });

    it('should detect invalid configuration when pool is required but disabled', () => {
      const resolved = service.resolveWorksheetGenerationOptions({
        selectionStrategy: SelectionStrategy.POOL_ONLY,
        useQuestionPoolOverride: false,
      });

      const validation = service.validateResolvedOptions(resolved);

      expect(validation.isValid).toBe(false);
      expect(validation.issues).toContain(
        'Question pool is required for the selected strategy but is disabled'
      );
    });

    it('should detect negative minimum questions threshold', () => {
      const resolved = service.resolveWorksheetGenerationOptions({
        minPoolQuestionsRequired: -5,
      });

      const validation = service.validateResolvedOptions(resolved);

      expect(validation.isValid).toBe(false);
      expect(validation.issues).toContain(
        'Minimum pool questions required cannot be negative'
      );
    });
  });

  describe('Feature Flag Scenarios', () => {
    it('should handle disabled strategies', async () => {
      // Create a new module with disabled strategies
      process.env.ALLOW_POOL_ONLY_STRATEGY = 'false';
      process.env.ALLOW_HYBRID_STRATEGY = 'false';

      const module: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            load: [questionPoolConfig],
            isGlobal: true,
          }),
        ],
        providers: [QuestionPoolConfigService],
      }).compile();

      const testService = module.get<QuestionPoolConfigService>(QuestionPoolConfigService);

      expect(testService.isStrategyAllowed(SelectionStrategy.POOL_ONLY)).toBe(false);
      expect(testService.isStrategyAllowed(SelectionStrategy.HYBRID)).toBe(false);
      expect(testService.isStrategyAllowed(SelectionStrategy.AI_ONLY)).toBe(true);
      expect(testService.isStrategyAllowed(SelectionStrategy.MIXED)).toBe(true);

      const allowed = testService.getAllowedStrategies();
      expect(allowed).toHaveLength(2);
      expect(allowed).toContain(SelectionStrategy.AI_ONLY);
      expect(allowed).toContain(SelectionStrategy.MIXED);
    });

    it('should fall back to default when requested strategy is not allowed', async () => {
      process.env.ALLOW_POOL_ONLY_STRATEGY = 'false';
      process.env.DEFAULT_SELECTION_STRATEGY = 'ai-only';

      const module: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            load: [questionPoolConfig],
            isGlobal: true,
          }),
        ],
        providers: [QuestionPoolConfigService],
      }).compile();

      const testService = module.get<QuestionPoolConfigService>(QuestionPoolConfigService);

      const resolved = testService.resolveWorksheetGenerationOptions({
        selectionStrategy: SelectionStrategy.POOL_ONLY,
      });

      expect(resolved.resolvedStrategy).toBe(SelectionStrategy.AI_ONLY);
      expect(resolved.strategyAllowed).toBe(false);
    });
  });
});
