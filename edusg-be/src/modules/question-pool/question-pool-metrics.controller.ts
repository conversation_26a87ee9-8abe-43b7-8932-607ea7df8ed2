import { <PERSON>, Get, Header } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Public } from '../auth/decorators/public.decorator';
import { QuestionPoolMetricsService } from './services/question-pool-metrics.service';

@ApiTags('Question Pool Metrics')
@Controller('question-pool/metrics')
@Public()
export class QuestionPoolMetricsController {
  constructor(private readonly metricsService: QuestionPoolMetricsService) {}

  @Get()
  @Header('Content-Type', 'text/plain')
  @ApiOperation({ summary: 'Get Prometheus metrics for question pool operations' })
  @ApiResponse({ 
    status: 200, 
    description: 'Prometheus metrics in text format',
    content: {
      'text/plain': {
        schema: {
          type: 'string',
          example: '# HELP question_pool_query_duration_seconds Duration of question pool queries in seconds\n# TYPE question_pool_query_duration_seconds histogram\nquestion_pool_query_duration_seconds_bucket{le="0.001",method="getRandomQuestions",cache_status="miss"} 0\n...'
        }
      }
    }
  })
  async getMetrics(): Promise<string> {
    return this.metricsService.getMetrics();
  }

  @Get('summary')
  @ApiOperation({ summary: 'Get performance summary for question pool operations' })
  @ApiResponse({ 
    status: 200, 
    description: 'Performance summary with key metrics',
    schema: {
      type: 'object',
      properties: {
        totalQueries: { type: 'number', description: 'Total number of queries executed' },
        cacheHitRate: { type: 'number', description: 'Cache hit rate (0-1)' },
        averageQueryTime: { type: 'number', description: 'Average query time in seconds' },
        errorRate: { type: 'number', description: 'Error rate (0-1)' },
      }
    }
  })
  async getPerformanceSummary() {
    return this.metricsService.getPerformanceSummary();
  }

  @Get('cache-stats')
  @ApiOperation({ summary: 'Get cache statistics for question pool' })
  @ApiResponse({ 
    status: 200, 
    description: 'Cache statistics',
    schema: {
      type: 'object',
      properties: {
        hitRate: { type: 'number', description: 'Cache hit rate (0-1)' },
        totalHits: { type: 'number', description: 'Total cache hits' },
        totalMisses: { type: 'number', description: 'Total cache misses' },
      }
    }
  })
  async getCacheStats() {
    const hitRate = await this.metricsService.getCacheHitRate();
    
    // Get raw metrics to extract hit/miss counts
    const metrics = await this.metricsService.getRegistry().getMetricsAsJSON();
    const cacheHits = metrics.find(m => m.name === 'question_pool_cache_hits_total');
    const cacheMisses = metrics.find(m => m.name === 'question_pool_cache_misses_total');
    
    const totalHits = cacheHits?.values.reduce((sum, v) => sum + (v.value || 0), 0) || 0;
    const totalMisses = cacheMisses?.values.reduce((sum, v) => sum + (v.value || 0), 0) || 0;
    
    return {
      hitRate,
      totalHits,
      totalMisses,
    };
  }
}
