import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Logger } from '@nestjs/common';
import { QuestionPoolService } from '../question-pool.service';
import { QuestionPool } from '../../mongodb/schemas/question-pool.schema';
import { QuestionPoolCacheService } from '../services/question-pool-cache.service';
import { QuestionPoolMetricsService } from '../services/question-pool-metrics.service';
import { PoolMonitoringService } from '../../monitoring/services/pool-monitoring.service';
import { ContentValidationService } from '../../validation/content-validation.service';
import { QuestionPoolConfigService } from '../question-pool-config.service';
import { WorksheetGenerateConsumer } from '../../worksheet/queue.consumer';
import { WorksheetDocumentCacheService } from '../../worksheet/services/worksheet-document-cache.service';
import { WorksheetService } from '../../worksheet/worksheet.service';
import { DocumentsService } from '../../documents/documents.service';
import { PromptService } from '../../prompt/services/prompt.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { BatchImageService } from '../../gen-image/batch-image.service';
import { AiOrchestrationService } from '../../ai/ai-orchestration.service';
import { SelectionStrategy } from '../../worksheet/dto/worksheet-generation-options.dto';
import {
  QuestionSelectionParams,
  QuestionSelectionResult
} from '../interfaces/distribution.interface';

describe('QuestionPoolService Integration Tests', () => {
  let questionPoolService: QuestionPoolService;
  let worksheetGenerateConsumer: WorksheetGenerateConsumer;
  let cacheService: QuestionPoolCacheService;
  let configService: QuestionPoolConfigService;
  let mockQuestionPoolModel: any;
  let mockSocketGateway: any;

  // Mock data
  const mockQuestions = [
    {
      _id: '507f1f77bcf86cd799439011',
      content: 'What is 2 + 2?',
      type: 'multiple_choice',
      options: ['3', '4', '5', '6'],
      answer: '4',
      subject: 'Mathematics',
      parentSubject: 'Mathematics',
      childSubject: 'Addition',
      grade: 'Primary 2',
      language: 'English',
      difficulty: 'Easy',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      _id: '507f1f77bcf86cd799439012',
      content: 'What is 5 × 3?',
      type: 'multiple_choice',
      options: ['12', '15', '18', '20'],
      answer: '15',
      subject: 'Mathematics',
      parentSubject: 'Mathematics',
      childSubject: 'Multiplication',
      grade: 'Primary 3',
      language: 'English',
      difficulty: 'Medium',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  beforeEach(async () => {
    // Create mock aggregation pipeline
    const mockAggregate = jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue(mockQuestions),
    });

    // Create mock model
    mockQuestionPoolModel = {
      aggregate: mockAggregate,
      find: jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockQuestions),
      }),
      countDocuments: jest.fn().mockResolvedValue(100),
      distinct: jest.fn().mockResolvedValue(['Easy', 'Medium', 'Advanced']),
    };

    // Create mock services for WorksheetGenerateConsumer dependencies
    mockSocketGateway = {
      emitWorksheetProgress: jest.fn(),
      emitRetryAttempt: jest.fn(),
      emitSystemError: jest.fn(),
      emitFallbackActivated: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuestionPoolService,
        WorksheetGenerateConsumer,
        {
          provide: getModelToken(QuestionPool.name),
          useValue: mockQuestionPoolModel,
        },
        {
          provide: getModelToken('WorksheetPromptResult'),
          useValue: {
            create: jest.fn(),
            findOne: jest.fn(),
            updateOne: jest.fn(),
          },
        },
        {
          provide: QuestionPoolCacheService,
          useValue: {
            getFromCache: jest.fn(),
            saveToCache: jest.fn(),
            invalidateCache: jest.fn(),
            generateCacheKey: jest.fn(),
          },
        },
        {
          provide: QuestionPoolMetricsService,
          useValue: {
            recordQuery: jest.fn(),
            recordQueryDuration: jest.fn(),
            recordCacheHit: jest.fn(),
            recordCacheMiss: jest.fn(),
            updateQuestionCounts: jest.fn(),
            recordError: jest.fn(),
          },
        },
        {
          provide: PoolMonitoringService,
          useValue: {
            emitEvent: jest.fn(),
            getEvents: jest.fn(),
            calculatePoolUtilization: jest.fn(),
            calculateQuestionReuse: jest.fn(),
            calculateGenerationTimeComparison: jest.fn(),
            calculateValidationMetrics: jest.fn(),
            calculateCacheMetrics: jest.fn(),
            getDashboardMetrics: jest.fn(),
          },
        },
        {
          provide: ContentValidationService,
          useValue: {
            validateQuestion: jest.fn().mockResolvedValue({ isValid: true, score: 0.9 }),
            validateQuestions: jest.fn().mockResolvedValue([]),
            getValidationRules: jest.fn().mockReturnValue([]),
          },
        },
        {
          provide: QuestionPoolConfigService,
          useValue: {
            getConfig: jest.fn().mockReturnValue({
              enabled: true,
              defaultSelectionStrategy: 'hybrid',
              minPoolQuestionsThreshold: 10,
              distribution: {
                defaultDifficultyDistribution: { Easy: 0.3, Medium: 0.4, Advanced: 0.3 },
                defaultQuestionTypeBalancing: { enabled: true, preferDiversity: true },
                defaultDiversityConfig: { enabled: true, recencyPenaltyWeight: 0.3, frequencyPenaltyWeight: 0.2, recencyThresholdHours: 24 },
                defaultQualityValidationConfig: { enabled: true, failureHandlingStrategy: 'replace', maxReplacementAttempts: 3, minValidationSuccessRate: 0.8 },
                defaultFallbackConfig: { allowBestEffort: true, relaxDistributionOnShortfall: true, logFallbackReasons: true },
              },
              featureFlags: {
                allowPoolOnlyStrategy: true,
                allowAiOnlyStrategy: true,
                allowHybridStrategy: true,
                allowMixedStrategy: true,
              },
            }),
            isQuestionPoolEnabled: jest.fn().mockReturnValue(true),
            getDefaultSelectionStrategy: jest.fn().mockReturnValue('hybrid'),
            resolveWorksheetGenerationOptions: jest.fn().mockImplementation((options) => ({
              ...options,
              resolvedStrategy: 'hybrid',
              poolEnabled: true,
              strategyAllowed: true,
            })),
          },
        },
        {
          provide: WorksheetDocumentCacheService,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            delete: jest.fn(),
            clear: jest.fn(),
            generateKey: jest.fn(),
          },
        },
        {
          provide: WorksheetService,
          useValue: {
            findOne: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: DocumentsService,
          useValue: {
            findByWorksheetId: jest.fn(),
          },
        },
        {
          provide: PromptService,
          useValue: {
            generateQuestions: jest.fn(),
          },
        },
        {
          provide: SocketGateway,
          useValue: mockSocketGateway,
        },
        {
          provide: BatchImageService,
          useValue: {
            processBatchImages: jest.fn(),
          },
        },
        {
          provide: AiOrchestrationService,
          useValue: {
            generateQuestions: jest.fn(),
          },
        },
        {
          provide: Logger,
          useValue: {
            debug: jest.fn(),
            log: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
          },
        },
      ],
    }).compile();

    questionPoolService = module.get<QuestionPoolService>(QuestionPoolService);
    worksheetGenerateConsumer = module.get<WorksheetGenerateConsumer>(WorksheetGenerateConsumer);
    cacheService = module.get<QuestionPoolCacheService>(QuestionPoolCacheService);
    configService = module.get<QuestionPoolConfigService>(QuestionPoolConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('WorksheetGenerateConsumer Integration', () => {
    it('should correctly invoke QuestionPoolService through getQuestionsFromPool', async () => {
      const userRequest = {
        subject: 'Mathematics',
        grade: 'Primary 2',
        topic: 'Addition',
        exerciseTypes: ['multiple_choice'],
      };

      // Mock the QuestionPoolService method that the consumer actually calls
      const mockGetRandomQuestions = jest.spyOn(questionPoolService, 'getRandomQuestions');
      mockGetRandomQuestions.mockResolvedValue(mockQuestions as any);

      // Call the private method through reflection (for testing purposes)
      const result = await (worksheetGenerateConsumer as any).getQuestionsFromPool(userRequest, 5, 'worksheet456');

      expect(mockGetRandomQuestions).toHaveBeenCalledWith(
        expect.objectContaining({
          subject: 'Mathematics',
          grade: 'Primary 2',
        }),
        5
      );

      expect(result).toHaveLength(2);
      expect(mockSocketGateway.emitWorksheetProgress).toHaveBeenCalled();
    });

    it('should handle QuestionPoolService errors and emit appropriate error messages', async () => {
      const userRequest = {
        subject: 'Mathematics',
        grade: 'Primary 2',
        topic: 'Addition',
      };

      // Mock QuestionPoolService to throw an error
      const mockGetRandomQuestions = jest.spyOn(questionPoolService, 'getRandomQuestions');
      mockGetRandomQuestions.mockRejectedValue(new Error('Database connection failed'));

      // Call the private method and expect it to handle the error
      const result = await (worksheetGenerateConsumer as any).getQuestionsFromPool(userRequest, 10, 'worksheet456');

      expect(mockGetRandomQuestions).toHaveBeenCalled();
      expect(mockSocketGateway.emitSystemError).toHaveBeenCalled();
      expect(result).toEqual([]); // Should return empty array on failure
    });

    it('should handle insufficient questions scenario and trigger hybrid fallback', async () => {
      const userRequest = {
        subject: 'RareSubject',
        grade: 'Primary 2',
        topic: 'Advanced Topic',
        totalQuestions: 20,
      };

      // Mock QuestionPoolService to return insufficient questions
      const mockGetRandomQuestions = jest.spyOn(questionPoolService, 'getRandomQuestions');
      mockGetRandomQuestions.mockResolvedValue([mockQuestions[0]] as any); // Only 1 question available

      // Test the hybrid sourcing method
      const hybridParams = {
        worksheetId: 'worksheet456',
        resolvedOptions: { resolvedStrategy: SelectionStrategy.HYBRID },
        documentResults: {},
        userRequest,
      };

      const result = await (worksheetGenerateConsumer as any).processHybridQuestionSourcing(hybridParams);

      expect(mockGetRandomQuestions).toHaveBeenCalled();
      expect(mockSocketGateway.emitFallbackActivated).toHaveBeenCalledWith(
        'worksheet456',
        'question_pool_selection',
        'ai_generation',
        expect.stringContaining('Insufficient questions in pool')
      );
    });
  });

  describe('Caching Integration', () => {
    it('should use cached results when available', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        grade: 'Primary 2',
        count: 2,
      };

      const cachedResult: QuestionSelectionResult = {
        questions: mockQuestions,
        metadata: {
          totalRequested: 2,
          totalReturned: 2,
          distributionAchieved: { Easy: 0.5, Medium: 0.5 },
          fallbacksTriggered: [],
          validationStats: {
            totalValidated: 2,
            passed: 2,
            failed: 0,
            successRate: 1.0,
          },
          selectionTime: 50,
          cachedAt: new Date(),
        },
      };

      // Mock cache hit
      const mockGetFromCache = jest.spyOn(cacheService, 'getFromCache');
      mockGetFromCache.mockResolvedValue(cachedResult);

      const result = await questionPoolService.getRandomQuestionsWithDistribution(params);

      expect(result).toEqual(cachedResult);
      expect(mockGetFromCache).toHaveBeenCalled();
      expect(mockQuestionPoolModel.aggregate).not.toHaveBeenCalled(); // Should not hit database
    });

    it('should cache results on cache miss', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        grade: 'Primary 2',
        count: 2,
      };

      // Mock cache miss
      const mockGetFromCache = jest.spyOn(cacheService, 'getFromCache');
      mockGetFromCache.mockResolvedValue(null);

      const mockSaveToCache = jest.spyOn(cacheService, 'saveToCache');
      mockSaveToCache.mockResolvedValue(undefined);

      const result = await questionPoolService.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(2);
      expect(mockGetFromCache).toHaveBeenCalled();
      expect(mockSaveToCache).toHaveBeenCalled();
      expect(mockQuestionPoolModel.aggregate).toHaveBeenCalled(); // Should hit database
    });

    it('should handle cache errors gracefully', async () => {
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        grade: 'Primary 2',
        count: 2,
      };

      // Mock cache error
      const mockGetFromCache = jest.spyOn(cacheService, 'getFromCache');
      mockGetFromCache.mockRejectedValue(new Error('Cache service unavailable'));

      const result = await questionPoolService.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(2);
      expect(mockQuestionPoolModel.aggregate).toHaveBeenCalled(); // Should fallback to database
    });
  });

  describe('Configuration Management Integration', () => {
    it('should apply different distribution configurations', async () => {
      // Test with custom configuration
      const mockGetConfig = jest.spyOn(configService, 'getConfig');
      mockGetConfig.mockReturnValue({
        enabled: true,
        defaultSelectionStrategy: 'hybrid',
        minPoolQuestionsThreshold: 5,
        distribution: {
          defaultDifficultyDistribution: { Easy: 0.2, Medium: 0.6, Advanced: 0.2 },
          defaultQuestionTypeBalancing: { enabled: false, preferDiversity: false },
          defaultDiversityConfig: { enabled: true, recencyPenaltyWeight: 0.8, frequencyPenaltyWeight: 0.2, recencyThresholdHours: 24 },
          defaultQualityValidationConfig: { enabled: true, failureHandlingStrategy: 'replace', maxReplacementAttempts: 3, minValidationSuccessRate: 0.8 },
          defaultFallbackConfig: { allowBestEffort: true, relaxDistributionOnShortfall: true, logFallbackReasons: true },
        },
        featureFlags: {
          allowPoolOnlyStrategy: true,
          allowAiOnlyStrategy: true,
          allowHybridStrategy: true,
          allowMixedStrategy: true,
        },
      });

      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 5,
      };

      const result = await questionPoolService.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toBeDefined();
      expect(mockGetConfig).toHaveBeenCalled();
    });

    it('should respect cache configuration settings', async () => {
      // Test with caching disabled by modifying the config
      const mockGetConfig = jest.spyOn(configService, 'getConfig');
      mockGetConfig.mockReturnValue({
        enabled: true,
        defaultSelectionStrategy: 'hybrid',
        minPoolQuestionsThreshold: 10,
        distribution: {
          defaultDifficultyDistribution: { Easy: 0.3, Medium: 0.4, Advanced: 0.3 },
          defaultQuestionTypeBalancing: { enabled: true, preferDiversity: true },
          defaultDiversityConfig: { enabled: true, recencyPenaltyWeight: 0.3, frequencyPenaltyWeight: 0.2, recencyThresholdHours: 24 },
          defaultQualityValidationConfig: { enabled: true, failureHandlingStrategy: 'replace', maxReplacementAttempts: 3, minValidationSuccessRate: 0.8 },
          defaultFallbackConfig: { allowBestEffort: true, relaxDistributionOnShortfall: true, logFallbackReasons: true },
        },
        featureFlags: {
          allowPoolOnlyStrategy: true,
          allowAiOnlyStrategy: true,
          allowHybridStrategy: true,
          allowMixedStrategy: true,
        },

      });

      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 2,
      };

      const result = await questionPoolService.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(2);
      expect(mockGetConfig).toHaveBeenCalled();
    });

    it('should apply validation configuration settings', async () => {
      // Test with validation enabled
      const params: QuestionSelectionParams = {
        subject: 'Mathematics',
        count: 2,
        distributionConfig: {
          qualityValidation: {
            enabled: true,
            failureHandlingStrategy: 'discard',
            maxReplacementAttempts: 2,
            minValidationSuccessRate: 0.9,
          },
        },
      };

      const result = await questionPoolService.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toBeDefined();
      expect(result.metadata.validationStats).toBeDefined();
      expect(result.metadata.validationStats.totalValidated).toBeGreaterThanOrEqual(0);
    });
  });
});
