import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, <PERSON>String, IsArray, IsBoolean, IsObject, Min, Max, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for difficulty distribution configuration
 */
export class DifficultyDistributionDto {
  @ApiProperty({ description: 'Percentage for Easy questions (0-1)', example: 0.2, minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  Easy: number;

  @ApiProperty({ description: 'Percentage for Medium questions (0-1)', example: 0.6, minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  Medium: number;

  @ApiProperty({ description: 'Percentage for Advanced questions (0-1)', example: 0.2, minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  Advanced: number;
}

/**
 * DTO for question type balancing configuration
 */
export class QuestionTypeBalancingDto {
  @ApiProperty({ description: 'Enable question type balancing', example: true })
  @IsBoolean()
  enabled: boolean;

  @ApiProperty({ description: 'Prefer diversity in question types', example: true })
  @IsBoolean()
  preferDiversity: boolean;

  @ApiProperty({ 
    description: 'Target distribution for specific question types', 
    example: { 'multiple_choice': 0.5, 'fill_blank': 0.3, 'open_ended': 0.2 },
    required: false 
  })
  @IsObject()
  @IsOptional()
  targetDistribution?: Record<string, number>;
}

/**
 * DTO for diversity configuration
 */
export class DiversityConfigDto {
  @ApiProperty({ description: 'Enable diversity algorithms', example: true })
  @IsBoolean()
  enabled: boolean;

  @ApiProperty({ description: 'Weight for recency penalty (0-1)', example: 0.3, minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  recencyPenaltyWeight: number;

  @ApiProperty({ description: 'Weight for frequency penalty (0-1)', example: 0.2, minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  frequencyPenaltyWeight: number;

  @ApiProperty({ description: 'Hours within which questions are considered recent', example: 24, minimum: 1 })
  @IsNumber()
  @Min(1)
  recencyThresholdHours: number;
}

/**
 * DTO for quality validation configuration
 */
export class QualityValidationConfigDto {
  @ApiProperty({ description: 'Enable quality validation', example: true })
  @IsBoolean()
  enabled: boolean;

  @ApiProperty({ 
    description: 'Strategy for handling validation failures', 
    example: 'replace',
    enum: ['discard', 'replace', 'log_and_proceed']
  })
  @IsString()
  failureHandlingStrategy: 'discard' | 'replace' | 'log_and_proceed';

  @ApiProperty({ description: 'Maximum replacement attempts', example: 3, minimum: 0 })
  @IsNumber()
  @Min(0)
  maxReplacementAttempts: number;

  @ApiProperty({ description: 'Minimum validation success rate (0-1)', example: 0.8, minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  minValidationSuccessRate: number;
}

/**
 * DTO for fallback configuration
 */
export class FallbackConfigDto {
  @ApiProperty({ description: 'Allow best effort selection', example: true })
  @IsBoolean()
  allowBestEffort: boolean;

  @ApiProperty({ description: 'Relax distribution rules on shortfall', example: true })
  @IsBoolean()
  relaxDistributionOnShortfall: boolean;

  @ApiProperty({ description: 'Log fallback reasons', example: true })
  @IsBoolean()
  logFallbackReasons: boolean;
}

/**
 * DTO for complete distribution configuration
 */
export class QuestionDistributionConfigDto {
  @ApiProperty({ description: 'Difficulty distribution configuration' })
  @ValidateNested()
  @Type(() => DifficultyDistributionDto)
  difficultyDistribution: DifficultyDistributionDto;

  @ApiProperty({ description: 'Question type balancing configuration', required: false })
  @ValidateNested()
  @Type(() => QuestionTypeBalancingDto)
  @IsOptional()
  questionTypeBalancing?: QuestionTypeBalancingDto;

  @ApiProperty({ description: 'Diversity configuration', required: false })
  @ValidateNested()
  @Type(() => DiversityConfigDto)
  @IsOptional()
  diversity?: DiversityConfigDto;

  @ApiProperty({ description: 'Quality validation configuration', required: false })
  @ValidateNested()
  @Type(() => QualityValidationConfigDto)
  @IsOptional()
  qualityValidation?: QualityValidationConfigDto;

  @ApiProperty({ description: 'Fallback configuration', required: false })
  @ValidateNested()
  @Type(() => FallbackConfigDto)
  @IsOptional()
  fallback?: FallbackConfigDto;
}

/**
 * DTO for question selection parameters
 */
export class QuestionSelectionParamsDto {
  @ApiProperty({ description: 'Subject filter', required: false })
  @IsString()
  @IsOptional()
  subject?: string;

  @ApiProperty({ description: 'Parent subject filter', required: false })
  @IsString()
  @IsOptional()
  parentSubject?: string;

  @ApiProperty({ description: 'Child subject filter', required: false })
  @IsString()
  @IsOptional()
  childSubject?: string;

  @ApiProperty({ description: 'Question type filter', required: false })
  @IsOptional()
  type?: string | string[];

  @ApiProperty({ description: 'Status filter', required: false, default: 'active' })
  @IsString()
  @IsOptional()
  status?: string;

  @ApiProperty({ description: 'Grade filter', required: false })
  @IsString()
  @IsOptional()
  grade?: string;

  @ApiProperty({ description: 'Language filter', required: false })
  @IsString()
  @IsOptional()
  language?: string;

  @ApiProperty({ description: 'Number of questions to select', example: 10, minimum: 1 })
  @IsNumber()
  @Min(1)
  count: number;

  @ApiProperty({ description: 'Distribution configuration', required: false })
  @ValidateNested()
  @Type(() => QuestionDistributionConfigDto)
  @IsOptional()
  distributionConfig?: QuestionDistributionConfigDto;

  @ApiProperty({ description: 'Skip distribution enforcement', required: false, default: false })
  @IsBoolean()
  @IsOptional()
  skipDistribution?: boolean;

  @ApiProperty({ description: 'Skip diversity algorithms', required: false, default: false })
  @IsBoolean()
  @IsOptional()
  skipDiversity?: boolean;

  @ApiProperty({ description: 'Skip quality validation', required: false, default: false })
  @IsBoolean()
  @IsOptional()
  skipValidation?: boolean;
}
