import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIndependentTeacherRole1748500000000 implements MigrationInterface {
  name = 'AddIndependentTeacherRole1748500000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add the new INDEPENDENT_TEACHER role to the existing enum
    await queryRunner.query(
      `ALTER TYPE "public"."users_role_enum" ADD VALUE 'independent_teacher'`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Note: PostgreSQL doesn't support removing enum values directly
    // We need to recreate the enum without the independent_teacher value
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "role"`);
    await queryRunner.query(`DROP TYPE "public"."users_role_enum"`);
    await queryRunner.query(
      `CREATE TYPE "public"."users_role_enum" AS ENUM('teacher', 'admin', 'student', 'school_manager')`
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD "role" "public"."users_role_enum" DEFAULT 'teacher'`
    );
    await queryRunner.query(
      `UPDATE "users" SET "role" = 'teacher' WHERE "role" IS NULL`
    );
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "role" SET NOT NULL`
    );
  }
}
