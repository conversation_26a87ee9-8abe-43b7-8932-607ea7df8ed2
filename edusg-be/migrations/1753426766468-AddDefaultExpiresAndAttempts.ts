import { MigrationInterface, QueryRunner } from "typeorm";

export class AddDefaultExpiresAndAttempts1753426766468 implements MigrationInterface {
    name = 'AddDefaultExpiresAndAttempts1753426766468'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "invitations" DROP CONSTRAINT "FK_invitations_userId"`);
        await queryRunner.query(`ALTER TABLE "exams" DROP COLUMN "maxAttempts"`);
        await queryRunner.query(`ALTER TABLE "exams" ADD "defaultExpiresInDays" integer`);
        await queryRunner.query(`ALTER TABLE "exams" ADD "defaultTotalAttempts" integer NOT NULL DEFAULT '1'`);
        await queryRunner.query(`COMMENT ON COLUMN "exam_results"."startedAt" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "exam_assignments"."expiresAt" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "exam_assignments"."totalAttempts" IS NULL`);
        await queryRunner.query(`ALTER TABLE "invitations" ADD CONSTRAINT "FK_c16fdad3aea7ec7b047aedb9afe" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "invitations" DROP CONSTRAINT "FK_c16fdad3aea7ec7b047aedb9afe"`);
        await queryRunner.query(`COMMENT ON COLUMN "exam_assignments"."totalAttempts" IS 'The total number of attempts allowed for this assignment'`);
        await queryRunner.query(`COMMENT ON COLUMN "exam_assignments"."expiresAt" IS 'The date and time when the exam assignment expires'`);
        await queryRunner.query(`COMMENT ON COLUMN "exam_results"."startedAt" IS 'The timestamp when the student started this specific exam attempt'`);
        await queryRunner.query(`ALTER TABLE "exams" DROP COLUMN "defaultTotalAttempts"`);
        await queryRunner.query(`ALTER TABLE "exams" DROP COLUMN "defaultExpiresInDays"`);
        await queryRunner.query(`ALTER TABLE "exams" ADD "maxAttempts" integer NOT NULL DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE "invitations" ADD CONSTRAINT "FK_invitations_userId" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

}
