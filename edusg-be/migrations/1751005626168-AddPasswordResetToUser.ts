import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPasswordResetToUser1751005626168 implements MigrationInterface {
    name = 'AddPasswordResetToUser1751005626168'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add password reset fields to users table
        await queryRunner.query(`ALTER TABLE "users" ADD "passwordResetToken" character varying`);
        await queryRunner.query(`ALTER TABLE "users" ADD "passwordResetExpires" TIMESTAMP WITH TIME ZONE`);

        // Add index for password reset token lookups
        await queryRunner.query(`CREATE INDEX "IDX_USER_PASSWORD_RESET_TOKEN" ON "users" ("passwordResetToken") WHERE "passwordResetToken" IS NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove password reset index
        await queryRunner.query(`DROP INDEX "public"."IDX_USER_PASSWORD_RESET_TOKEN"`);

        // Remove password reset fields from users table
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "passwordResetExpires"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "passwordResetToken"`);
    }

}
