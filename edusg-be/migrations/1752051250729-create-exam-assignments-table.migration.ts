import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateExamAssignmentsTable1752051250729 implements MigrationInterface {
  name = 'CreateExamAssignmentsTable1752051250729';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."exam_assignments_status_enum" AS ENUM('assigned', 'in_progress', 'completed')`
    );
    await queryRunner.query(
      `CREATE TABLE "exam_assignments" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "examId" uuid NOT NULL, "studentId" uuid NOT NULL, "status" "public"."exam_assignments_status_enum" NOT NULL DEFAULT 'assigned', "assignedAt" TIMESTAMP NOT NULL DEFAULT now(), "startedAt" TIMESTAMP, "completedAt" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_exam_assignments_exam_student" UNIQUE ("examId", "studentId"), CONSTRAINT "PK_b298beac3e3b5b8b1f0a7b4e7ab" PRIMARY KEY ("id"))`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "exam_assignments"`);
    await queryRunner.query(`DROP TYPE "public"."exam_assignments_status_enum"`);
  }
}
