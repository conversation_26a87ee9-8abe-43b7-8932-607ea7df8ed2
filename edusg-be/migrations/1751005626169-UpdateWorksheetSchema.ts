import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateWorksheetSchema1751005626169 implements MigrationInterface {
    name = 'UpdateWorksheetSchema1751005626169'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Drop old worksheet indexes
        await queryRunner.query(`DROP INDEX "public"."IDX_WORKSHEET_SCHOOL_UPDATED"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_WORKSHEET_SCHOOL_TOTAL_QUESTIONS"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_WORKSHEET_LAST_MODIFIED"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_WORKSHEET_WITH_QUESTIONS"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_WORKSHEET_QUESTIONS_GIN"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_WORKSHEET_QUESTION_IDS_GIN"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_WORKSHEET_QUESTION_METADATA_GIN"`);
        
        // Drop old worksheet constraints
        await queryRunner.query(`ALTER TABLE "worksheets" DROP CONSTRAINT "CHK_WORKSHEET_TOTAL_QUESTIONS_NON_NEGATIVE"`);
        await queryRunner.query(`ALTER TABLE "worksheets" DROP CONSTRAINT "CHK_WORKSHEET_MAX_QUESTIONS_POSITIVE"`);
        await queryRunner.query(`ALTER TABLE "worksheets" DROP CONSTRAINT "CHK_WORKSHEET_TOTAL_LE_MAX_QUESTIONS"`);
        await queryRunner.query(`ALTER TABLE "worksheets" DROP CONSTRAINT "CHK_WORKSHEET_QUESTIONS_VALID"`);
        
        // Update schools table constraints - first handle null values, then make columns NOT NULL
        await queryRunner.query(`UPDATE "schools" SET "address" = '' WHERE "address" IS NULL`);
        await queryRunner.query(`UPDATE "schools" SET "phoneNumber" = '' WHERE "phoneNumber" IS NULL`);
        await queryRunner.query(`ALTER TABLE "schools" ALTER COLUMN "address" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "schools" ALTER COLUMN "phoneNumber" SET NOT NULL`);
        
        // Remove comments from worksheet columns
        await queryRunner.query(`COMMENT ON COLUMN "worksheets"."questions" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "worksheets"."totalQuestions" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "worksheets"."lastModifiedBy" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "worksheets"."createdBy" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "worksheets"."maxQuestions" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "worksheets"."questionIds" IS NULL`);
        await queryRunner.query(`COMMENT ON COLUMN "worksheets"."questionMetadata" IS NULL`);
        
        // Create new optimized indexes
        await queryRunner.query(`CREATE INDEX "IDX_ab18efaf4c83ec1a77968f85de" ON "worksheets" ("lastModifiedBy", "updatedAt") `);
        await queryRunner.query(`CREATE INDEX "IDX_da5693b91ef5da9586e7557610" ON "worksheets" ("schoolId", "totalQuestions") `);
        await queryRunner.query(`CREATE INDEX "IDX_7213a8765398180ad97ca939df" ON "worksheets" ("schoolId", "updatedAt") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop new indexes
        await queryRunner.query(`DROP INDEX "public"."IDX_7213a8765398180ad97ca939df"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_da5693b91ef5da9586e7557610"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ab18efaf4c83ec1a77968f85de"`);
        
        // Restore comments on worksheet columns
        await queryRunner.query(`COMMENT ON COLUMN "worksheets"."questionMetadata" IS 'Question management metadata including version, lock status, collaborators'`);
        await queryRunner.query(`COMMENT ON COLUMN "worksheets"."questionIds" IS 'Array of MongoDB ObjectIds as strings (references to WorksheetQuestionDocument)'`);
        await queryRunner.query(`COMMENT ON COLUMN "worksheets"."maxQuestions" IS 'Maximum number of questions allowed in the worksheet'`);
        await queryRunner.query(`COMMENT ON COLUMN "worksheets"."createdBy" IS 'User ID who created the worksheet'`);
        await queryRunner.query(`COMMENT ON COLUMN "worksheets"."lastModifiedBy" IS 'User ID who last modified questions'`);
        await queryRunner.query(`COMMENT ON COLUMN "worksheets"."totalQuestions" IS 'Total number of questions in the worksheet'`);
        await queryRunner.query(`COMMENT ON COLUMN "worksheets"."questions" IS 'Array of exercise questions (JSONB format)'`);
        
        // Revert schools table constraints
        await queryRunner.query(`ALTER TABLE "schools" ALTER COLUMN "phoneNumber" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "schools" ALTER COLUMN "address" DROP NOT NULL`);
        
        // Restore worksheet constraints
        await queryRunner.query(`ALTER TABLE "worksheets" ADD CONSTRAINT "CHK_WORKSHEET_QUESTIONS_VALID" CHECK (((questions IS NULL) OR validate_worksheet_questions(questions)))`);
        await queryRunner.query(`ALTER TABLE "worksheets" ADD CONSTRAINT "CHK_WORKSHEET_TOTAL_LE_MAX_QUESTIONS" CHECK (("totalQuestions" <= "maxQuestions"))`);
        await queryRunner.query(`ALTER TABLE "worksheets" ADD CONSTRAINT "CHK_WORKSHEET_MAX_QUESTIONS_POSITIVE" CHECK (("maxQuestions" > 0))`);
        await queryRunner.query(`ALTER TABLE "worksheets" ADD CONSTRAINT "CHK_WORKSHEET_TOTAL_QUESTIONS_NON_NEGATIVE" CHECK (("totalQuestions" >= 0))`);
        
        // Restore old indexes
        await queryRunner.query(`CREATE INDEX "IDX_WORKSHEET_QUESTION_METADATA_GIN" ON "worksheets" ("questionMetadata") `);
        await queryRunner.query(`CREATE INDEX "IDX_WORKSHEET_QUESTION_IDS_GIN" ON "worksheets" ("questionIds") `);
        await queryRunner.query(`CREATE INDEX "IDX_WORKSHEET_QUESTIONS_GIN" ON "worksheets" ("questions") `);
        await queryRunner.query(`CREATE INDEX "IDX_WORKSHEET_WITH_QUESTIONS" ON "worksheets" ("totalQuestions") WHERE ("totalQuestions" > 0)`);
        await queryRunner.query(`CREATE INDEX "IDX_WORKSHEET_LAST_MODIFIED" ON "worksheets" ("updatedAt", "lastModifiedBy") `);
        await queryRunner.query(`CREATE INDEX "IDX_WORKSHEET_SCHOOL_TOTAL_QUESTIONS" ON "worksheets" ("schoolId", "totalQuestions") `);
        await queryRunner.query(`CREATE INDEX "IDX_WORKSHEET_SCHOOL_UPDATED" ON "worksheets" ("updatedAt", "schoolId") `);
    }

}
