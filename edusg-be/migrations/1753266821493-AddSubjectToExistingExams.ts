import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSubjectToExistingExams1753266821493 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Get all exams that don't have a proper topic in selectedOptions
        const examsWithoutSubject = await queryRunner.query(`
            SELECT id, "selectedOptions" 
            FROM exams 
            WHERE "selectedOptions" IS NULL 
            OR NOT EXISTS (
                SELECT 1 
                FROM jsonb_array_elements("selectedOptions") AS option 
                WHERE option->>'key' = 'topic'
            )
        `);

        console.log(`Found ${examsWithoutSubject.length} exams without proper subject information`);

        for (const exam of examsWithoutSubject) {
            // Set default subject to 'Mathematics' for existing exams
            const defaultSelectedOptions = [
                { key: 'topic', value: 'mathematics' },
                { key: 'level', value: 'medium' },
                { key: 'passingScore', value: '50' }
            ];

            // If exam already has selectedOptions, merge with default topic
            let updatedOptions = defaultSelectedOptions;
            if (exam.selectedOptions && Array.isArray(exam.selectedOptions)) {
                // Check if topic exists
                const hasTopicOption = exam.selectedOptions.some((option: any) => option.key === 'topic');
                
                if (!hasTopicOption) {
                    // Add default topic to existing options
                    updatedOptions = [
                        { key: 'topic', value: 'mathematics' },
                        ...exam.selectedOptions
                    ];
                } else {
                    // Topic exists, keep as is
                    updatedOptions = exam.selectedOptions;
                }
            }

            await queryRunner.query(`
                UPDATE exams 
                SET "selectedOptions" = $1 
                WHERE id = $2
            `, [JSON.stringify(updatedOptions), exam.id]);
        }

        console.log(`Updated ${examsWithoutSubject.length} exams with default Mathematics subject`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // This migration adds default data, so reverting would be complex
        // and potentially destructive. We'll leave this empty as the change
        // is safe and doesn't alter schema structure.
        console.log('Down migration: No action needed for AddSubjectToExistingExams');
    }

}
