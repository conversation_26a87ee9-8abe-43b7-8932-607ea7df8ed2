import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTimestampsAndUniqueConstraintToExamAssignments1752051250730 implements MigrationInterface {
  name = 'AddTimestampsAndUniqueConstraintToExamAssignments1752051250730';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Modify existing timestamp columns to use TIMESTAMP WITH TIME ZONE
    await queryRunner.query(`
      ALTER TABLE "exam_assignments"
      ALTER COLUMN "assignedAt" TYPE TIMESTAMP WITH TIME ZONE
    `);

    await queryRunner.query(`
      ALTER TABLE "exam_assignments"
      ALTER COLUMN "startedAt" TYPE TIMESTAMP WITH TIME ZONE
    `);

    await queryRunner.query(`
      ALTER TABLE "exam_assignments"
      ALTER COLUMN "completedAt" TYPE TIMESTAMP WITH TIME ZONE
    `);

    // Rename existing unique constraint to match entity definition
    await queryRunner.query(`
      ALTER TABLE "exam_assignments"
      RENAME CONSTRAINT "UQ_exam_assignments_exam_student"
      TO "UQ_exam_assignments_examId_studentId"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Rename constraint back to original name
    await queryRunner.query(`
      ALTER TABLE "exam_assignments"
      RENAME CONSTRAINT "UQ_exam_assignments_examId_studentId"
      TO "UQ_exam_assignments_exam_student"
    `);

    // Revert timestamp columns back to TIMESTAMP (without time zone)
    await queryRunner.query(`
      ALTER TABLE "exam_assignments"
      ALTER COLUMN "completedAt" TYPE TIMESTAMP
    `);

    await queryRunner.query(`
      ALTER TABLE "exam_assignments"
      ALTER COLUMN "startedAt" TYPE TIMESTAMP
    `);

    await queryRunner.query(`
      ALTER TABLE "exam_assignments"
      ALTER COLUMN "assignedAt" TYPE TIMESTAMP
    `);
  }
}
