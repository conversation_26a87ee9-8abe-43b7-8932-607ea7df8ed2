import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPermissionsToPackage1751601504980 implements MigrationInterface {
    name = 'AddPermissionsToPackage1751601504980'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add permissions column to package table
        await queryRunner.query(`
            ALTER TABLE "package" 
            ADD COLUMN "permissions" jsonb
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove permissions column from package table
        await queryRunner.query(`
            ALTER TABLE "package" 
            DROP COLUMN IF EXISTS "permissions"
        `);
    }
}
