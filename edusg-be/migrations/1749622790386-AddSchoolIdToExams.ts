import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSchoolIdToExams1749622790386 implements MigrationInterface {
    name = 'AddSchoolIdToExams1749622790386'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "narrative_structures" DROP CONSTRAINT "FK_narrative_structures_school"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_narrative_structures_school_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_narrative_structures_extracted_at"`);
        await queryRunner.query(`ALTER TABLE "exams" ADD "schoolId" uuid`);
        await queryRunner.query(`ALTER TABLE "narrative_structures" DROP CONSTRAINT "UQ_narrative_structures_school"`);
        await queryRunner.query(`ALTER TABLE "narrative_structures" ALTER COLUMN "extractedAt" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "exams" ADD CONSTRAINT "FK_82b8fb9529a50eef647d1fe321d" FOREIGN KEY ("schoolId") REFERENCES "schools"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "narrative_structures" ADD CONSTRAINT "FK_430cc8e882f0307fc2d9f0a15da" FOREIGN KEY ("schoolId") REFERENCES "schools"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "narrative_structures" DROP CONSTRAINT "FK_430cc8e882f0307fc2d9f0a15da"`);
        await queryRunner.query(`ALTER TABLE "exams" DROP CONSTRAINT "FK_82b8fb9529a50eef647d1fe321d"`);
        await queryRunner.query(`ALTER TABLE "narrative_structures" ALTER COLUMN "extractedAt" SET DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "narrative_structures" ADD CONSTRAINT "UQ_narrative_structures_school" UNIQUE ("schoolId")`);
        await queryRunner.query(`ALTER TABLE "exams" DROP COLUMN "schoolId"`);
        await queryRunner.query(`CREATE INDEX "IDX_narrative_structures_extracted_at" ON "narrative_structures" ("extractedAt") `);
        await queryRunner.query(`CREATE INDEX "IDX_narrative_structures_school_id" ON "narrative_structures" ("schoolId") `);
        await queryRunner.query(`ALTER TABLE "narrative_structures" ADD CONSTRAINT "FK_narrative_structures_school" FOREIGN KEY ("schoolId") REFERENCES "schools"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

}
