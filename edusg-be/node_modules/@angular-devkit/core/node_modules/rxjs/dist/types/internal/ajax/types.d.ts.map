{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../../src/internal/ajax/types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,UAAU,CAAC;AAE3C;;;;GAIG;AACH,oBAAY,aAAa,GAAG,QAAQ,GAAG,UAAU,CAAC;AAElD,oBAAY,iBAAiB,GAAG,WAAW,GAAG,UAAU,GAAG,MAAM,CAAC;AAElE,oBAAY,gBAAgB,GAAG,GAAG,aAAa,IAAI,iBAAiB,EAAE,CAAC;AAEvE;;;;;GAKG;AACH,MAAM,WAAW,WAAW;IAC1B;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;IAEZ;;OAEG;IACH,IAAI,CAAC,EAAE,GAAG,CAAC;IAEX;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,KAAK,EAAE,OAAO,CAAC;IAEf;;OAEG;IACH,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;IAEvC;;;OAGG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,WAAW,EAAE,OAAO,CAAC;IAErB;;;OAGG;IACH,eAAe,EAAE,OAAO,CAAC;IAEzB;;OAEG;IACH,YAAY,EAAE,0BAA0B,CAAC;CAC1C;AAED;;GAEG;AACH,MAAM,WAAW,UAAU;IACzB,uDAAuD;IACvD,GAAG,EAAE,MAAM,CAAC;IAEZ;;;;;;;;OAQG;IACH,IAAI,CAAC,EAAE,GAAG,CAAC;IAEX;;;OAGG;IACH,KAAK,CAAC,EAAE,OAAO,CAAC;IAEhB;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAEhB;;;;;;;;;;OAUG;IACH,OAAO,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;IAExC;;;OAGG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB,mEAAmE;IACnE,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd,iEAAiE;IACjE,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;;;;;;;;;;OAWG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IAEtB;;;;;OAKG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAE1B;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;;;;;OAOG;IACH,YAAY,CAAC,EAAE,0BAA0B,CAAC;IAE1C;;;;;;;;;;OAUG;IACH,SAAS,CAAC,EAAE,MAAM,cAAc,CAAC;IAEjC;;;;;;;;;;OAUG;IACH,kBAAkB,CAAC,EAAE,eAAe,CAAC,aAAa,CAAC,CAAC;IAEpD;;;;;;OAMG;IACH,uBAAuB,CAAC,EAAE,OAAO,CAAC;IAElC;;;;;;OAMG;IACH,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAEhC;;;;;;;;;;OAUG;IACH,WAAW,CAAC,EACR,MAAM,GACN,eAAe,GACf,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,OAAO,EAAE,CAAC,GAC3E,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC7E"}