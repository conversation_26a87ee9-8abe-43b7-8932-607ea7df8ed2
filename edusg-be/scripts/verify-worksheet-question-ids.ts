#!/usr/bin/env ts-node

import * as mongoose from 'mongoose';
import { DataSource } from 'typeorm';

// Load environment variables
try {
  require('dotenv').config();
} catch (e) {
  // dotenv not available, use process.env directly
}

interface Logger {
  log: (message: string) => void;
  warn: (message: string) => void;
  error: (message: string, stack?: string) => void;
  debug: (message: string) => void;
}

const logger: Logger = {
  log: (message: string) => console.log(`[WorksheetVerification] ${message}`),
  warn: (message: string) => console.warn(`[WorksheetVerification] ${message}`),
  error: (message: string, stack?: string) => {
    console.error(`[WorksheetVerification] ${message}`);
    if (stack) console.error(stack);
  },
  debug: (message: string) => console.log(`[WorksheetVerification] ${message}`)
};

// MongoDB Schema
const WorksheetPromptResultSchema = new mongoose.Schema({
  worksheetId: { type: String, required: true },
  promptResult: { type: Object, required: true },
  currentQuestionCount: { type: Number, default: 0 },
  totalQuestionCount: { type: Number, required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, { timestamps: true });

class WorksheetVerification {
  private mongoConnection: mongoose.Connection;
  private pgDataSource: DataSource;
  private WorksheetPromptResultModel: mongoose.Model<any>;

  async initialize(): Promise<void> {
    logger.log('Initializing database connections...');

    // Initialize MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/edusg';
    await mongoose.connect(mongoUri);
    this.mongoConnection = mongoose.connection;
    this.WorksheetPromptResultModel = mongoose.model('WorksheetPromptResult', WorksheetPromptResultSchema);
    logger.log('✅ MongoDB connected');

    // Initialize PostgreSQL
    this.pgDataSource = new DataSource({
      type: 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      username: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'password',
      database: process.env.DB_NAME || 'edusg',
      synchronize: false,
      logging: false,
    });

    await this.pgDataSource.initialize();
    logger.log('✅ PostgreSQL connected');
  }

  async cleanup(): Promise<void> {
    if (this.mongoConnection) {
      await mongoose.disconnect();
      logger.log('MongoDB disconnected');
    }
    if (this.pgDataSource && this.pgDataSource.isInitialized) {
      await this.pgDataSource.destroy();
      logger.log('PostgreSQL disconnected');
    }
  }

  async verifyData(): Promise<void> {
    logger.log('Starting verification of worksheet question IDs...');

    try {
      // Get a few worksheets from MongoDB
      const promptResults = await this.WorksheetPromptResultModel.find({
        'promptResult.result': { $exists: true, $ne: [] }
      }).limit(5).exec();

      logger.log(`Found ${promptResults.length} worksheet prompt results to verify`);

      for (const promptResult of promptResults) {
        const worksheetId = promptResult.worksheetId;
        const questions = promptResult.promptResult?.result || [];

        logger.log(`\n=== Verifying Worksheet ${worksheetId} ===`);
        
        // Check MongoDB data
        const hasIds = questions.every(q => q && q.id);
        const questionIds = questions.filter(q => q && q.id).map(q => q.id);
        
        logger.log(`MongoDB - Questions: ${questions.length}, Has IDs: ${hasIds}`);
        if (questionIds.length > 0) {
          logger.log(`MongoDB - First few question IDs: ${questionIds.slice(0, 3).join(', ')}`);
        }

        // Check PostgreSQL data
        const pgResult = await this.pgDataSource.query(
          'SELECT id, "questionIds", "questionMetadata", "maxQuestions" FROM worksheets WHERE id = $1',
          [worksheetId]
        );

        if (pgResult.length === 0) {
          logger.warn(`PostgreSQL - Worksheet ${worksheetId} not found`);
        } else {
          const worksheet = pgResult[0];
          logger.log(`PostgreSQL - questionIds: ${JSON.stringify(worksheet.questionIds)}`);
          logger.log(`PostgreSQL - questionMetadata: ${JSON.stringify(worksheet.questionMetadata)}`);
          logger.log(`PostgreSQL - maxQuestions: ${worksheet.maxQuestions}`);
          
          if (worksheet.questionIds) {
            const pgQuestionIds = Array.isArray(worksheet.questionIds) ? worksheet.questionIds : JSON.parse(worksheet.questionIds);
            logger.log(`PostgreSQL - Question IDs count: ${pgQuestionIds.length}`);
            
            // Compare with MongoDB
            if (pgQuestionIds.length === questionIds.length) {
              logger.log(`✅ Question count matches between MongoDB and PostgreSQL`);
            } else {
              logger.warn(`❌ Question count mismatch: MongoDB=${questionIds.length}, PostgreSQL=${pgQuestionIds.length}`);
            }
          } else {
            logger.warn(`❌ PostgreSQL questionIds is null/empty`);
          }
        }
      }

      // Check overall statistics
      logger.log('\n=== Overall Statistics ===');
      
      const totalMongo = await this.WorksheetPromptResultModel.countDocuments({
        'promptResult.result': { $exists: true, $ne: [] }
      });
      
      const totalPgWithQuestionIds = await this.pgDataSource.query(
        'SELECT COUNT(*) as count FROM worksheets WHERE "questionIds" IS NOT NULL'
      );
      
      const totalPgWorksheets = await this.pgDataSource.query(
        'SELECT COUNT(*) as count FROM worksheets'
      );

      logger.log(`MongoDB worksheets with questions: ${totalMongo}`);
      logger.log(`PostgreSQL total worksheets: ${totalPgWorksheets[0].count}`);
      logger.log(`PostgreSQL worksheets with questionIds: ${totalPgWithQuestionIds[0].count}`);

    } catch (error) {
      logger.error(`Verification failed: ${error.message}`, error.stack);
      throw error;
    }
  }
}

async function bootstrap() {
  const verification = new WorksheetVerification();
  
  try {
    await verification.initialize();
    await verification.verifyData();
    await verification.cleanup();
    logger.log('Verification completed successfully');
    process.exit(0);
    
  } catch (error) {
    logger.error(`Verification failed: ${error.message}`, error.stack);
    await verification.cleanup();
    process.exit(1);
  }
}

bootstrap();
